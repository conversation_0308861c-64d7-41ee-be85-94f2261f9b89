{
    "compilerOptions": {
        "lib": ["es5", "es6", "es7", "es2017", "dom", "ESNext"],
        "target": "ESNext",
        "allowJs": true,
        "jsx": "react-jsx",
        "moduleResolution": "node",
        "module": "ESNext",
        "declaration": true,
        "emitDeclarationOnly": true,
        "outDir": "./esm",
        "baseUrl": "./",
        "strict": true /* Enable all strict type-checking options. */,
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
        "allowSyntheticDefaultImports": true,
        "preserveWatchOutput": true,
        "resolveJsonModule": true,
        "paths": {
            "mo/*": ["./src/*"]
        }
    },
    "include": ["src", "typing.d.ts"]
}
