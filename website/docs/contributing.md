# Contributing

## Development

Start to development

```bash
yarn # install dependencies

yarn dev
```

Running a web preview version:

```bash
yarn build
yarn web
```

## Naming

Unify the Service methods basic prefix naming:

-   add, prefix for Add sth.
-   remove, prefix for Remove sth.
-   update, prefix for Update sth.
-   get, prefix for Get sth.
-   set, prefix for Set sth.
-   create, perfix for create sth.
-   on, prefix for listen to the event.
-   find, prefix for Find sth.
-   move, prefix for Move sth.
-   append, prefix for Append sth.
-   toggle, prefix for Toggle sth.

## Git Work Flow

[Branch-based Workflow](https://guides.github.com/introduction/flow/)

### Reference

-   ARIA: [Accessible Rich Internet Applications](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA)
-   [VS Code Extension](https://code.visualstudio.com/api/get-started/your-first-extension)
    <https://code.visualstudio.com/api>
    <https://medium.com/dev-genius/reactjs-manage-your-state-nicely-with-context-1ed3090a6a46>
