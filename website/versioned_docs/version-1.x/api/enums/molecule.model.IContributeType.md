---
id: 'molecule.model.IContributeType'
title: 'Enumeration: IContributeType'
sidebar_label: 'IContributeType'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IContributeType

## Enumeration Members

### Commands

• **Commands**

#### Defined in

[model/extension.ts:21](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L21)

---

### Configuration

• **Configuration**

#### Defined in

[model/extension.ts:22](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L22)

---

### Grammar

• **Grammar**

#### Defined in

[model/extension.ts:23](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L23)

---

### IconTheme

• **IconTheme**

#### Defined in

[model/extension.ts:25](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L25)

---

### Languages

• **Languages**

#### Defined in

[model/extension.ts:20](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L20)

---

### Themes

• **Themes**

#### Defined in

[model/extension.ts:24](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L24)
