---
id: 'molecule.model.EditorTreeEvent'
title: 'Enumeration: EditorTreeEvent'
sidebar_label: 'EditorTreeEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).EditorTreeEvent

## Enumeration Members

### onClose

• **onClose**

#### Defined in

[model/workbench/explorer/editorTree.ts:2](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L2)

---

### onCloseAll

• **onCloseAll**

#### Defined in

[model/workbench/explorer/editorTree.ts:6](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L6)

---

### onCloseOthers

• **onCloseOthers**

#### Defined in

[model/workbench/explorer/editorTree.ts:4](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L4)

---

### onCloseSaved

• **onCloseSaved**

#### Defined in

[model/workbench/explorer/editorTree.ts:5](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L5)

---

### onContextMenu

• **onContextMenu**

#### Defined in

[model/workbench/explorer/editorTree.ts:10](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L10)

---

### onSaveAll

• **onSaveAll**

#### Defined in

[model/workbench/explorer/editorTree.ts:7](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L7)

---

### onSelect

• **onSelect**

#### Defined in

[model/workbench/explorer/editorTree.ts:3](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L3)

---

### onSplitEditorLayout

• **onSplitEditorLayout**

#### Defined in

[model/workbench/explorer/editorTree.ts:8](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L8)

---

### onToolbarClick

• **onToolbarClick**

#### Defined in

[model/workbench/explorer/editorTree.ts:9](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/editorTree.ts#L9)
