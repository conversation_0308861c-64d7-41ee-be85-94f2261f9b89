---
id: 'molecule.model.ActivityBarEvent'
title: 'Enumeration: ActivityBarEvent'
sidebar_label: 'ActivityBarEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ActivityBarEvent

The activity bar event definition

## Enumeration Members

### DataChanged

• **DataChanged**

Activity bar data changed

#### Defined in

[model/workbench/activityBar.ts:13](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L13)

---

### OnChange

• **OnChange**

#### Defined in

[model/workbench/activityBar.ts:9](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L9)

---

### OnClick

• **OnClick**

#### Defined in

[model/workbench/activityBar.ts:8](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L8)

---

### ReRender

• **ReRender**

#### Defined in

[model/workbench/activityBar.ts:14](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L14)
