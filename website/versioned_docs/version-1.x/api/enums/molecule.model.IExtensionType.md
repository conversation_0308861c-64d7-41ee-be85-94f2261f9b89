---
id: 'molecule.model.IExtensionType'
title: 'Enumeration: IExtensionType'
sidebar_label: 'IExtensionType'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IExtensionType

Defines extension types

## Enumeration Members

### Locals

• **Locals**

#### Defined in

[model/extension.ts:14](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L14)

---

### Menus

• **Menus**

#### Defined in

[model/extension.ts:15](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L15)

---

### Normal

• **Normal**

#### Defined in

[model/extension.ts:12](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L12)

---

### Settings

• **Settings**

#### Defined in

[model/extension.ts:13](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L13)

---

### Theme

• **Theme**

#### Defined in

[model/extension.ts:11](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L11)

---

### Workbench

• **Workbench**

#### Defined in

[model/extension.ts:16](https://github.com/DTStack/molecule/blob/927b7d39/src/model/extension.ts#L16)
