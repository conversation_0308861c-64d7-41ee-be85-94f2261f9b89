---
id: 'molecule.model.ExplorerEvent'
title: 'Enumeration: ExplorerEvent'
sidebar_label: 'ExplorerEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ExplorerEvent

## Enumeration Members

### onClick

• **onClick**

#### Defined in

[model/workbench/explorer/explorer.tsx:6](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L6)

---

### onCollapseAllFolders

• **onCollapseAllFolders**

#### Defined in

[model/workbench/explorer/explorer.tsx:10](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L10)

---

### onCollapseChange

• **onCollapseChange**

#### Defined in

[model/workbench/explorer/explorer.tsx:8](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L8)

---

### onPanelToolbarClick

• **onPanelToolbarClick**

#### Defined in

[model/workbench/explorer/explorer.tsx:7](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L7)

---

### onRemovePanel

• **onRemovePanel**

#### Defined in

[model/workbench/explorer/explorer.tsx:9](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L9)
