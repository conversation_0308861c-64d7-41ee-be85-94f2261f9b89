---
id: 'molecule.model.EditorEvent'
title: 'Enumeration: EditorEvent'
sidebar_label: 'EditorEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).EditorEvent

## Enumeration Members

### OnCloseAll

• **OnCloseAll**

#### Defined in

[model/workbench/editor.ts:10](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L10)

---

### OnCloseOther

• **OnCloseOther**

#### Defined in

[model/workbench/editor.ts:11](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L11)

---

### OnCloseTab

• **OnCloseTab**

#### Defined in

[model/workbench/editor.ts:9](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L9)

---

### OnCloseToLeft

• **OnCloseToLeft**

#### Defined in

[model/workbench/editor.ts:12](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L12)

---

### OnCloseToRight

• **OnCloseToRight**

#### Defined in

[model/workbench/editor.ts:13](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L13)

---

### OnMoveTab

• **OnMoveTab**

#### Defined in

[model/workbench/editor.ts:14](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L14)

---

### OnSelectTab

• **OnSelectTab**

#### Defined in

[model/workbench/editor.ts:16](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L16)

---

### OnSplitEditorRight

• **OnSplitEditorRight**

#### Defined in

[model/workbench/editor.ts:19](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L19)

---

### OnUpdateTab

• **OnUpdateTab**

#### Defined in

[model/workbench/editor.ts:17](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L17)

---

### OpenTab

• **OpenTab**

#### Defined in

[model/workbench/editor.ts:15](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L15)

---

### onActionsClick

• **onActionsClick**

#### Defined in

[model/workbench/editor.ts:18](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L18)
