---
id: 'molecule.model.IFolderTreeModel'
title: 'Class: IFolderTreeModel'
sidebar_label: 'IFolderTreeModel'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTreeModel

## Implements

-   [`IFolderTree`](../interfaces/molecule.model.IFolderTree)

## Constructors

### constructor

• **new IFolderTreeModel**(`folderTree?`, `autoSort?`, `entry?`)

#### Parameters

| Name         | Type                                                                    | Default value |
| :----------- | :---------------------------------------------------------------------- | :------------ |
| `folderTree` | [`IFolderTreeSubItem`](../interfaces/molecule.model.IFolderTreeSubItem) | `undefined`   |
| `autoSort`   | `Boolean`                                                               | `false`       |
| `entry?`     | `ReactNode`                                                             | `undefined`   |

#### Defined in

[model/workbench/explorer/folderTree.tsx:98](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L98)

## Properties

### autoSort

• **autoSort**: `Boolean`

#### Implementation of

[IFolderTree](../interfaces/molecule.model.IFolderTree).[autoSort](../interfaces/molecule.model.IFolderTree#autosort)

#### Defined in

[model/workbench/explorer/folderTree.tsx:96](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L96)

---

### entry

• **entry**: `ReactNode`

#### Implementation of

[IFolderTree](../interfaces/molecule.model.IFolderTree).[entry](../interfaces/molecule.model.IFolderTree#entry)

#### Defined in

[model/workbench/explorer/folderTree.tsx:95](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L95)

---

### folderTree

• **folderTree**: [`IFolderTreeSubItem`](../interfaces/molecule.model.IFolderTreeSubItem)

#### Implementation of

[IFolderTree](../interfaces/molecule.model.IFolderTree).[folderTree](../interfaces/molecule.model.IFolderTree#foldertree)

#### Defined in

[model/workbench/explorer/folderTree.tsx:94](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L94)
