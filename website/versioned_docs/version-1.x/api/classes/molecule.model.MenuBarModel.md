---
id: 'molecule.model.MenuBarModel'
title: 'Class: MenuBarModel'
sidebar_label: 'MenuBarModel'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).MenuBarModel

## Implements

-   [`IMenuBar`](../interfaces/molecule.model.IMenuBar)

## Constructors

### constructor

• **new MenuBarModel**(`data?`)

#### Parameters

| Name   | Type                                                          | Default value |
| :----- | :------------------------------------------------------------ | :------------ |
| `data` | [`IMenuBarItem`](../interfaces/molecule.model.IMenuBarItem)[] | `[]`          |

#### Defined in

[model/workbench/menuBar.ts:34](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/menuBar.ts#L34)

## Properties

### data

• **data**: [`IMenuBarItem`](../interfaces/molecule.model.IMenuBarItem)[]

#### Implementation of

[IMenuBar](../interfaces/molecule.model.IMenuBar).[data](../interfaces/molecule.model.IMenuBar#data)

#### Defined in

[model/workbench/menuBar.ts:32](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/menuBar.ts#L32)
