---
id: 'molecule.model.IActivityBar'
title: 'Interface: IActivityBar'
sidebar_label: 'IActivityBar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IActivityBar

## Implemented by

-   [`ActivityBarModel`](../classes/molecule.model.ActivityBarModel)

## Properties

### contextMenu

• `Optional` **contextMenu**: [`IActivityMenuItemProps`](molecule.model.IActivityMenuItemProps)[]

#### Defined in

[model/workbench/activityBar.ts:37](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L37)

---

### data

• `Optional` **data**: [`IActivityBarItem`](molecule.model.IActivityBarItem)[]

#### Defined in

[model/workbench/activityBar.ts:36](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L36)

---

### selected

• `Optional` **selected**: `UniqueId`

#### Defined in

[model/workbench/activityBar.ts:38](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/activityBar.ts#L38)
