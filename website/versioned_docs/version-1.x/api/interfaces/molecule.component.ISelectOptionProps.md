---
id: 'molecule.component.ISelectOptionProps'
title: 'Interface: ISelectOptionProps'
sidebar_label: 'ISelectOptionProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).ISelectOptionProps

## Hierarchy

-   `ComponentProps`<`"div"`\>

    ↳ **`ISelectOptionProps`**

## Properties

### description

• `Optional` **description**: `string`

#### Defined in

[components/select/option.tsx:10](https://github.com/DTStack/molecule/blob/927b7d39/src/components/select/option.tsx#L10)

---

### disabled

• `Optional` **disabled**: `boolean`

#### Defined in

[components/select/option.tsx:11](https://github.com/DTStack/molecule/blob/927b7d39/src/components/select/option.tsx#L11)

---

### name

• `Optional` **name**: `string`

#### Defined in

[components/select/option.tsx:9](https://github.com/DTStack/molecule/blob/927b7d39/src/components/select/option.tsx#L9)

---

### value

• `Optional` **value**: `string`

#### Defined in

[components/select/option.tsx:8](https://github.com/DTStack/molecule/blob/927b7d39/src/components/select/option.tsx#L8)
