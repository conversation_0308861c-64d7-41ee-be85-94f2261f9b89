---
id: 'molecule.component.ICheckboxProps'
title: 'Interface: ICheckboxProps'
sidebar_label: 'ICheckboxProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).ICheckboxProps

## Hierarchy

-   `HTMLElementProps`

    ↳ **`ICheckboxProps`**

## Indexable

▪ [key: `string`]: `any`

## Properties

### children

• `Optional` **children**: `ReactNode`

#### Defined in

[components/checkbox/checkbox.tsx:9](https://github.com/DTStack/molecule/blob/927b7d39/src/components/checkbox/checkbox.tsx#L9)

---

### className

• `Optional` **className**: `string`

#### Inherited from

HTMLElementProps.className

#### Defined in

[common/types.ts:4](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L4)

---

### id

• **id**: `UniqueId`

#### Defined in

[components/checkbox/checkbox.tsx:7](https://github.com/DTStack/molecule/blob/927b7d39/src/components/checkbox/checkbox.tsx#L7)

---

### role

• `Optional` **role**: `string`

#### Inherited from

HTMLElementProps.role

#### Defined in

[common/types.ts:5](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L5)

---

### style

• `Optional` **style**: `CSSProperties`

#### Inherited from

HTMLElementProps.style

#### Defined in

[common/types.ts:3](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L3)

---

### title

• `Optional` **title**: `string`

#### Inherited from

HTMLElementProps.title

#### Defined in

[common/types.ts:2](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L2)

---

### value

• `Optional` **value**: `string`

#### Defined in

[components/checkbox/checkbox.tsx:8](https://github.com/DTStack/molecule/blob/927b7d39/src/components/checkbox/checkbox.tsx#L8)

## Methods

### onChange

▸ `Optional` **onChange**(`e`, `options?`): `void`

#### Parameters

| Name       | Type                                                  |
| :--------- | :---------------------------------------------------- |
| `e`        | `ChangeEvent`<`Element`\>                             |
| `options?` | [`ICheckboxProps`](molecule.component.ICheckboxProps) |

#### Returns

`void`

#### Defined in

[components/checkbox/checkbox.tsx:10](https://github.com/DTStack/molecule/blob/927b7d39/src/components/checkbox/checkbox.tsx#L10)
