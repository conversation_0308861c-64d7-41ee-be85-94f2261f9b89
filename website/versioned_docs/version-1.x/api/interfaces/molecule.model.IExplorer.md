---
id: 'molecule.model.IExplorer'
title: 'Interface: IExplorer'
sidebar_label: 'IExplorer'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IExplorer

## Implemented by

-   [`IExplorerModel`](../classes/molecule.model.IExplorerModel)

## Properties

### activePanelKeys

• `Optional` **activePanelKeys**: `UniqueId`[]

#### Defined in

[model/workbench/explorer/explorer.tsx:42](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L42)

---

### data

• **data**: [`IExplorerPanelItem`](molecule.model.IExplorerPanelItem)[]

#### Defined in

[model/workbench/explorer/explorer.tsx:40](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L40)

---

### headerToolBar

• `Optional` **headerToolBar**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>

#### Defined in

[model/workbench/explorer/explorer.tsx:41](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/explorer.tsx#L41)
