---
id: 'molecule.model.BuiltInEditorTabDataType'
title: 'Interface: BuiltInEditorTabDataType'
sidebar_label: 'BuiltInEditorTabDataType'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).BuiltInEditorTabDataType

## Indexable

▪ [key: `string`]: `any`

## Properties

### language

• `Optional` **language**: `string`

#### Defined in

[model/workbench/editor.ts:23](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L23)

---

### modified

• `Optional` **modified**: `boolean`

#### Defined in

[model/workbench/editor.ts:26](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L26)

---

### path

• `Optional` **path**: `string`

#### Defined in

[model/workbench/editor.ts:24](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L24)

---

### value

• `Optional` **value**: `string`

#### Defined in

[model/workbench/editor.ts:25](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L25)
