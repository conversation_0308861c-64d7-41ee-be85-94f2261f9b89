---
id: 'molecule.model.IPanel'
title: 'Interface: IPanel'
sidebar_label: 'IPanel'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IPanel

## Implemented by

-   [`PanelModel`](../classes/molecule.model.PanelModel)

## Properties

### current

• `Optional` **current**: `null` \| [`IPanelItem`](molecule.model.IPanelItem)<`any`\>

#### Defined in

[model/workbench/panel.tsx:24](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/panel.tsx#L24)

---

### data

• `Optional` **data**: [`IPanelItem`](molecule.model.IPanelItem)<`any`\>[]

#### Defined in

[model/workbench/panel.tsx:25](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/panel.tsx#L25)

---

### toolbox

• `Optional` **toolbox**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>[]

#### Defined in

[model/workbench/panel.tsx:26](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/panel.tsx#L26)
