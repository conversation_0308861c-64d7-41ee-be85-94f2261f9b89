---
id: 'molecule.component.ITextAreaProps'
title: 'Interface: ITextAreaProps'
sidebar_label: 'ITextAreaProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).ITextAreaProps

## Hierarchy

-   `TextAreaProps`

    ↳ **`ITextAreaProps`**

## Properties

### maxLength

• `Optional` **maxLength**: `number`

#### Overrides

TextAreaProps.maxLength

#### Defined in

[components/input/textArea.tsx:12](https://github.com/DTStack/molecule/blob/927b7d39/src/components/input/textArea.tsx#L12)

---

### showCount

• `Optional` **showCount**: `boolean`

#### Defined in

[components/input/textArea.tsx:11](https://github.com/DTStack/molecule/blob/927b7d39/src/components/input/textArea.tsx#L11)

## Methods

### onChange

▸ `Optional` **onChange**(`e`): `void`

#### Parameters

| Name | Type                                                        |
| :--- | :---------------------------------------------------------- |
| `e`  | `ChangeEvent`<`HTMLTextAreaElement` \| `HTMLInputElement`\> |

#### Returns

`void`

#### Overrides

TextAreaProps.onChange

#### Defined in

[components/input/textArea.tsx:13](https://github.com/DTStack/molecule/blob/927b7d39/src/components/input/textArea.tsx#L13)
