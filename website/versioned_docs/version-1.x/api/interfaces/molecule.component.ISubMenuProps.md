---
id: 'molecule.component.ISubMenuProps'
title: 'Interface: ISubMenuProps'
sidebar_label: 'ISubMenuProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).ISubMenuProps

## Hierarchy

-   `Omit`<[`IMenuItemProps`](molecule.component.IMenuItemProps), `"id"`\>

    ↳ **`ISubMenuProps`**

## Properties

### data

• `Optional` **data**: [`ISubMenuProps`](molecule.component.ISubMenuProps)[]

#### Defined in

[components/menu/subMenu.tsx:37](https://github.com/DTStack/molecule/blob/927b7d39/src/components/menu/subMenu.tsx#L37)

---

### icon

• `Optional` **icon**: `string` \| `Element`

#### Defined in

[components/menu/subMenu.tsx:36](https://github.com/DTStack/molecule/blob/927b7d39/src/components/menu/subMenu.tsx#L36)

---

### mode

• `Optional` **mode**: [`MenuMode`](../enums/molecule.component.MenuMode)

#### Defined in

[components/menu/subMenu.tsx:38](https://github.com/DTStack/molecule/blob/927b7d39/src/components/menu/subMenu.tsx#L38)

---

### trigger

• `Optional` **trigger**: `TriggerEvent`

The event of show subMenu, default value is 'hover'

#### Defined in

[components/menu/subMenu.tsx:35](https://github.com/DTStack/molecule/blob/927b7d39/src/components/menu/subMenu.tsx#L35)
