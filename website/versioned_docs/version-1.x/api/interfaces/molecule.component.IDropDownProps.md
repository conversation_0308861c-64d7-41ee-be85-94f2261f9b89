---
id: 'molecule.component.IDropDownProps'
title: 'Interface: IDropDownProps'
sidebar_label: 'IDropDownProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IDropDownProps

## Hierarchy

-   `ComponentProps`<`"div"`\>

    ↳ **`IDropDownProps`**

## Properties

### overlay

• **overlay**: `ReactNode`

#### Defined in

[components/dropdown/index.tsx:12](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dropdown/index.tsx#L12)

---

### placement

• `Optional` **placement**: `PlacementType`

#### Defined in

[components/dropdown/index.tsx:14](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dropdown/index.tsx#L14)

---

### trigger

• `Optional` **trigger**: `TriggerEvent`

#### Defined in

[components/dropdown/index.tsx:13](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dropdown/index.tsx#L13)
