---
id: 'molecule.component.IIconProps'
title: 'Interface: IIconProps'
sidebar_label: 'IIconProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IIconProps

## Hierarchy

-   `ComponentProps`<`"span"`\>

    ↳ **`IIconProps`**

## Properties

### type

• `Optional` **type**: `string` \| `Element`

#### Defined in

[components/icon/index.tsx:7](https://github.com/DTStack/molecule/blob/927b7d39/src/components/icon/index.tsx#L7)

## Methods

### onClick

▸ `Optional` **onClick**(`e`): `void`

#### Parameters

| Name | Type                                   |
| :--- | :------------------------------------- |
| `e`  | `MouseEvent`<`Element`, `MouseEvent`\> |

#### Returns

`void`

#### Overrides

ComponentProps.onClick

#### Defined in

[components/icon/index.tsx:8](https://github.com/DTStack/molecule/blob/927b7d39/src/components/icon/index.tsx#L8)
