---
id: 'molecule.component.IButtonProps'
title: 'Interface: IButtonProps'
sidebar_label: 'IButtonProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IButtonProps

## Hierarchy

-   `Omit`<`React.ComponentProps`<`"button"`\>, `"ref"`\>

    ↳ **`IButtonProps`**

## Properties

### disabled

• `Optional` **disabled**: `boolean`

#### Overrides

Omit.disabled

#### Defined in

[components/button/index.tsx:7](https://github.com/DTStack/molecule/blob/927b7d39/src/components/button/index.tsx#L7)

---

### size

• `Optional` **size**: `BtnSizeType`

#### Defined in

[components/button/index.tsx:8](https://github.com/DTStack/molecule/blob/927b7d39/src/components/button/index.tsx#L8)

## Methods

### onClick

▸ `Optional` **onClick**(`event`): `void`

#### Parameters

| Name    | Type                                   |
| :------ | :------------------------------------- |
| `event` | `MouseEvent`<`Element`, `MouseEvent`\> |

#### Returns

`void`

#### Overrides

Omit.onClick

#### Defined in

[components/button/index.tsx:9](https://github.com/DTStack/molecule/blob/927b7d39/src/components/button/index.tsx#L9)
