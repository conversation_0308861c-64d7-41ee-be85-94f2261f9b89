---
id: 'molecule.model.ISimpleKeybinding'
title: 'Interface: ISimpleKeybinding'
sidebar_label: 'ISimpleKeybinding'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ISimpleKeybinding

## Properties

### altKey

• **altKey**: `boolean`

#### Defined in

[model/keybinding.ts:62](https://github.com/DTStack/molecule/blob/927b7d39/src/model/keybinding.ts#L62)

---

### ctrlKey

• **ctrlKey**: `boolean`

#### Defined in

[model/keybinding.ts:60](https://github.com/DTStack/molecule/blob/927b7d39/src/model/keybinding.ts#L60)

---

### keyCode

• **keyCode**: `KeyCode`

#### Defined in

[model/keybinding.ts:64](https://github.com/DTStack/molecule/blob/927b7d39/src/model/keybinding.ts#L64)

---

### metaKey

• **metaKey**: `boolean`

#### Defined in

[model/keybinding.ts:63](https://github.com/DTStack/molecule/blob/927b7d39/src/model/keybinding.ts#L63)

---

### shiftKey

• **shiftKey**: `boolean`

#### Defined in

[model/keybinding.ts:61](https://github.com/DTStack/molecule/blob/927b7d39/src/model/keybinding.ts#L61)
