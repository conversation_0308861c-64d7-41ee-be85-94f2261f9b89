---
id: 'molecule.model.IMenuBar'
title: 'Interface: IMenuBar'
sidebar_label: 'IMenuBar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IMenuBar

## Implemented by

-   [`MenuBarModel`](../classes/molecule.model.MenuBarModel)

## Properties

### data

• **data**: [`IMenuBarItem`](molecule.model.IMenuBarItem)[]

#### Defined in

[model/workbench/menuBar.ts:27](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/menuBar.ts#L27)

---

### logo

• `Optional` **logo**: `ReactNode`

#### Defined in

[model/workbench/menuBar.ts:29](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/menuBar.ts#L29)

---

### mode

• `Optional` **mode**: `"horizontal"` \| `"vertical"`

#### Defined in

[model/workbench/menuBar.ts:28](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/menuBar.ts#L28)
