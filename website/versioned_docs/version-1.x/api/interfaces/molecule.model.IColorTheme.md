---
id: 'molecule.model.IColorTheme'
title: 'Interface: IColorTheme'
sidebar_label: 'IColorTheme'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IColorTheme

## Properties

### colors

• `Optional` **colors**: [`IColors`](molecule.model.IColors)

#### Defined in

[model/colorTheme.ts:39](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L39)

---

### description

• `Optional` **description**: `string`

#### Defined in

[model/colorTheme.ts:37](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L37)

---

### id

• **id**: `string`

The id of component, theme will be applied by this ID

#### Defined in

[model/colorTheme.ts:32](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L32)

---

### label

• **label**: `string`

#### Defined in

[model/colorTheme.ts:33](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L33)

---

### name

• `Optional` **name**: `string`

#### Defined in

[model/colorTheme.ts:34](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L34)

---

### path

• `Optional` **path**: `string`

#### Defined in

[model/colorTheme.ts:36](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L36)

---

### semanticHighlighting

• `Optional` **semanticHighlighting**: `boolean`

The semanticTokenColors mappings as well as
the semanticHighlighting setting
allow to enhance the highlighting in the editor
More info visit: https://code.visualstudio.com/api/language-extensions/semantic-highlight-guide

#### Defined in

[model/colorTheme.ts:47](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L47)

---

### tokenColors

• `Optional` **tokenColors**: [`TokenColor`](molecule.model.TokenColor)[]

#### Defined in

[model/colorTheme.ts:40](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L40)

---

### type

• `Optional` **type**: [`ColorScheme`](../enums/molecule.model.ColorScheme)

#### Defined in

[model/colorTheme.ts:38](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L38)

---

### uiTheme

• `Optional` **uiTheme**: `string`

#### Defined in

[model/colorTheme.ts:35](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L35)
