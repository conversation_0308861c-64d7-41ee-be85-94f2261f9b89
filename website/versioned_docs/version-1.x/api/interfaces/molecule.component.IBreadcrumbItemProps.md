---
id: 'molecule.component.IBreadcrumbItemProps'
title: 'Interface: IBreadcrumbItemProps'
sidebar_label: 'IBreadcrumbItemProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IBreadcrumbItemProps

## Hierarchy

-   `HTMLElementProps`

    ↳ **`IBreadcrumbItemProps`**

## Indexable

▪ [key: `string`]: `any`

## Properties

### className

• `Optional` **className**: `string`

#### Inherited from

HTMLElementProps.className

#### Defined in

[common/types.ts:4](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L4)

---

### href

• `Optional` **href**: `string`

#### Defined in

[components/breadcrumb/index.tsx:14](https://github.com/DTStack/molecule/blob/927b7d39/src/components/breadcrumb/index.tsx#L14)

---

### icon

• `Optional` **icon**: `string` \| `Element`

#### Defined in

[components/breadcrumb/index.tsx:16](https://github.com/DTStack/molecule/blob/927b7d39/src/components/breadcrumb/index.tsx#L16)

---

### id

• **id**: `UniqueId`

#### Defined in

[components/breadcrumb/index.tsx:13](https://github.com/DTStack/molecule/blob/927b7d39/src/components/breadcrumb/index.tsx#L13)

---

### name

• `Optional` **name**: `string`

#### Defined in

[components/breadcrumb/index.tsx:15](https://github.com/DTStack/molecule/blob/927b7d39/src/components/breadcrumb/index.tsx#L15)

---

### role

• `Optional` **role**: `string`

#### Inherited from

HTMLElementProps.role

#### Defined in

[common/types.ts:5](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L5)

---

### style

• `Optional` **style**: `CSSProperties`

#### Inherited from

HTMLElementProps.style

#### Defined in

[common/types.ts:3](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L3)

---

### title

• `Optional` **title**: `string`

#### Inherited from

HTMLElementProps.title

#### Defined in

[common/types.ts:2](https://github.com/DTStack/molecule/blob/927b7d39/src/common/types.ts#L2)

## Methods

### render

▸ `Optional` **render**(`item`): `ReactNode`

#### Parameters

| Name   | Type                                                              |
| :----- | :---------------------------------------------------------------- |
| `item` | [`IBreadcrumbItemProps`](molecule.component.IBreadcrumbItemProps) |

#### Returns

`ReactNode`

#### Defined in

[components/breadcrumb/index.tsx:17](https://github.com/DTStack/molecule/blob/927b7d39/src/components/breadcrumb/index.tsx#L17)
