---
id: 'molecule.event.ListenerEventContext'
title: 'Interface: ListenerEventContext'
sidebar_label: 'ListenerEventContext'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[event](../namespaces/molecule.event).ListenerEventContext

## Methods

### stopDelivery

▸ **stopDelivery**(): `void`

#### Returns

`void`

#### Defined in

[common/event/eventEmitter.ts:2](https://github.com/DTStack/molecule/blob/927b7d39/src/common/event/eventEmitter.ts#L2)
