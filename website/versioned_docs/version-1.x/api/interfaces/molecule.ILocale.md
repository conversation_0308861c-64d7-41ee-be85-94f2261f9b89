---
id: 'molecule.ILocale'
title: 'Interface: ILocale'
sidebar_label: 'ILocale'
custom_edit_url: null
---

[molecule](../namespaces/molecule).ILocale

## Properties

### description

• `Optional` **description**: `string`

#### Defined in

[i18n/localization.ts:91](https://github.com/DTStack/molecule/blob/927b7d39/src/i18n/localization.ts#L91)

---

### id

• **id**: `string`

#### Defined in

[i18n/localization.ts:89](https://github.com/DTStack/molecule/blob/927b7d39/src/i18n/localization.ts#L89)

---

### inherit

• `Optional` **inherit**: `string`

Whether inherit an exist locale, if it's exist, merge the parent locale

#### Defined in

[i18n/localization.ts:95](https://github.com/DTStack/molecule/blob/927b7d39/src/i18n/localization.ts#L95)

---

### name

• **name**: `string`

#### Defined in

[i18n/localization.ts:90](https://github.com/DTStack/molecule/blob/927b7d39/src/i18n/localization.ts#L90)

---

### source

• **source**: `Map`<`string` \| `LocaleSourceIdType`, `string`\>

#### Defined in

[i18n/localization.ts:96](https://github.com/DTStack/molecule/blob/927b7d39/src/i18n/localization.ts#L96)
