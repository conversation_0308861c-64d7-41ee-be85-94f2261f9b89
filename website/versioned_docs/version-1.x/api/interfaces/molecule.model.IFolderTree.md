---
id: 'molecule.model.IFolderTree'
title: 'Interface: IFolderTree'
sidebar_label: 'IFolderTree'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTree

## Implemented by

-   [`IFolderTreeModel`](../classes/molecule.model.IFolderTreeModel)

## Properties

### autoSort

• `Optional` **autoSort**: `Boolean`

#### Defined in

[model/workbench/explorer/folderTree.tsx:44](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L44)

---

### entry

• `Optional` **entry**: `ReactNode`

#### Defined in

[model/workbench/explorer/folderTree.tsx:43](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L43)

---

### folderTree

• `Optional` **folderTree**: [`IFolderTreeSubItem`](molecule.model.IFolderTreeSubItem)

#### Defined in

[model/workbench/explorer/folderTree.tsx:42](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L42)
