---
id: 'molecule.model.IWorkbench'
title: 'Interface: IWorkbench'
sidebar_label: 'IWorkbench'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IWorkbench

## Properties

### activityBar

• **activityBar**: [`IActivityBar`](molecule.model.IActivityBar)

#### Defined in

[model/workbench/index.ts:16](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/index.ts#L16)

---

### menuBar

• **menuBar**: [`IMenuBar`](molecule.model.IMenuBar)

#### Defined in

[model/workbench/index.ts:17](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/index.ts#L17)

---

### panel

• **panel**: [`IPanel`](molecule.model.IPanel)

#### Defined in

[model/workbench/index.ts:15](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/index.ts#L15)

---

### sidebar

• **sidebar**: [`ISidebar`](molecule.model.ISidebar)

#### Defined in

[model/workbench/index.ts:19](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/index.ts#L19)

---

### statusBar

• **statusBar**: [`IStatusBar`](molecule.model.IStatusBar)

#### Defined in

[model/workbench/index.ts:18](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/index.ts#L18)
