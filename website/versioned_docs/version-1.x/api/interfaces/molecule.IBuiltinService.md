---
id: 'molecule.IBuiltinService'
title: 'Interface: IBuiltinService'
sidebar_label: 'IBuiltinService'
custom_edit_url: null
---

[molecule](../namespaces/molecule).IBuiltinService

## Implemented by

-   [`BuiltinService`](../classes/molecule.BuiltinService)

## Methods

### getConstant

▸ **getConstant**(`id`): `undefined` \| `IBuiltinConstantProps`

Get the specific constant by id

#### Parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id` | `"PANEL_PROBLEMS"` \| `"STATUS_PROBLEMS"` \| `"SAMPLE_FOLDER_PANEL_ID"` \| `"EDITOR_PANEL_ID"` \| `"OUTLINE_PANEL_ID"` \| `"OUTLINE_PANEL_MORE_DESC"` \| `"EXPLORER_ACTIVITY_ITEM"` \| `"EXPLORER_ACTION_TITLE"` \| `"EXPLORER_TOGGLE_VERTICAL"` \| `"EXPLORER_TOGGLE_SAVE_ALL"` \| `"EXPLORER_TOGGLE_CLOSE_ALL_EDITORS"` \| `"EXPLORER_TOGGLE_SAVE_GROUP"` \| `"EXPLORER_TOGGLE_CLOSE_GROUP_EDITORS"` \| `"NEW_FILE_COMMAND_ID"` \| `"NEW_FOLDER_COMMAND_ID"` \| `"COLLAPSE_COMMAND_ID"` \| `"RENAME_COMMAND_ID"` \| `"REMOVE_COMMAND_ID"` \| `"DELETE_COMMAND_ID"` \| `"OPEN_TO_SIDE_COMMAND_ID"` \| `"FIND_IN_WORKSPACE_ID"` \| `"DOWNLOAD_COMMAND_ID"` \| `"EDITOR_MENU_CLOSE_TO_RIGHT"` \| `"EDITOR_MENU_CLOSE_TO_LEFT"` \| `"EDITOR_MENU_CLOSE_ALL"` \| `"EDITOR_MENU_CLOSE_OTHERS"` \| `"EDITOR_MENU_CLOSE_SAVED"` \| `"EDITOR_MENU_CLOSE"` \| `"EDITOR_MENU_SHOW_OPENEDITORS"` \| `"EDITOR_MENU_SPLIT"` \| `"SETTING_ID"` \| `"PROBLEM_MODEL_ID"` \| `"PROBLEM_MODEL_NAME"` \| `"NOTIFICATION_CLEAR_ALL_ID"` \| `"NOTIFICATION_HIDE_ID"` \| `"NOTIFICATION_MODEL_ID"` \| `"NOTIFICATION_MODEL_NAME"` \| `"STATUS_BAR_HIDE_ID"` \| `"SEARCH_CASE_SENSITIVE_COMMAND_ID"` \| `"SEARCH_WHOLE_WORD_COMMAND_ID"` \| `"SEARCH_REGULAR_EXPRESSION_COMMAND_ID"` \| `"SEARCH_PRESERVE_CASE_COMMAND_ID"` \| `"SEARCH_REPLACE_ALL_COMMAND_ID"` \| `"SEARCH_ACTIVITY_ITEM"` \| `"SEARCH_TOOLBAR_REFRESH"` \| `"SEARCH_TOOLBAR_CLEAR"` \| `"SEARCH_TOOLBAR_COLLAPSE"` \| `"PANEL_TOOLBOX_CLOSE"` \| `"PANEL_TOOLBOX_RESIZE"` \| `"PANEL_TOOLBOX_RESTORE_SIZE"` \| `"PANEL_OUTPUT"` \| `"MENU_APPEARANCE_ID"` \| `"MENU_FILE_OPEN"` \| `"MENU_QUICK_COMMAND"` \| `"MENU_VIEW_MENUBAR"` \| `"MENU_VIEW_AUXILIARY"` \| `"MENU_VIEW_ACTIVITYBAR"` \| `"MENU_VIEW_STATUSBAR"` \| `"MENU_VIEW_PANEL"` \| `"ACTION_QUICK_COMMAND"` \| `"ACTION_QUICK_SELECT_ALL"` \| `"ACTION_QUICK_COPY_LINE_UP"` \| `"ACTION_QUICK_UNDO"` \| `"ACTION_QUICK_REDO"` \| `"ACTION_QUICK_CREATE_FILE"` \| `"ACTION_QUICK_CREATE_FOLDER"` \| `"ACTION_QUICK_ACCESS_SETTINGS"` \| `"ACTION_SELECT_THEME"` \| `"ACTION_SELECT_LOCALE"` \| `"ACTIVITY_BAR_GLOBAL_SETTINGS"` \| `"ACTIVITY_BAR_GLOBAL_ACCOUNT"` \| `"CONTEXT_MENU_MENU"` \| `"CONTEXT_MENU_EXPLORER"` \| `"CONTEXT_MENU_SEARCH"` \| `"CONTEXT_MENU_HIDE"` \| `"MENUBAR_MODE_HORIZONTAL"` \| `"MENUBAR_MODE_VERTICAL"` \| `"MENUBAR_MENU_MODE_DIVIDER"` |

#### Returns

`undefined` \| `IBuiltinConstantProps`

#### Defined in

[services/builtinService/index.ts:38](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L38)

---

### getConstants

▸ **getConstants**(): `Partial`<{ `ACTION_QUICK_ACCESS_SETTINGS`: `string` = 'workbench.action.quickAccessSettings'; `ACTION_QUICK_COMMAND`: `string` = 'editor.action.quickCommand'; `ACTION_QUICK_COPY_LINE_UP`: `string` = 'editor.action.copyLinesUpAction'; `ACTION_QUICK_CREATE_FILE`: `string` = 'workbench.action.quickCreateFile'; `ACTION_QUICK_CREATE_FOLDER`: `string` = 'workbench.action.quickCreateFolder'; `ACTION_QUICK_REDO`: `string` = 'editor.action.redo'; `ACTION_QUICK_SELECT_ALL`: `string` = 'editor.action.quickSelectAll'; `ACTION_QUICK_UNDO`: `string` = 'editor.action.undo'; `ACTION_SELECT_LOCALE`: `string` = 'workbench.action.selectLocale'; `ACTION_SELECT_THEME`: `string` = 'workbench.action.selectTheme'; `ACTIVITY_BAR_GLOBAL_ACCOUNT`: `string` = 'global.menu.account'; `ACTIVITY_BAR_GLOBAL_SETTINGS`: `string` = 'global.menu.settings'; `COLLAPSE_COMMAND_ID`: `string` = 'explorer.collapse'; `CONTEXT_MENU_EXPLORER`: `string` = 'sidebar.explore.title'; `CONTEXT_MENU_HIDE`: `string` = 'menu.hideActivityBar'; `CONTEXT_MENU_MENU`: `string` = 'menubar'; `CONTEXT_MENU_SEARCH`: `string` = 'sidebar.search.title'; `DELETE_COMMAND_ID`: `string` = 'explorer.delete'; `DOWNLOAD_COMMAND_ID`: `string` = 'explorer.download'; `EDITOR_MENU_CLOSE`: `string` = 'editor.close'; `EDITOR_MENU_CLOSE_ALL`: `string` = 'editor.closeAll'; `EDITOR_MENU_CLOSE_OTHERS`: `string` = 'editor.closeOthers'; `EDITOR_MENU_CLOSE_SAVED`: `string` = 'editor.closeSaved'; `EDITOR_MENU_CLOSE_TO_LEFT`: `string` = 'editor.closeToLeft'; `EDITOR_MENU_CLOSE_TO_RIGHT`: `string` = 'editor.closeToRight'; `EDITOR_MENU_SHOW_OPENEDITORS`: `string` = 'editor.showOpenEditors'; `EDITOR_MENU_SPLIT`: `string` = 'editor.split'; `EDITOR_PANEL_ID`: `string` = 'sidebar.explore.openEditor'; `EXPLORER_ACTION_TITLE`: `string` = 'sidebar.explore.actionDesc'; `EXPLORER_ACTIVITY_ITEM`: `string` = 'sidebar.explore.title'; `EXPLORER_TOGGLE_CLOSE_ALL_EDITORS`: `string` = 'sidebar.explore.closeAllEditors'; `EXPLORER_TOGGLE_CLOSE_GROUP_EDITORS`: `string` = 'sidebar.explore.closeGroupEditors'; `EXPLORER_TOGGLE_SAVE_ALL`: `string` = 'sidebar.explore.saveALL'; `EXPLORER_TOGGLE_SAVE_GROUP`: `string` = 'sidebar.explore.saveGroup'; `EXPLORER_TOGGLE_VERTICAL`: `string` = 'sidebar.explore.toggleVertical'; `FIND_IN_WORKSPACE_ID`: `string` = 'filesExplorer.findInWorkspace'; `MENUBAR_MENU_MODE_DIVIDER`: `string` = 'menuBar.modeDivider'; `MENUBAR_MODE_HORIZONTAL`: `string` = 'menuBar.mode.horizontal'; `MENUBAR_MODE_VERTICAL`: `string` = 'menuBar.mode.vertical'; `MENU_APPEARANCE_ID`: `string` = 'Appearance'; `MENU_FILE_OPEN`: `string` = 'openFile'; `MENU_QUICK_COMMAND`: `string` = 'editor.action.quickCommand'; `MENU_VIEW_ACTIVITYBAR`: `string` = 'workbench.action.showActivityBar'; `MENU_VIEW_AUXILIARY`: `string` = 'workbench.action.showAuxiliary'; `MENU_VIEW_MENUBAR`: `string` = 'workbench.action.showMenuBar'; `MENU_VIEW_PANEL`: `string` = 'workbench.action.showPanel'; `MENU_VIEW_STATUSBAR`: `string` = 'workbench.action.showStatusBar'; `NEW_FILE_COMMAND_ID`: `string` = 'explorer.newFile'; `NEW_FOLDER_COMMAND_ID`: `string` = 'explorer.newFolder'; `NOTIFICATION_CLEAR_ALL_ID`: `string` = 'ClearAll'; `NOTIFICATION_HIDE_ID`: `string` = 'HideNotifications'; `NOTIFICATION_MODEL_ID`: `string` = 'MO_NOTIFICATION'; `NOTIFICATION_MODEL_NAME`: `string` = 'Notification'; `OPEN_TO_SIDE_COMMAND_ID`: `string` = 'explorer.openToSide'; `OUTLINE_PANEL_ID`: `string` = 'sidebar.explore.outline'; `OUTLINE_PANEL_MORE_DESC`: `string` = 'sidebar.explore.outlineMore'; `PANEL_OUTPUT`: `string` = 'panel.output.title'; `PANEL_PROBLEMS`: `string` = 'panel.problems.title'; `PANEL_TOOLBOX_CLOSE`: `string` = 'panel.toolbox.closePanel'; `PANEL_TOOLBOX_RESIZE`: `string` = 'panel.toolbox.maximize'; `PANEL_TOOLBOX_RESTORE_SIZE`: `string` = 'panel.toolbox.restoreSize'; `PROBLEM_MODEL_ID`: `string` = 'MO_PROBLEMS'; `PROBLEM_MODEL_NAME`: `string` = 'Problems'; `REMOVE_COMMAND_ID`: `string` = 'explorer.remove'; `RENAME_COMMAND_ID`: `string` = 'explorer.rename'; `SAMPLE_FOLDER_PANEL_ID`: `string` = 'sidebar.explore.folders'; `SEARCH_ACTIVITY_ITEM`: `string` = 'sidebar.search.title'; `SEARCH_CASE_SENSITIVE_COMMAND_ID`: `string` = 'search.matchCase'; `SEARCH_PRESERVE_CASE_COMMAND_ID`: `string` = 'search.preserveCase'; `SEARCH_REGULAR_EXPRESSION_COMMAND_ID`: `string` = 'search.useRegularExpression'; `SEARCH_REPLACE_ALL_COMMAND_ID`: `string` = 'search.replaceAll'; `SEARCH_TOOLBAR_CLEAR`: `string` = 'search.toolbar.clearAll'; `SEARCH_TOOLBAR_COLLAPSE`: `string` = 'search.toolbar.collapseAll'; `SEARCH_TOOLBAR_REFRESH`: `string` = 'search.toolbar.refresh'; `SEARCH_WHOLE_WORD_COMMAND_ID`: `string` = 'search.matchWholeWord'; `SETTING_ID`: `string` = 'Setting'; `STATUS_BAR_HIDE_ID`: `string` = 'hide'; `STATUS_PROBLEMS`: `string` = 'statusbar.problems.title' }\>

Get all constants

#### Returns

`Partial`<{ `ACTION_QUICK_ACCESS_SETTINGS`: `string` = 'workbench.action.quickAccessSettings'; `ACTION_QUICK_COMMAND`: `string` = 'editor.action.quickCommand'; `ACTION_QUICK_COPY_LINE_UP`: `string` = 'editor.action.copyLinesUpAction'; `ACTION_QUICK_CREATE_FILE`: `string` = 'workbench.action.quickCreateFile'; `ACTION_QUICK_CREATE_FOLDER`: `string` = 'workbench.action.quickCreateFolder'; `ACTION_QUICK_REDO`: `string` = 'editor.action.redo'; `ACTION_QUICK_SELECT_ALL`: `string` = 'editor.action.quickSelectAll'; `ACTION_QUICK_UNDO`: `string` = 'editor.action.undo'; `ACTION_SELECT_LOCALE`: `string` = 'workbench.action.selectLocale'; `ACTION_SELECT_THEME`: `string` = 'workbench.action.selectTheme'; `ACTIVITY_BAR_GLOBAL_ACCOUNT`: `string` = 'global.menu.account'; `ACTIVITY_BAR_GLOBAL_SETTINGS`: `string` = 'global.menu.settings'; `COLLAPSE_COMMAND_ID`: `string` = 'explorer.collapse'; `CONTEXT_MENU_EXPLORER`: `string` = 'sidebar.explore.title'; `CONTEXT_MENU_HIDE`: `string` = 'menu.hideActivityBar'; `CONTEXT_MENU_MENU`: `string` = 'menubar'; `CONTEXT_MENU_SEARCH`: `string` = 'sidebar.search.title'; `DELETE_COMMAND_ID`: `string` = 'explorer.delete'; `DOWNLOAD_COMMAND_ID`: `string` = 'explorer.download'; `EDITOR_MENU_CLOSE`: `string` = 'editor.close'; `EDITOR_MENU_CLOSE_ALL`: `string` = 'editor.closeAll'; `EDITOR_MENU_CLOSE_OTHERS`: `string` = 'editor.closeOthers'; `EDITOR_MENU_CLOSE_SAVED`: `string` = 'editor.closeSaved'; `EDITOR_MENU_CLOSE_TO_LEFT`: `string` = 'editor.closeToLeft'; `EDITOR_MENU_CLOSE_TO_RIGHT`: `string` = 'editor.closeToRight'; `EDITOR_MENU_SHOW_OPENEDITORS`: `string` = 'editor.showOpenEditors'; `EDITOR_MENU_SPLIT`: `string` = 'editor.split'; `EDITOR_PANEL_ID`: `string` = 'sidebar.explore.openEditor'; `EXPLORER_ACTION_TITLE`: `string` = 'sidebar.explore.actionDesc'; `EXPLORER_ACTIVITY_ITEM`: `string` = 'sidebar.explore.title'; `EXPLORER_TOGGLE_CLOSE_ALL_EDITORS`: `string` = 'sidebar.explore.closeAllEditors'; `EXPLORER_TOGGLE_CLOSE_GROUP_EDITORS`: `string` = 'sidebar.explore.closeGroupEditors'; `EXPLORER_TOGGLE_SAVE_ALL`: `string` = 'sidebar.explore.saveALL'; `EXPLORER_TOGGLE_SAVE_GROUP`: `string` = 'sidebar.explore.saveGroup'; `EXPLORER_TOGGLE_VERTICAL`: `string` = 'sidebar.explore.toggleVertical'; `FIND_IN_WORKSPACE_ID`: `string` = 'filesExplorer.findInWorkspace'; `MENUBAR_MENU_MODE_DIVIDER`: `string` = 'menuBar.modeDivider'; `MENUBAR_MODE_HORIZONTAL`: `string` = 'menuBar.mode.horizontal'; `MENUBAR_MODE_VERTICAL`: `string` = 'menuBar.mode.vertical'; `MENU_APPEARANCE_ID`: `string` = 'Appearance'; `MENU_FILE_OPEN`: `string` = 'openFile'; `MENU_QUICK_COMMAND`: `string` = 'editor.action.quickCommand'; `MENU_VIEW_ACTIVITYBAR`: `string` = 'workbench.action.showActivityBar'; `MENU_VIEW_AUXILIARY`: `string` = 'workbench.action.showAuxiliary'; `MENU_VIEW_MENUBAR`: `string` = 'workbench.action.showMenuBar'; `MENU_VIEW_PANEL`: `string` = 'workbench.action.showPanel'; `MENU_VIEW_STATUSBAR`: `string` = 'workbench.action.showStatusBar'; `NEW_FILE_COMMAND_ID`: `string` = 'explorer.newFile'; `NEW_FOLDER_COMMAND_ID`: `string` = 'explorer.newFolder'; `NOTIFICATION_CLEAR_ALL_ID`: `string` = 'ClearAll'; `NOTIFICATION_HIDE_ID`: `string` = 'HideNotifications'; `NOTIFICATION_MODEL_ID`: `string` = 'MO_NOTIFICATION'; `NOTIFICATION_MODEL_NAME`: `string` = 'Notification'; `OPEN_TO_SIDE_COMMAND_ID`: `string` = 'explorer.openToSide'; `OUTLINE_PANEL_ID`: `string` = 'sidebar.explore.outline'; `OUTLINE_PANEL_MORE_DESC`: `string` = 'sidebar.explore.outlineMore'; `PANEL_OUTPUT`: `string` = 'panel.output.title'; `PANEL_PROBLEMS`: `string` = 'panel.problems.title'; `PANEL_TOOLBOX_CLOSE`: `string` = 'panel.toolbox.closePanel'; `PANEL_TOOLBOX_RESIZE`: `string` = 'panel.toolbox.maximize'; `PANEL_TOOLBOX_RESTORE_SIZE`: `string` = 'panel.toolbox.restoreSize'; `PROBLEM_MODEL_ID`: `string` = 'MO_PROBLEMS'; `PROBLEM_MODEL_NAME`: `string` = 'Problems'; `REMOVE_COMMAND_ID`: `string` = 'explorer.remove'; `RENAME_COMMAND_ID`: `string` = 'explorer.rename'; `SAMPLE_FOLDER_PANEL_ID`: `string` = 'sidebar.explore.folders'; `SEARCH_ACTIVITY_ITEM`: `string` = 'sidebar.search.title'; `SEARCH_CASE_SENSITIVE_COMMAND_ID`: `string` = 'search.matchCase'; `SEARCH_PRESERVE_CASE_COMMAND_ID`: `string` = 'search.preserveCase'; `SEARCH_REGULAR_EXPRESSION_COMMAND_ID`: `string` = 'search.useRegularExpression'; `SEARCH_REPLACE_ALL_COMMAND_ID`: `string` = 'search.replaceAll'; `SEARCH_TOOLBAR_CLEAR`: `string` = 'search.toolbar.clearAll'; `SEARCH_TOOLBAR_COLLAPSE`: `string` = 'search.toolbar.collapseAll'; `SEARCH_TOOLBAR_REFRESH`: `string` = 'search.toolbar.refresh'; `SEARCH_WHOLE_WORD_COMMAND_ID`: `string` = 'search.matchWholeWord'; `SETTING_ID`: `string` = 'Setting'; `STATUS_BAR_HIDE_ID`: `string` = 'hide'; `STATUS_PROBLEMS`: `string` = 'statusbar.problems.title' }\>

#### Defined in

[services/builtinService/index.ts:42](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L42)

---

### getModule

▸ **getModule**<`T`\>(`id`): `undefined` \| `IBuiltinModuleProps`<`T`\>

Get the specific module by id

#### Type parameters

| Name |
| :--- |
| `T`  |

#### Parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `id` | `"builtInExplorerActivityItem"` \| `"builtInExplorerFolderPanel"` \| `"builtInExplorerHeaderToolbar"` \| `"builtInExplorerEditorPanel"` \| `"builtInExplorerOutlinePanel"` \| `"BuiltInEditorOptions"` \| `"builtInEditorInitialActions"` \| `"builtInEditorInitialMenu"` \| `"builtInEditorTreeHeaderContextMenu"` \| `"builtInEditorTreeContextMenu"` \| `"BuiltInSettingsTab"` \| `"builtInStatusProblems"` \| `"builtInPanelProblems"` \| `"NOTIFICATION_CLEAR_ALL"` \| `"NOTIFICATION_HIDE"` \| `"builtInNotification"` \| `"STATUS_EDITOR_INFO"` \| `"CONTEXT_MENU_HIDE_STATUS_BAR"` \| `"builtInSearchActivityItem"` \| `"builtInHeaderToolbar"` \| `"builtInSearchAddons"` \| `"builtInReplaceAddons"` \| `"builtInOutputPanel"` \| `"builtInPanelToolboxResize"` \| `"builtInPanelToolboxReStore"` \| `"builtInPanelToolbox"` \| `"builtInMenuBarData"` \| `"quickAcessViewAction"` \| `"quickSelectColorThemeAction"` \| `"quickAccessSettingsAction"` \| `"quickSelectLocaleAction"` \| `"quickTogglePanelAction"` \| `"quickSelectAllAction"` \| `"quickCopyLineUpAction"` \| `"quickUndoAction"` \| `"quickRedoAction"` \| `"quickCreateFileAction"` \| `"COMMON_CONTEXT_MENU"` \| `"BASE_CONTEXT_MENU"` \| `"ROOT_FOLDER_CONTEXT_MENU"` \| `"FILE_CONTEXT_MENU"` \| `"FOLDER_PANEL_CONTEXT_MENU"` \| `"activityBarData"` \| `"contextMenuData"` |

#### Returns

`undefined` \| `IBuiltinModuleProps`<`T`\>

#### Defined in

[services/builtinService/index.ts:46](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L46)

---

### getModules

▸ **getModules**(): `any`

Get all modules

#### Returns

`any`

#### Defined in

[services/builtinService/index.ts:50](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L50)

---

### inactiveConstant

▸ **inactiveConstant**(`id`): `boolean`

Mark the specific constant as inactive

**`deprecated`** we're considering the necessary of this method, because it's useless for now to inactive a constant

#### Parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id` | `"PANEL_PROBLEMS"` \| `"STATUS_PROBLEMS"` \| `"SAMPLE_FOLDER_PANEL_ID"` \| `"EDITOR_PANEL_ID"` \| `"OUTLINE_PANEL_ID"` \| `"OUTLINE_PANEL_MORE_DESC"` \| `"EXPLORER_ACTIVITY_ITEM"` \| `"EXPLORER_ACTION_TITLE"` \| `"EXPLORER_TOGGLE_VERTICAL"` \| `"EXPLORER_TOGGLE_SAVE_ALL"` \| `"EXPLORER_TOGGLE_CLOSE_ALL_EDITORS"` \| `"EXPLORER_TOGGLE_SAVE_GROUP"` \| `"EXPLORER_TOGGLE_CLOSE_GROUP_EDITORS"` \| `"NEW_FILE_COMMAND_ID"` \| `"NEW_FOLDER_COMMAND_ID"` \| `"COLLAPSE_COMMAND_ID"` \| `"RENAME_COMMAND_ID"` \| `"REMOVE_COMMAND_ID"` \| `"DELETE_COMMAND_ID"` \| `"OPEN_TO_SIDE_COMMAND_ID"` \| `"FIND_IN_WORKSPACE_ID"` \| `"DOWNLOAD_COMMAND_ID"` \| `"EDITOR_MENU_CLOSE_TO_RIGHT"` \| `"EDITOR_MENU_CLOSE_TO_LEFT"` \| `"EDITOR_MENU_CLOSE_ALL"` \| `"EDITOR_MENU_CLOSE_OTHERS"` \| `"EDITOR_MENU_CLOSE_SAVED"` \| `"EDITOR_MENU_CLOSE"` \| `"EDITOR_MENU_SHOW_OPENEDITORS"` \| `"EDITOR_MENU_SPLIT"` \| `"SETTING_ID"` \| `"PROBLEM_MODEL_ID"` \| `"PROBLEM_MODEL_NAME"` \| `"NOTIFICATION_CLEAR_ALL_ID"` \| `"NOTIFICATION_HIDE_ID"` \| `"NOTIFICATION_MODEL_ID"` \| `"NOTIFICATION_MODEL_NAME"` \| `"STATUS_BAR_HIDE_ID"` \| `"SEARCH_CASE_SENSITIVE_COMMAND_ID"` \| `"SEARCH_WHOLE_WORD_COMMAND_ID"` \| `"SEARCH_REGULAR_EXPRESSION_COMMAND_ID"` \| `"SEARCH_PRESERVE_CASE_COMMAND_ID"` \| `"SEARCH_REPLACE_ALL_COMMAND_ID"` \| `"SEARCH_ACTIVITY_ITEM"` \| `"SEARCH_TOOLBAR_REFRESH"` \| `"SEARCH_TOOLBAR_CLEAR"` \| `"SEARCH_TOOLBAR_COLLAPSE"` \| `"PANEL_TOOLBOX_CLOSE"` \| `"PANEL_TOOLBOX_RESIZE"` \| `"PANEL_TOOLBOX_RESTORE_SIZE"` \| `"PANEL_OUTPUT"` \| `"MENU_APPEARANCE_ID"` \| `"MENU_FILE_OPEN"` \| `"MENU_QUICK_COMMAND"` \| `"MENU_VIEW_MENUBAR"` \| `"MENU_VIEW_AUXILIARY"` \| `"MENU_VIEW_ACTIVITYBAR"` \| `"MENU_VIEW_STATUSBAR"` \| `"MENU_VIEW_PANEL"` \| `"ACTION_QUICK_COMMAND"` \| `"ACTION_QUICK_SELECT_ALL"` \| `"ACTION_QUICK_COPY_LINE_UP"` \| `"ACTION_QUICK_UNDO"` \| `"ACTION_QUICK_REDO"` \| `"ACTION_QUICK_CREATE_FILE"` \| `"ACTION_QUICK_CREATE_FOLDER"` \| `"ACTION_QUICK_ACCESS_SETTINGS"` \| `"ACTION_SELECT_THEME"` \| `"ACTION_SELECT_LOCALE"` \| `"ACTIVITY_BAR_GLOBAL_SETTINGS"` \| `"ACTIVITY_BAR_GLOBAL_ACCOUNT"` \| `"CONTEXT_MENU_MENU"` \| `"CONTEXT_MENU_EXPLORER"` \| `"CONTEXT_MENU_SEARCH"` \| `"CONTEXT_MENU_HIDE"` \| `"MENUBAR_MODE_HORIZONTAL"` \| `"MENUBAR_MODE_VERTICAL"` \| `"MENUBAR_MENU_MODE_DIVIDER"` |

#### Returns

`boolean`

#### Defined in

[services/builtinService/index.ts:30](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L30)

---

### inactiveModule

▸ **inactiveModule**(`id`): `boolean`

Mark the specific module as inactive

#### Parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `id` | `"builtInExplorerActivityItem"` \| `"builtInExplorerFolderPanel"` \| `"builtInExplorerHeaderToolbar"` \| `"builtInExplorerEditorPanel"` \| `"builtInExplorerOutlinePanel"` \| `"BuiltInEditorOptions"` \| `"builtInEditorInitialActions"` \| `"builtInEditorInitialMenu"` \| `"builtInEditorTreeHeaderContextMenu"` \| `"builtInEditorTreeContextMenu"` \| `"BuiltInSettingsTab"` \| `"builtInStatusProblems"` \| `"builtInPanelProblems"` \| `"NOTIFICATION_CLEAR_ALL"` \| `"NOTIFICATION_HIDE"` \| `"builtInNotification"` \| `"STATUS_EDITOR_INFO"` \| `"CONTEXT_MENU_HIDE_STATUS_BAR"` \| `"builtInSearchActivityItem"` \| `"builtInHeaderToolbar"` \| `"builtInSearchAddons"` \| `"builtInReplaceAddons"` \| `"builtInOutputPanel"` \| `"builtInPanelToolboxResize"` \| `"builtInPanelToolboxReStore"` \| `"builtInPanelToolbox"` \| `"builtInMenuBarData"` \| `"quickAcessViewAction"` \| `"quickSelectColorThemeAction"` \| `"quickAccessSettingsAction"` \| `"quickSelectLocaleAction"` \| `"quickTogglePanelAction"` \| `"quickSelectAllAction"` \| `"quickCopyLineUpAction"` \| `"quickUndoAction"` \| `"quickRedoAction"` \| `"quickCreateFileAction"` \| `"COMMON_CONTEXT_MENU"` \| `"BASE_CONTEXT_MENU"` \| `"ROOT_FOLDER_CONTEXT_MENU"` \| `"FILE_CONTEXT_MENU"` \| `"FOLDER_PANEL_CONTEXT_MENU"` \| `"activityBarData"` \| `"contextMenuData"` |

#### Returns

`boolean`

#### Defined in

[services/builtinService/index.ts:34](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L34)

---

### reset

▸ **reset**(): `void`

Reset all constants and modules

#### Returns

`void`

#### Defined in

[services/builtinService/index.ts:54](https://github.com/DTStack/molecule/blob/927b7d39/src/services/builtinService/index.ts#L54)
