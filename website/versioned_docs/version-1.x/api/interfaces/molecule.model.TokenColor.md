---
id: 'molecule.model.TokenColor'
title: 'Interface: TokenColor'
sidebar_label: 'TokenColor'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).TokenColor

## Hierarchy

-   `Object`

    ↳ **`TokenColor`**

## Properties

### name

• `Optional` **name**: `string`

#### Defined in

[model/colorTheme.ts:5](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L5)

---

### scope

• `Optional` **scope**: `string` \| `string`[]

#### Defined in

[model/colorTheme.ts:6](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L6)

---

### settings

• `Optional` **settings**: `object`

#### Defined in

[model/colorTheme.ts:7](https://github.com/DTStack/molecule/blob/927b7d39/src/model/colorTheme.ts#L7)
