---
id: 'molecule.component.IModalFuncProps'
title: 'Interface: IModalFuncProps'
sidebar_label: 'IModalFuncProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IModalFuncProps

## Hierarchy

-   `IDialogPropTypes`

    ↳ **`IModalFuncProps`**

## Properties

### cancelButtonProps

• `Optional` **cancelButtonProps**: [`IButtonProps`](molecule.component.IButtonProps)

#### Defined in

[components/dialog/modal.tsx:33](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L33)

---

### cancelText

• `Optional` **cancelText**: `ReactNode`

#### Defined in

[components/dialog/modal.tsx:26](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L26)

---

### centered

• `Optional` **centered**: `boolean`

#### Defined in

[components/dialog/modal.tsx:34](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L34)

---

### content

• `Optional` **content**: `ReactNode`

#### Defined in

[components/dialog/modal.tsx:29](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L29)

---

### icon

• `Optional` **icon**: `string` \| `Element`

#### Defined in

[components/dialog/modal.tsx:28](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L28)

---

### okButtonProps

• `Optional` **okButtonProps**: [`IButtonProps`](molecule.component.IButtonProps)

#### Defined in

[components/dialog/modal.tsx:32](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L32)

---

### okCancel

• `Optional` **okCancel**: `boolean`

#### Defined in

[components/dialog/modal.tsx:35](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L35)

---

### okText

• `Optional` **okText**: `ReactNode`

#### Defined in

[components/dialog/modal.tsx:27](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L27)

---

### type

• `Optional` **type**: `"warning"` \| `"confirm"`

#### Defined in

[components/dialog/modal.tsx:36](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L36)

## Methods

### onCancel

▸ `Optional` **onCancel**(...`args`): `void`

#### Parameters

| Name      | Type    |
| :-------- | :------ |
| `...args` | `any`[] |

#### Returns

`void`

#### Defined in

[components/dialog/modal.tsx:31](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L31)

---

### onOk

▸ `Optional` **onOk**(...`args`): `any`

#### Parameters

| Name      | Type    |
| :-------- | :------ |
| `...args` | `any`[] |

#### Returns

`any`

#### Defined in

[components/dialog/modal.tsx:30](https://github.com/DTStack/molecule/blob/927b7d39/src/components/dialog/modal.tsx#L30)
