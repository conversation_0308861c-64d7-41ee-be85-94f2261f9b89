---
id: 'molecule.model.IEditorAction'
title: 'Interface: IEditorAction'
sidebar_label: 'IEditorAction'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IEditorAction

## Properties

### actions

• `Optional` **actions**: [`IEditorActionsProps`](molecule.model.IEditorActionsProps)[]

#### Defined in

[model/workbench/editor.ts:45](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L45)

---

### menu

• `Optional` **menu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[model/workbench/editor.ts:46](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/editor.ts#L46)
