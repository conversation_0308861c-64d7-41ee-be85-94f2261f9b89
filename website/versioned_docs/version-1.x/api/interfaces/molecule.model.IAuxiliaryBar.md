---
id: 'molecule.model.IAuxiliaryBar'
title: 'Interface: IAuxiliaryBar'
sidebar_label: 'IAuxiliaryBar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IAuxiliaryBar

## Implemented by

-   [`AuxiliaryModel`](../classes/molecule.model.AuxiliaryModel)

## Properties

### children

• `Optional` **children**: `ReactNode`

#### Defined in

[model/workbench/auxiliaryBar.ts:16](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/auxiliaryBar.ts#L16)

---

### current

• `Optional` **current**: `UniqueId`

#### Defined in

[model/workbench/auxiliaryBar.ts:15](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/auxiliaryBar.ts#L15)

---

### data

• **data**: [`IAuxiliaryData`](../namespaces/molecule.model#iauxiliarydata)[]

#### Defined in

[model/workbench/auxiliaryBar.ts:14](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/auxiliaryBar.ts#L14)

---

### mode

• **mode**: [`IAuxiliaryBarMode`](../namespaces/molecule.model#iauxiliarybarmode)

#### Defined in

[model/workbench/auxiliaryBar.ts:13](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/auxiliaryBar.ts#L13)
