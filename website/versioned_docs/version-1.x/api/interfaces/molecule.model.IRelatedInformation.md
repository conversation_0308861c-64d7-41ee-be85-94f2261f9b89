---
id: 'molecule.model.IRelatedInformation'
title: 'Interface: IRelatedInformation'
sidebar_label: 'IRelatedInformation'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IRelatedInformation

## Properties

### code

• **code**: `string`

#### Defined in

[model/problems.tsx:16](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L16)

---

### endColumn

• **endColumn**: `number`

#### Defined in

[model/problems.tsx:21](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L21)

---

### endLineNumber

• **endLineNumber**: `number`

#### Defined in

[model/problems.tsx:20](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L20)

---

### message

• **message**: `string`

#### Defined in

[model/problems.tsx:17](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L17)

---

### startColumn

• **startColumn**: `number`

#### Defined in

[model/problems.tsx:19](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L19)

---

### startLineNumber

• **startLineNumber**: `number`

#### Defined in

[model/problems.tsx:18](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L18)

---

### status

• **status**: [`MarkerSeverity`](../enums/molecule.model.MarkerSeverity)

#### Defined in

[model/problems.tsx:22](https://github.com/DTStack/molecule/blob/927b7d39/src/model/problems.tsx#L22)
