---
id: 'molecule.component.IContextMenuProps'
title: 'Interface: IContextMenuProps'
sidebar_label: 'IContextMenuProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IContextMenuProps

## Properties

### anchor

• **anchor**: `HTMLElementType`

#### Defined in

[components/contextMenu/index.tsx:6](https://github.com/DTStack/molecule/blob/927b7d39/src/components/contextMenu/index.tsx#L6)

## Methods

### render

▸ **render**(): `ReactNode`

#### Returns

`ReactNode`

#### Defined in

[components/contextMenu/index.tsx:7](https://github.com/DTStack/molecule/blob/927b7d39/src/components/contextMenu/index.tsx#L7)
