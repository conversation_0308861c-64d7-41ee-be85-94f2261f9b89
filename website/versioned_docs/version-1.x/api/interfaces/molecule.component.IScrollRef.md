---
id: 'molecule.component.IScrollRef'
title: 'Interface: IScrollRef'
sidebar_label: 'IScrollRef'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IScrollRef

## Properties

### scrollHeight

• **scrollHeight**: `number`

#### Defined in

[components/scrollBar/index.tsx:51](https://github.com/DTStack/molecule/blob/927b7d39/src/components/scrollBar/index.tsx#L51)

## Methods

### scrollTo

▸ **scrollTo**(`offset`): `void`

#### Parameters

| Name     | Type     |
| :------- | :------- |
| `offset` | `number` |

#### Returns

`void`

#### Defined in

[components/scrollBar/index.tsx:52](https://github.com/DTStack/molecule/blob/927b7d39/src/components/scrollBar/index.tsx#L52)
