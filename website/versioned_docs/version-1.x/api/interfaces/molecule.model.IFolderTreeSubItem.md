---
id: 'molecule.model.IFolderTreeSubItem'
title: 'Interface: IFolderTreeSubItem'
sidebar_label: 'IFolderTreeSubItem'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTreeSubItem

## Properties

### contextMenu

• `Optional` **contextMenu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[model/workbench/explorer/folderTree.tsx:35](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L35)

---

### current

• `Optional` **current**: `null` \| [`IFolderTreeNodeProps`](molecule.model.IFolderTreeNodeProps)

#### Defined in

[model/workbench/explorer/folderTree.tsx:37](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L37)

---

### data

• `Optional` **data**: [`IFolderTreeNodeProps`](molecule.model.IFolderTreeNodeProps)[]

#### Defined in

[model/workbench/explorer/folderTree.tsx:34](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L34)

---

### expandKeys

• `Optional` **expandKeys**: `UniqueId`[]

#### Defined in

[model/workbench/explorer/folderTree.tsx:38](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L38)

---

### folderPanelContextMenu

• `Optional` **folderPanelContextMenu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[model/workbench/explorer/folderTree.tsx:36](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L36)

---

### loadedKeys

• `Optional` **loadedKeys**: `string`[]

#### Defined in

[model/workbench/explorer/folderTree.tsx:39](https://github.com/DTStack/molecule/blob/927b7d39/src/model/workbench/explorer/folderTree.tsx#L39)
