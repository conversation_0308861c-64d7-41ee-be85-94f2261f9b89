---
id: 'index'
title: 'Molecule'
slug: '/api/'
sidebar_label: 'Exports'
sidebar_position: 0.5
custom_edit_url: null
---

## Namespaces

-   [molecule](namespaces/molecule)

## References

### default

Renames and re-exports [molecule](namespaces/molecule)

## Variables

### Workbench

• `Const` **Workbench**: `ComponentType`<`any`\>

#### Defined in

[workbench/workbench.tsx:178](https://github.com/DTStack/molecule/blob/927b7d39/src/workbench/workbench.tsx#L178)

## Functions

### create

▸ **create**(`config`): `default`

#### Parameters

| Name     | Type           |
| :------- | :------------- |
| `config` | `IConfigProps` |

#### Returns

`default`

#### Defined in

[provider/create.ts:39](https://github.com/DTStack/molecule/blob/927b7d39/src/provider/create.ts#L39)
