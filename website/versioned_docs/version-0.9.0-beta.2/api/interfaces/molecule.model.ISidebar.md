---
id: 'molecule.model.ISidebar'
title: 'Interface: ISidebar'
sidebar_label: 'ISidebar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ISidebar

## Implemented by

-   [`SidebarModel`](../classes/molecule.model.SidebarModel)

## Properties

### current

• **current**: `UniqueId`

#### Defined in

[src/model/workbench/sidebar.ts:10](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/sidebar.ts#L10)

---

### panes

• **panes**: [`ISidebarPane`](molecule.model.ISidebarPane)[]

#### Defined in

[src/model/workbench/sidebar.ts:11](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/sidebar.ts#L11)
