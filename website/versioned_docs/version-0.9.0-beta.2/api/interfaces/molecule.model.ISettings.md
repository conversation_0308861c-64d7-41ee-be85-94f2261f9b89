---
id: 'molecule.model.ISettings'
title: 'Interface: ISettings'
sidebar_label: 'ISettings'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ISettings

## Implemented by

-   [`SettingsModel`](../classes/molecule.model.SettingsModel)

## Indexable

▪ [index: `string`]: `any`

## Properties

### colorTheme

• `Optional` **colorTheme**: `string`

#### Defined in

[src/model/settings.ts:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/settings.ts#L14)

---

### editor

• `Optional` **editor**: [`IEditorOptions`](../namespaces/molecule.model#ieditoroptions)

#### Defined in

[src/model/settings.ts:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/settings.ts#L15)

---

### locale

• `Optional` **locale**: `string`

#### Defined in

[src/model/settings.ts:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/settings.ts#L16)
