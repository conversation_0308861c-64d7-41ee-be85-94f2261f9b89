---
id: 'molecule.component.IInputProps'
title: 'Interface: IInputProps'
sidebar_label: 'IInputProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IInputProps

## Hierarchy

-   `Omit`<`React.InputHTMLAttributes`<`HTMLInputElement`\>, `"size"` \| `"onChange"` \| `"onKeyDown"` \| `"onPressEnter"`\>

    ↳ **`IInputProps`**

## Properties

### about

• `Optional` **about**: `string`

#### Inherited from

Omit.about

#### Defined in

node_modules/@types/react/index.d.ts:1772

---

### accept

• `Optional` **accept**: `string`

#### Inherited from

Omit.accept

#### Defined in

node_modules/@types/react/index.d.ts:2082

---

### accessKey

• `Optional` **accessKey**: `string`

#### Inherited from

Omit.accessKey

#### Defined in

node_modules/@types/react/index.d.ts:1748

---

### alt

• `Optional` **alt**: `string`

#### Inherited from

Omit.alt

#### Defined in

node_modules/@types/react/index.d.ts:2083

---

### aria-activedescendant

• `Optional` **aria-activedescendant**: `string`

Identifies the currently active element when DOM focus is on a composite widget, textbox, group, or application.

#### Inherited from

Omit.aria-activedescendant

#### Defined in

node_modules/@types/react/index.d.ts:1555

---

### aria-atomic

• `Optional` **aria-atomic**: `boolean` \| `"true"` \| `"false"`

Indicates whether assistive technologies will present all, or only parts of, the changed region based on the change notifications defined by the aria-relevant attribute.

#### Inherited from

Omit.aria-atomic

#### Defined in

node_modules/@types/react/index.d.ts:1557

---

### aria-autocomplete

• `Optional` **aria-autocomplete**: `"none"` \| `"inline"` \| `"list"` \| `"both"`

Indicates whether inputting text could trigger display of one or more predictions of the user's intended value for an input and specifies how predictions would be
presented if they are made.

#### Inherited from

Omit.aria-autocomplete

#### Defined in

node_modules/@types/react/index.d.ts:1562

---

### aria-busy

• `Optional` **aria-busy**: `boolean` \| `"true"` \| `"false"`

Indicates an element is being modified and that assistive technologies MAY want to wait until the modifications are complete before exposing them to the user.

#### Inherited from

Omit.aria-busy

#### Defined in

node_modules/@types/react/index.d.ts:1564

---

### aria-checked

• `Optional` **aria-checked**: `boolean` \| `"true"` \| `"false"` \| `"mixed"`

Indicates the current "checked" state of checkboxes, radio buttons, and other widgets.

**`see`** aria-pressed @see aria-selected.

#### Inherited from

Omit.aria-checked

#### Defined in

node_modules/@types/react/index.d.ts:1569

---

### aria-colcount

• `Optional` **aria-colcount**: `number`

Defines the total number of columns in a table, grid, or treegrid.

**`see`** aria-colindex.

#### Inherited from

Omit.aria-colcount

#### Defined in

node_modules/@types/react/index.d.ts:1574

---

### aria-colindex

• `Optional` **aria-colindex**: `number`

Defines an element's column index or position with respect to the total number of columns within a table, grid, or treegrid.

**`see`** aria-colcount @see aria-colspan.

#### Inherited from

Omit.aria-colindex

#### Defined in

node_modules/@types/react/index.d.ts:1579

---

### aria-colspan

• `Optional` **aria-colspan**: `number`

Defines the number of columns spanned by a cell or gridcell within a table, grid, or treegrid.

**`see`** aria-colindex @see aria-rowspan.

#### Inherited from

Omit.aria-colspan

#### Defined in

node_modules/@types/react/index.d.ts:1584

---

### aria-controls

• `Optional` **aria-controls**: `string`

Identifies the element (or elements) whose contents or presence are controlled by the current element.

**`see`** aria-owns.

#### Inherited from

Omit.aria-controls

#### Defined in

node_modules/@types/react/index.d.ts:1589

---

### aria-current

• `Optional` **aria-current**: `boolean` \| `"time"` \| `"true"` \| `"false"` \| `"page"` \| `"step"` \| `"location"` \| `"date"`

Indicates the element that represents the current item within a container or set of related elements.

#### Inherited from

Omit.aria-current

#### Defined in

node_modules/@types/react/index.d.ts:1591

---

### aria-describedby

• `Optional` **aria-describedby**: `string`

Identifies the element (or elements) that describes the object.

**`see`** aria-labelledby

#### Inherited from

Omit.aria-describedby

#### Defined in

node_modules/@types/react/index.d.ts:1596

---

### aria-details

• `Optional` **aria-details**: `string`

Identifies the element that provides a detailed, extended description for the object.

**`see`** aria-describedby.

#### Inherited from

Omit.aria-details

#### Defined in

node_modules/@types/react/index.d.ts:1601

---

### aria-disabled

• `Optional` **aria-disabled**: `boolean` \| `"true"` \| `"false"`

Indicates that the element is perceivable but disabled, so it is not editable or otherwise operable.

**`see`** aria-hidden @see aria-readonly.

#### Inherited from

Omit.aria-disabled

#### Defined in

node_modules/@types/react/index.d.ts:1606

---

### aria-dropeffect

• `Optional` **aria-dropeffect**: `"link"` \| `"none"` \| `"copy"` \| `"execute"` \| `"move"` \| `"popup"`

Indicates what functions can be performed when a dragged object is released on the drop target.

**`deprecated`** in ARIA 1.1

#### Inherited from

Omit.aria-dropeffect

#### Defined in

node_modules/@types/react/index.d.ts:1611

---

### aria-errormessage

• `Optional` **aria-errormessage**: `string`

Identifies the element that provides an error message for the object.

**`see`** aria-invalid @see aria-describedby.

#### Inherited from

Omit.aria-errormessage

#### Defined in

node_modules/@types/react/index.d.ts:1616

---

### aria-expanded

• `Optional` **aria-expanded**: `boolean` \| `"true"` \| `"false"`

Indicates whether the element, or another grouping element it controls, is currently expanded or collapsed.

#### Inherited from

Omit.aria-expanded

#### Defined in

node_modules/@types/react/index.d.ts:1618

---

### aria-flowto

• `Optional` **aria-flowto**: `string`

Identifies the next element (or elements) in an alternate reading order of content which, at the user's discretion,
allows assistive technology to override the general default of reading in document source order.

#### Inherited from

Omit.aria-flowto

#### Defined in

node_modules/@types/react/index.d.ts:1623

---

### aria-grabbed

• `Optional` **aria-grabbed**: `boolean` \| `"true"` \| `"false"`

Indicates an element's "grabbed" state in a drag-and-drop operation.

**`deprecated`** in ARIA 1.1

#### Inherited from

Omit.aria-grabbed

#### Defined in

node_modules/@types/react/index.d.ts:1628

---

### aria-haspopup

• `Optional` **aria-haspopup**: `boolean` \| `"dialog"` \| `"menu"` \| `"true"` \| `"false"` \| `"listbox"` \| `"tree"` \| `"grid"`

Indicates the availability and type of interactive popup element, such as menu or dialog, that can be triggered by an element.

#### Inherited from

Omit.aria-haspopup

#### Defined in

node_modules/@types/react/index.d.ts:1630

---

### aria-hidden

• `Optional` **aria-hidden**: `boolean` \| `"true"` \| `"false"`

Indicates whether the element is exposed to an accessibility API.

**`see`** aria-disabled.

#### Inherited from

Omit.aria-hidden

#### Defined in

node_modules/@types/react/index.d.ts:1635

---

### aria-invalid

• `Optional` **aria-invalid**: `boolean` \| `"true"` \| `"false"` \| `"grammar"` \| `"spelling"`

Indicates the entered value does not conform to the format expected by the application.

**`see`** aria-errormessage.

#### Inherited from

Omit.aria-invalid

#### Defined in

node_modules/@types/react/index.d.ts:1640

---

### aria-keyshortcuts

• `Optional` **aria-keyshortcuts**: `string`

Indicates keyboard shortcuts that an author has implemented to activate or give focus to an element.

#### Inherited from

Omit.aria-keyshortcuts

#### Defined in

node_modules/@types/react/index.d.ts:1642

---

### aria-label

• `Optional` **aria-label**: `string`

Defines a string value that labels the current element.

**`see`** aria-labelledby.

#### Inherited from

Omit.aria-label

#### Defined in

node_modules/@types/react/index.d.ts:1647

---

### aria-labelledby

• `Optional` **aria-labelledby**: `string`

Identifies the element (or elements) that labels the current element.

**`see`** aria-describedby.

#### Inherited from

Omit.aria-labelledby

#### Defined in

node_modules/@types/react/index.d.ts:1652

---

### aria-level

• `Optional` **aria-level**: `number`

Defines the hierarchical level of an element within a structure.

#### Inherited from

Omit.aria-level

#### Defined in

node_modules/@types/react/index.d.ts:1654

---

### aria-live

• `Optional` **aria-live**: `"off"` \| `"assertive"` \| `"polite"`

Indicates that an element will be updated, and describes the types of updates the user agents, assistive technologies, and user can expect from the live region.

#### Inherited from

Omit.aria-live

#### Defined in

node_modules/@types/react/index.d.ts:1656

---

### aria-modal

• `Optional` **aria-modal**: `boolean` \| `"true"` \| `"false"`

Indicates whether an element is modal when displayed.

#### Inherited from

Omit.aria-modal

#### Defined in

node_modules/@types/react/index.d.ts:1658

---

### aria-multiline

• `Optional` **aria-multiline**: `boolean` \| `"true"` \| `"false"`

Indicates whether a text box accepts multiple lines of input or only a single line.

#### Inherited from

Omit.aria-multiline

#### Defined in

node_modules/@types/react/index.d.ts:1660

---

### aria-multiselectable

• `Optional` **aria-multiselectable**: `boolean` \| `"true"` \| `"false"`

Indicates that the user may select more than one item from the current selectable descendants.

#### Inherited from

Omit.aria-multiselectable

#### Defined in

node_modules/@types/react/index.d.ts:1662

---

### aria-orientation

• `Optional` **aria-orientation**: `"horizontal"` \| `"vertical"`

Indicates whether the element's orientation is horizontal, vertical, or unknown/ambiguous.

#### Inherited from

Omit.aria-orientation

#### Defined in

node_modules/@types/react/index.d.ts:1664

---

### aria-owns

• `Optional` **aria-owns**: `string`

Identifies an element (or elements) in order to define a visual, functional, or contextual parent/child relationship
between DOM elements where the DOM hierarchy cannot be used to represent the relationship.

**`see`** aria-controls.

#### Inherited from

Omit.aria-owns

#### Defined in

node_modules/@types/react/index.d.ts:1670

---

### aria-placeholder

• `Optional` **aria-placeholder**: `string`

Defines a short hint (a word or short phrase) intended to aid the user with data entry when the control has no value.
A hint could be a sample value or a brief description of the expected format.

#### Inherited from

Omit.aria-placeholder

#### Defined in

node_modules/@types/react/index.d.ts:1675

---

### aria-posinset

• `Optional` **aria-posinset**: `number`

Defines an element's number or position in the current set of listitems or treeitems. Not required if all elements in the set are present in the DOM.

**`see`** aria-setsize.

#### Inherited from

Omit.aria-posinset

#### Defined in

node_modules/@types/react/index.d.ts:1680

---

### aria-pressed

• `Optional` **aria-pressed**: `boolean` \| `"true"` \| `"false"` \| `"mixed"`

Indicates the current "pressed" state of toggle buttons.

**`see`** aria-checked @see aria-selected.

#### Inherited from

Omit.aria-pressed

#### Defined in

node_modules/@types/react/index.d.ts:1685

---

### aria-readonly

• `Optional` **aria-readonly**: `boolean` \| `"true"` \| `"false"`

Indicates that the element is not editable, but is otherwise operable.

**`see`** aria-disabled.

#### Inherited from

Omit.aria-readonly

#### Defined in

node_modules/@types/react/index.d.ts:1690

---

### aria-relevant

• `Optional` **aria-relevant**: `"text"` \| `"additions"` \| `"additions removals"` \| `"additions text"` \| `"all"` \| `"removals"` \| `"removals additions"` \| `"removals text"` \| `"text additions"` \| `"text removals"`

Indicates what notifications the user agent will trigger when the accessibility tree within a live region is modified.

**`see`** aria-atomic.

#### Inherited from

Omit.aria-relevant

#### Defined in

node_modules/@types/react/index.d.ts:1695

---

### aria-required

• `Optional` **aria-required**: `boolean` \| `"true"` \| `"false"`

Indicates that user input is required on the element before a form may be submitted.

#### Inherited from

Omit.aria-required

#### Defined in

node_modules/@types/react/index.d.ts:1697

---

### aria-roledescription

• `Optional` **aria-roledescription**: `string`

Defines a human-readable, author-localized description for the role of an element.

#### Inherited from

Omit.aria-roledescription

#### Defined in

node_modules/@types/react/index.d.ts:1699

---

### aria-rowcount

• `Optional` **aria-rowcount**: `number`

Defines the total number of rows in a table, grid, or treegrid.

**`see`** aria-rowindex.

#### Inherited from

Omit.aria-rowcount

#### Defined in

node_modules/@types/react/index.d.ts:1704

---

### aria-rowindex

• `Optional` **aria-rowindex**: `number`

Defines an element's row index or position with respect to the total number of rows within a table, grid, or treegrid.

**`see`** aria-rowcount @see aria-rowspan.

#### Inherited from

Omit.aria-rowindex

#### Defined in

node_modules/@types/react/index.d.ts:1709

---

### aria-rowspan

• `Optional` **aria-rowspan**: `number`

Defines the number of rows spanned by a cell or gridcell within a table, grid, or treegrid.

**`see`** aria-rowindex @see aria-colspan.

#### Inherited from

Omit.aria-rowspan

#### Defined in

node_modules/@types/react/index.d.ts:1714

---

### aria-selected

• `Optional` **aria-selected**: `boolean` \| `"true"` \| `"false"`

Indicates the current "selected" state of various widgets.

**`see`** aria-checked @see aria-pressed.

#### Inherited from

Omit.aria-selected

#### Defined in

node_modules/@types/react/index.d.ts:1719

---

### aria-setsize

• `Optional` **aria-setsize**: `number`

Defines the number of items in the current set of listitems or treeitems. Not required if all elements in the set are present in the DOM.

**`see`** aria-posinset.

#### Inherited from

Omit.aria-setsize

#### Defined in

node_modules/@types/react/index.d.ts:1724

---

### aria-sort

• `Optional` **aria-sort**: `"none"` \| `"ascending"` \| `"descending"` \| `"other"`

Indicates if items in a table or grid are sorted in ascending or descending order.

#### Inherited from

Omit.aria-sort

#### Defined in

node_modules/@types/react/index.d.ts:1726

---

### aria-valuemax

• `Optional` **aria-valuemax**: `number`

Defines the maximum allowed value for a range widget.

#### Inherited from

Omit.aria-valuemax

#### Defined in

node_modules/@types/react/index.d.ts:1728

---

### aria-valuemin

• `Optional` **aria-valuemin**: `number`

Defines the minimum allowed value for a range widget.

#### Inherited from

Omit.aria-valuemin

#### Defined in

node_modules/@types/react/index.d.ts:1730

---

### aria-valuenow

• `Optional` **aria-valuenow**: `number`

Defines the current value for a range widget.

**`see`** aria-valuetext.

#### Inherited from

Omit.aria-valuenow

#### Defined in

node_modules/@types/react/index.d.ts:1735

---

### aria-valuetext

• `Optional` **aria-valuetext**: `string`

Defines the human readable text alternative of aria-valuenow for a range widget.

#### Inherited from

Omit.aria-valuetext

#### Defined in

node_modules/@types/react/index.d.ts:1737

---

### autoCapitalize

• `Optional` **autoCapitalize**: `string`

#### Inherited from

Omit.autoCapitalize

#### Defined in

node_modules/@types/react/index.d.ts:1782

---

### autoComplete

• `Optional` **autoComplete**: `string`

#### Inherited from

Omit.autoComplete

#### Defined in

node_modules/@types/react/index.d.ts:2084

---

### autoCorrect

• `Optional` **autoCorrect**: `string`

#### Inherited from

Omit.autoCorrect

#### Defined in

node_modules/@types/react/index.d.ts:1783

---

### autoFocus

• `Optional` **autoFocus**: `boolean`

#### Inherited from

Omit.autoFocus

#### Defined in

node_modules/@types/react/index.d.ts:2085

---

### autoSave

• `Optional` **autoSave**: `string`

#### Inherited from

Omit.autoSave

#### Defined in

node_modules/@types/react/index.d.ts:1784

---

### capture

• `Optional` **capture**: `string` \| `boolean`

#### Inherited from

Omit.capture

#### Defined in

node_modules/@types/react/index.d.ts:2086

---

### checked

• `Optional` **checked**: `boolean`

#### Inherited from

Omit.checked

#### Defined in

node_modules/@types/react/index.d.ts:2087

---

### children

• `Optional` **children**: `ReactNode`

#### Inherited from

Omit.children

#### Defined in

node_modules/@types/react/index.d.ts:1345

---

### className

• `Optional` **className**: `string`

#### Overrides

Omit.className

#### Defined in

[src/components/input/input.tsx:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L23)

---

### color

• `Optional` **color**: `string`

#### Inherited from

Omit.color

#### Defined in

node_modules/@types/react/index.d.ts:1785

---

### contentEditable

• `Optional` **contentEditable**: `Booleanish` \| `"inherit"`

#### Inherited from

Omit.contentEditable

#### Defined in

node_modules/@types/react/index.d.ts:1750

---

### contextMenu

• `Optional` **contextMenu**: `string`

#### Inherited from

Omit.contextMenu

#### Defined in

node_modules/@types/react/index.d.ts:1751

---

### crossOrigin

• `Optional` **crossOrigin**: `string`

#### Inherited from

Omit.crossOrigin

#### Defined in

node_modules/@types/react/index.d.ts:2088

---

### css

• `Optional` **css**: `InterpolationWithTheme`<`any`\>

#### Inherited from

Omit.css

#### Defined in

node_modules/@emotion/core/types/index.d.ts:84

---

### dangerouslySetInnerHTML

• `Optional` **dangerouslySetInnerHTML**: `Object`

#### Type declaration

| Name     | Type     |
| :------- | :------- |
| `__html` | `string` |

#### Inherited from

Omit.dangerouslySetInnerHTML

#### Defined in

node_modules/@types/react/index.d.ts:1346

---

### datatype

• `Optional` **datatype**: `string`

#### Inherited from

Omit.datatype

#### Defined in

node_modules/@types/react/index.d.ts:1773

---

### defaultChecked

• `Optional` **defaultChecked**: `boolean`

#### Inherited from

Omit.defaultChecked

#### Defined in

node_modules/@types/react/index.d.ts:1742

---

### defaultValue

• `Optional` **defaultValue**: `string`

#### Overrides

Omit.defaultValue

#### Defined in

[src/components/input/input.tsx:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L22)

---

### dir

• `Optional` **dir**: `string`

#### Inherited from

Omit.dir

#### Defined in

node_modules/@types/react/index.d.ts:1752

---

### disabled

• `Optional` **disabled**: `boolean`

#### Overrides

Omit.disabled

#### Defined in

[src/components/input/input.tsx:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L13)

---

### draggable

• `Optional` **draggable**: `Booleanish`

#### Inherited from

Omit.draggable

#### Defined in

node_modules/@types/react/index.d.ts:1753

---

### enterKeyHint

• `Optional` **enterKeyHint**: `"search"` \| `"enter"` \| `"done"` \| `"go"` \| `"next"` \| `"previous"` \| `"send"`

#### Inherited from

Omit.enterKeyHint

#### Defined in

node_modules/@types/react/index.d.ts:2090

---

### form

• `Optional` **form**: `string`

#### Inherited from

Omit.form

#### Defined in

node_modules/@types/react/index.d.ts:2091

---

### formAction

• `Optional` **formAction**: `string`

#### Inherited from

Omit.formAction

#### Defined in

node_modules/@types/react/index.d.ts:2092

---

### formEncType

• `Optional` **formEncType**: `string`

#### Inherited from

Omit.formEncType

#### Defined in

node_modules/@types/react/index.d.ts:2093

---

### formMethod

• `Optional` **formMethod**: `string`

#### Inherited from

Omit.formMethod

#### Defined in

node_modules/@types/react/index.d.ts:2094

---

### formNoValidate

• `Optional` **formNoValidate**: `boolean`

#### Inherited from

Omit.formNoValidate

#### Defined in

node_modules/@types/react/index.d.ts:2095

---

### formTarget

• `Optional` **formTarget**: `string`

#### Inherited from

Omit.formTarget

#### Defined in

node_modules/@types/react/index.d.ts:2096

---

### height

• `Optional` **height**: `string` \| `number`

#### Inherited from

Omit.height

#### Defined in

node_modules/@types/react/index.d.ts:2097

---

### hidden

• `Optional` **hidden**: `boolean`

#### Inherited from

Omit.hidden

#### Defined in

node_modules/@types/react/index.d.ts:1754

---

### id

• `Optional` **id**: `string`

#### Inherited from

Omit.id

#### Defined in

node_modules/@types/react/index.d.ts:1755

---

### inlist

• `Optional` **inlist**: `any`

#### Inherited from

Omit.inlist

#### Defined in

node_modules/@types/react/index.d.ts:1774

---

### inputMode

• `Optional` **inputMode**: `"text"` \| `"none"` \| `"tel"` \| `"url"` \| `"email"` \| `"numeric"` \| `"decimal"` \| `"search"`

Hints at the type of data that might be entered by the user while editing the element or its contents

**`see`** https://html.spec.whatwg.org/multipage/interaction.html#input-modalities:-the-inputmode-attribute

#### Inherited from

Omit.inputMode

#### Defined in

node_modules/@types/react/index.d.ts:1800

---

### is

• `Optional` **is**: `string`

Specify that a standard HTML element should behave like a defined custom built-in element

**`see`** https://html.spec.whatwg.org/multipage/custom-elements.html#attr-is

#### Inherited from

Omit.is

#### Defined in

node_modules/@types/react/index.d.ts:1805

---

### itemID

• `Optional` **itemID**: `string`

#### Inherited from

Omit.itemID

#### Defined in

node_modules/@types/react/index.d.ts:1789

---

### itemProp

• `Optional` **itemProp**: `string`

#### Inherited from

Omit.itemProp

#### Defined in

node_modules/@types/react/index.d.ts:1786

---

### itemRef

• `Optional` **itemRef**: `string`

#### Inherited from

Omit.itemRef

#### Defined in

node_modules/@types/react/index.d.ts:1790

---

### itemScope

• `Optional` **itemScope**: `boolean`

#### Inherited from

Omit.itemScope

#### Defined in

node_modules/@types/react/index.d.ts:1787

---

### itemType

• `Optional` **itemType**: `string`

#### Inherited from

Omit.itemType

#### Defined in

node_modules/@types/react/index.d.ts:1788

---

### lang

• `Optional` **lang**: `string`

#### Inherited from

Omit.lang

#### Defined in

node_modules/@types/react/index.d.ts:1756

---

### list

• `Optional` **list**: `string`

#### Inherited from

Omit.list

#### Defined in

node_modules/@types/react/index.d.ts:2098

---

### max

• `Optional` **max**: `string` \| `number`

#### Inherited from

Omit.max

#### Defined in

node_modules/@types/react/index.d.ts:2099

---

### maxLength

• `Optional` **maxLength**: `number`

#### Inherited from

Omit.maxLength

#### Defined in

node_modules/@types/react/index.d.ts:2100

---

### min

• `Optional` **min**: `string` \| `number`

#### Inherited from

Omit.min

#### Defined in

node_modules/@types/react/index.d.ts:2101

---

### minLength

• `Optional` **minLength**: `number`

#### Inherited from

Omit.minLength

#### Defined in

node_modules/@types/react/index.d.ts:2102

---

### multiple

• `Optional` **multiple**: `boolean`

#### Inherited from

Omit.multiple

#### Defined in

node_modules/@types/react/index.d.ts:2103

---

### name

• `Optional` **name**: `string`

#### Inherited from

Omit.name

#### Defined in

node_modules/@types/react/index.d.ts:2104

---

### onAbort

• `Optional` **onAbort**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAbort

#### Defined in

node_modules/@types/react/index.d.ts:1401

---

### onAbortCapture

• `Optional` **onAbortCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAbortCapture

#### Defined in

node_modules/@types/react/index.d.ts:1402

---

### onAnimationEnd

• `Optional` **onAnimationEnd**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationEnd

#### Defined in

node_modules/@types/react/index.d.ts:1531

---

### onAnimationEndCapture

• `Optional` **onAnimationEndCapture**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1532

---

### onAnimationIteration

• `Optional` **onAnimationIteration**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationIteration

#### Defined in

node_modules/@types/react/index.d.ts:1533

---

### onAnimationIterationCapture

• `Optional` **onAnimationIterationCapture**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationIterationCapture

#### Defined in

node_modules/@types/react/index.d.ts:1534

---

### onAnimationStart

• `Optional` **onAnimationStart**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationStart

#### Defined in

node_modules/@types/react/index.d.ts:1529

---

### onAnimationStartCapture

• `Optional` **onAnimationStartCapture**: `AnimationEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAnimationStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1530

---

### onAuxClick

• `Optional` **onAuxClick**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAuxClick

#### Defined in

node_modules/@types/react/index.d.ts:1447

---

### onAuxClickCapture

• `Optional` **onAuxClickCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onAuxClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1448

---

### onBeforeInput

• `Optional` **onBeforeInput**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onBeforeInput

#### Defined in

node_modules/@types/react/index.d.ts:1375

---

### onBeforeInputCapture

• `Optional` **onBeforeInputCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onBeforeInputCapture

#### Defined in

node_modules/@types/react/index.d.ts:1376

---

### onBlur

• `Optional` **onBlur**: `FocusEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onBlur

#### Defined in

node_modules/@types/react/index.d.ts:1369

---

### onBlurCapture

• `Optional` **onBlurCapture**: `FocusEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onBlurCapture

#### Defined in

node_modules/@types/react/index.d.ts:1370

---

### onCanPlay

• `Optional` **onCanPlay**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCanPlay

#### Defined in

node_modules/@types/react/index.d.ts:1403

---

### onCanPlayCapture

• `Optional` **onCanPlayCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCanPlayCapture

#### Defined in

node_modules/@types/react/index.d.ts:1404

---

### onCanPlayThrough

• `Optional` **onCanPlayThrough**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCanPlayThrough

#### Defined in

node_modules/@types/react/index.d.ts:1405

---

### onCanPlayThroughCapture

• `Optional` **onCanPlayThroughCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCanPlayThroughCapture

#### Defined in

node_modules/@types/react/index.d.ts:1406

---

### onChangeCapture

• `Optional` **onChangeCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1374

---

### onClick

• `Optional` **onClick**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onClick

#### Defined in

node_modules/@types/react/index.d.ts:1449

---

### onClickCapture

• `Optional` **onClickCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1450

---

### onCompositionEnd

• `Optional` **onCompositionEnd**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionEnd

#### Defined in

node_modules/@types/react/index.d.ts:1359

---

### onCompositionEndCapture

• `Optional` **onCompositionEndCapture**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1360

---

### onCompositionStart

• `Optional` **onCompositionStart**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionStart

#### Defined in

node_modules/@types/react/index.d.ts:1361

---

### onCompositionStartCapture

• `Optional` **onCompositionStartCapture**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1362

---

### onCompositionUpdate

• `Optional` **onCompositionUpdate**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionUpdate

#### Defined in

node_modules/@types/react/index.d.ts:1363

---

### onCompositionUpdateCapture

• `Optional` **onCompositionUpdateCapture**: `CompositionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCompositionUpdateCapture

#### Defined in

node_modules/@types/react/index.d.ts:1364

---

### onContextMenu

• `Optional` **onContextMenu**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onContextMenu

#### Defined in

node_modules/@types/react/index.d.ts:1451

---

### onContextMenuCapture

• `Optional` **onContextMenuCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onContextMenuCapture

#### Defined in

node_modules/@types/react/index.d.ts:1452

---

### onCopy

• `Optional` **onCopy**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCopy

#### Defined in

node_modules/@types/react/index.d.ts:1351

---

### onCopyCapture

• `Optional` **onCopyCapture**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCopyCapture

#### Defined in

node_modules/@types/react/index.d.ts:1352

---

### onCut

• `Optional` **onCut**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCut

#### Defined in

node_modules/@types/react/index.d.ts:1353

---

### onCutCapture

• `Optional` **onCutCapture**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onCutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1354

---

### onDoubleClick

• `Optional` **onDoubleClick**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDoubleClick

#### Defined in

node_modules/@types/react/index.d.ts:1453

---

### onDoubleClickCapture

• `Optional` **onDoubleClickCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDoubleClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1454

---

### onDrag

• `Optional` **onDrag**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDrag

#### Defined in

node_modules/@types/react/index.d.ts:1455

---

### onDragCapture

• `Optional` **onDragCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragCapture

#### Defined in

node_modules/@types/react/index.d.ts:1456

---

### onDragEnd

• `Optional` **onDragEnd**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragEnd

#### Defined in

node_modules/@types/react/index.d.ts:1457

---

### onDragEndCapture

• `Optional` **onDragEndCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1458

---

### onDragEnter

• `Optional` **onDragEnter**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragEnter

#### Defined in

node_modules/@types/react/index.d.ts:1459

---

### onDragEnterCapture

• `Optional` **onDragEnterCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragEnterCapture

#### Defined in

node_modules/@types/react/index.d.ts:1460

---

### onDragExit

• `Optional` **onDragExit**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragExit

#### Defined in

node_modules/@types/react/index.d.ts:1461

---

### onDragExitCapture

• `Optional` **onDragExitCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragExitCapture

#### Defined in

node_modules/@types/react/index.d.ts:1462

---

### onDragLeave

• `Optional` **onDragLeave**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragLeave

#### Defined in

node_modules/@types/react/index.d.ts:1463

---

### onDragLeaveCapture

• `Optional` **onDragLeaveCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragLeaveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1464

---

### onDragOver

• `Optional` **onDragOver**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragOver

#### Defined in

node_modules/@types/react/index.d.ts:1465

---

### onDragOverCapture

• `Optional` **onDragOverCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1466

---

### onDragStart

• `Optional` **onDragStart**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragStart

#### Defined in

node_modules/@types/react/index.d.ts:1467

---

### onDragStartCapture

• `Optional` **onDragStartCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDragStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1468

---

### onDrop

• `Optional` **onDrop**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDrop

#### Defined in

node_modules/@types/react/index.d.ts:1469

---

### onDropCapture

• `Optional` **onDropCapture**: `DragEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDropCapture

#### Defined in

node_modules/@types/react/index.d.ts:1470

---

### onDurationChange

• `Optional` **onDurationChange**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDurationChange

#### Defined in

node_modules/@types/react/index.d.ts:1407

---

### onDurationChangeCapture

• `Optional` **onDurationChangeCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onDurationChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1408

---

### onEmptied

• `Optional` **onEmptied**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEmptied

#### Defined in

node_modules/@types/react/index.d.ts:1409

---

### onEmptiedCapture

• `Optional` **onEmptiedCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEmptiedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1410

---

### onEncrypted

• `Optional` **onEncrypted**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEncrypted

#### Defined in

node_modules/@types/react/index.d.ts:1411

---

### onEncryptedCapture

• `Optional` **onEncryptedCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEncryptedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1412

---

### onEnded

• `Optional` **onEnded**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEnded

#### Defined in

node_modules/@types/react/index.d.ts:1413

---

### onEndedCapture

• `Optional` **onEndedCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onEndedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1414

---

### onError

• `Optional` **onError**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onError

#### Defined in

node_modules/@types/react/index.d.ts:1389

---

### onErrorCapture

• `Optional` **onErrorCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onErrorCapture

#### Defined in

node_modules/@types/react/index.d.ts:1390

---

### onFocus

• `Optional` **onFocus**: `FocusEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onFocus

#### Defined in

node_modules/@types/react/index.d.ts:1367

---

### onFocusCapture

• `Optional` **onFocusCapture**: `FocusEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onFocusCapture

#### Defined in

node_modules/@types/react/index.d.ts:1368

---

### onGotPointerCapture

• `Optional` **onGotPointerCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onGotPointerCapture

#### Defined in

node_modules/@types/react/index.d.ts:1515

---

### onGotPointerCaptureCapture

• `Optional` **onGotPointerCaptureCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onGotPointerCaptureCapture

#### Defined in

node_modules/@types/react/index.d.ts:1516

---

### onInput

• `Optional` **onInput**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onInput

#### Defined in

node_modules/@types/react/index.d.ts:1377

---

### onInputCapture

• `Optional` **onInputCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onInputCapture

#### Defined in

node_modules/@types/react/index.d.ts:1378

---

### onInvalid

• `Optional` **onInvalid**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onInvalid

#### Defined in

node_modules/@types/react/index.d.ts:1383

---

### onInvalidCapture

• `Optional` **onInvalidCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onInvalidCapture

#### Defined in

node_modules/@types/react/index.d.ts:1384

---

### onKeyDownCapture

• `Optional` **onKeyDownCapture**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onKeyDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1394

---

### onKeyPress

• `Optional` **onKeyPress**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onKeyPress

#### Defined in

node_modules/@types/react/index.d.ts:1395

---

### onKeyPressCapture

• `Optional` **onKeyPressCapture**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onKeyPressCapture

#### Defined in

node_modules/@types/react/index.d.ts:1396

---

### onKeyUp

• `Optional` **onKeyUp**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onKeyUp

#### Defined in

node_modules/@types/react/index.d.ts:1397

---

### onKeyUpCapture

• `Optional` **onKeyUpCapture**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onKeyUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1398

---

### onLoad

• `Optional` **onLoad**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoad

#### Defined in

node_modules/@types/react/index.d.ts:1387

---

### onLoadCapture

• `Optional` **onLoadCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadCapture

#### Defined in

node_modules/@types/react/index.d.ts:1388

---

### onLoadStart

• `Optional` **onLoadStart**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadStart

#### Defined in

node_modules/@types/react/index.d.ts:1419

---

### onLoadStartCapture

• `Optional` **onLoadStartCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1420

---

### onLoadedData

• `Optional` **onLoadedData**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadedData

#### Defined in

node_modules/@types/react/index.d.ts:1415

---

### onLoadedDataCapture

• `Optional` **onLoadedDataCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadedDataCapture

#### Defined in

node_modules/@types/react/index.d.ts:1416

---

### onLoadedMetadata

• `Optional` **onLoadedMetadata**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadedMetadata

#### Defined in

node_modules/@types/react/index.d.ts:1417

---

### onLoadedMetadataCapture

• `Optional` **onLoadedMetadataCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLoadedMetadataCapture

#### Defined in

node_modules/@types/react/index.d.ts:1418

---

### onLostPointerCapture

• `Optional` **onLostPointerCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLostPointerCapture

#### Defined in

node_modules/@types/react/index.d.ts:1517

---

### onLostPointerCaptureCapture

• `Optional` **onLostPointerCaptureCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onLostPointerCaptureCapture

#### Defined in

node_modules/@types/react/index.d.ts:1518

---

### onMouseDown

• `Optional` **onMouseDown**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseDown

#### Defined in

node_modules/@types/react/index.d.ts:1471

---

### onMouseDownCapture

• `Optional` **onMouseDownCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1472

---

### onMouseEnter

• `Optional` **onMouseEnter**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseEnter

#### Defined in

node_modules/@types/react/index.d.ts:1473

---

### onMouseLeave

• `Optional` **onMouseLeave**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseLeave

#### Defined in

node_modules/@types/react/index.d.ts:1474

---

### onMouseMove

• `Optional` **onMouseMove**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseMove

#### Defined in

node_modules/@types/react/index.d.ts:1475

---

### onMouseMoveCapture

• `Optional` **onMouseMoveCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1476

---

### onMouseOut

• `Optional` **onMouseOut**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseOut

#### Defined in

node_modules/@types/react/index.d.ts:1477

---

### onMouseOutCapture

• `Optional` **onMouseOutCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseOutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1478

---

### onMouseOver

• `Optional` **onMouseOver**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseOver

#### Defined in

node_modules/@types/react/index.d.ts:1479

---

### onMouseOverCapture

• `Optional` **onMouseOverCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1480

---

### onMouseUp

• `Optional` **onMouseUp**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseUp

#### Defined in

node_modules/@types/react/index.d.ts:1481

---

### onMouseUpCapture

• `Optional` **onMouseUpCapture**: `MouseEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onMouseUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1482

---

### onPaste

• `Optional` **onPaste**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPaste

#### Defined in

node_modules/@types/react/index.d.ts:1355

---

### onPasteCapture

• `Optional` **onPasteCapture**: `ClipboardEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPasteCapture

#### Defined in

node_modules/@types/react/index.d.ts:1356

---

### onPause

• `Optional` **onPause**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPause

#### Defined in

node_modules/@types/react/index.d.ts:1421

---

### onPauseCapture

• `Optional` **onPauseCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPauseCapture

#### Defined in

node_modules/@types/react/index.d.ts:1422

---

### onPlay

• `Optional` **onPlay**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPlay

#### Defined in

node_modules/@types/react/index.d.ts:1423

---

### onPlayCapture

• `Optional` **onPlayCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPlayCapture

#### Defined in

node_modules/@types/react/index.d.ts:1424

---

### onPlaying

• `Optional` **onPlaying**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPlaying

#### Defined in

node_modules/@types/react/index.d.ts:1425

---

### onPlayingCapture

• `Optional` **onPlayingCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPlayingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1426

---

### onPointerCancel

• `Optional` **onPointerCancel**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerCancel

#### Defined in

node_modules/@types/react/index.d.ts:1505

---

### onPointerCancelCapture

• `Optional` **onPointerCancelCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerCancelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1506

---

### onPointerDown

• `Optional` **onPointerDown**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerDown

#### Defined in

node_modules/@types/react/index.d.ts:1499

---

### onPointerDownCapture

• `Optional` **onPointerDownCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1500

---

### onPointerEnter

• `Optional` **onPointerEnter**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerEnter

#### Defined in

node_modules/@types/react/index.d.ts:1507

---

### onPointerEnterCapture

• `Optional` **onPointerEnterCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerEnterCapture

#### Defined in

node_modules/@types/react/index.d.ts:1508

---

### onPointerLeave

• `Optional` **onPointerLeave**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerLeave

#### Defined in

node_modules/@types/react/index.d.ts:1509

---

### onPointerLeaveCapture

• `Optional` **onPointerLeaveCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerLeaveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1510

---

### onPointerMove

• `Optional` **onPointerMove**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerMove

#### Defined in

node_modules/@types/react/index.d.ts:1501

---

### onPointerMoveCapture

• `Optional` **onPointerMoveCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1502

---

### onPointerOut

• `Optional` **onPointerOut**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerOut

#### Defined in

node_modules/@types/react/index.d.ts:1513

---

### onPointerOutCapture

• `Optional` **onPointerOutCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerOutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1514

---

### onPointerOver

• `Optional` **onPointerOver**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerOver

#### Defined in

node_modules/@types/react/index.d.ts:1511

---

### onPointerOverCapture

• `Optional` **onPointerOverCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1512

---

### onPointerUp

• `Optional` **onPointerUp**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerUp

#### Defined in

node_modules/@types/react/index.d.ts:1503

---

### onPointerUpCapture

• `Optional` **onPointerUpCapture**: `PointerEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onPointerUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1504

---

### onPressEnter

• `Optional` **onPressEnter**: `KeyboardEventHandler`<`HTMLInputElement`\>

#### Defined in

[src/components/input/input.tsx:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L24)

---

### onProgress

• `Optional` **onProgress**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onProgress

#### Defined in

node_modules/@types/react/index.d.ts:1427

---

### onProgressCapture

• `Optional` **onProgressCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onProgressCapture

#### Defined in

node_modules/@types/react/index.d.ts:1428

---

### onRateChange

• `Optional` **onRateChange**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onRateChange

#### Defined in

node_modules/@types/react/index.d.ts:1429

---

### onRateChangeCapture

• `Optional` **onRateChangeCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onRateChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1430

---

### onReset

• `Optional` **onReset**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onReset

#### Defined in

node_modules/@types/react/index.d.ts:1379

---

### onResetCapture

• `Optional` **onResetCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onResetCapture

#### Defined in

node_modules/@types/react/index.d.ts:1380

---

### onScroll

• `Optional` **onScroll**: `UIEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onScroll

#### Defined in

node_modules/@types/react/index.d.ts:1521

---

### onScrollCapture

• `Optional` **onScrollCapture**: `UIEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onScrollCapture

#### Defined in

node_modules/@types/react/index.d.ts:1522

---

### onSeeked

• `Optional` **onSeeked**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSeeked

#### Defined in

node_modules/@types/react/index.d.ts:1431

---

### onSeekedCapture

• `Optional` **onSeekedCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSeekedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1432

---

### onSeeking

• `Optional` **onSeeking**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSeeking

#### Defined in

node_modules/@types/react/index.d.ts:1433

---

### onSeekingCapture

• `Optional` **onSeekingCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSeekingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1434

---

### onSelect

• `Optional` **onSelect**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSelect

#### Defined in

node_modules/@types/react/index.d.ts:1485

---

### onSelectCapture

• `Optional` **onSelectCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSelectCapture

#### Defined in

node_modules/@types/react/index.d.ts:1486

---

### onStalled

• `Optional` **onStalled**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onStalled

#### Defined in

node_modules/@types/react/index.d.ts:1435

---

### onStalledCapture

• `Optional` **onStalledCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onStalledCapture

#### Defined in

node_modules/@types/react/index.d.ts:1436

---

### onSubmit

• `Optional` **onSubmit**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSubmit

#### Defined in

node_modules/@types/react/index.d.ts:1381

---

### onSubmitCapture

• `Optional` **onSubmitCapture**: `FormEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSubmitCapture

#### Defined in

node_modules/@types/react/index.d.ts:1382

---

### onSuspend

• `Optional` **onSuspend**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSuspend

#### Defined in

node_modules/@types/react/index.d.ts:1437

---

### onSuspendCapture

• `Optional` **onSuspendCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onSuspendCapture

#### Defined in

node_modules/@types/react/index.d.ts:1438

---

### onTimeUpdate

• `Optional` **onTimeUpdate**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTimeUpdate

#### Defined in

node_modules/@types/react/index.d.ts:1439

---

### onTimeUpdateCapture

• `Optional` **onTimeUpdateCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTimeUpdateCapture

#### Defined in

node_modules/@types/react/index.d.ts:1440

---

### onTouchCancel

• `Optional` **onTouchCancel**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchCancel

#### Defined in

node_modules/@types/react/index.d.ts:1489

---

### onTouchCancelCapture

• `Optional` **onTouchCancelCapture**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchCancelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1490

---

### onTouchEnd

• `Optional` **onTouchEnd**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchEnd

#### Defined in

node_modules/@types/react/index.d.ts:1491

---

### onTouchEndCapture

• `Optional` **onTouchEndCapture**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1492

---

### onTouchMove

• `Optional` **onTouchMove**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchMove

#### Defined in

node_modules/@types/react/index.d.ts:1493

---

### onTouchMoveCapture

• `Optional` **onTouchMoveCapture**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1494

---

### onTouchStart

• `Optional` **onTouchStart**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchStart

#### Defined in

node_modules/@types/react/index.d.ts:1495

---

### onTouchStartCapture

• `Optional` **onTouchStartCapture**: `TouchEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTouchStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1496

---

### onTransitionEnd

• `Optional` **onTransitionEnd**: `TransitionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTransitionEnd

#### Defined in

node_modules/@types/react/index.d.ts:1537

---

### onTransitionEndCapture

• `Optional` **onTransitionEndCapture**: `TransitionEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onTransitionEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1538

---

### onVolumeChange

• `Optional` **onVolumeChange**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onVolumeChange

#### Defined in

node_modules/@types/react/index.d.ts:1441

---

### onVolumeChangeCapture

• `Optional` **onVolumeChangeCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onVolumeChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1442

---

### onWaiting

• `Optional` **onWaiting**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onWaiting

#### Defined in

node_modules/@types/react/index.d.ts:1443

---

### onWaitingCapture

• `Optional` **onWaitingCapture**: `ReactEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onWaitingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1444

---

### onWheel

• `Optional` **onWheel**: `WheelEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onWheel

#### Defined in

node_modules/@types/react/index.d.ts:1525

---

### onWheelCapture

• `Optional` **onWheelCapture**: `WheelEventHandler`<`HTMLInputElement`\>

#### Inherited from

Omit.onWheelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1526

---

### pattern

• `Optional` **pattern**: `string`

#### Inherited from

Omit.pattern

#### Defined in

node_modules/@types/react/index.d.ts:2105

---

### placeholder

• `Optional` **placeholder**: `string`

#### Overrides

Omit.placeholder

#### Defined in

[src/components/input/input.tsx:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L19)

---

### prefix

• `Optional` **prefix**: `string`

#### Inherited from

Omit.prefix

#### Defined in

node_modules/@types/react/index.d.ts:1775

---

### property

• `Optional` **property**: `string`

#### Inherited from

Omit.property

#### Defined in

node_modules/@types/react/index.d.ts:1776

---

### radioGroup

• `Optional` **radioGroup**: `string`

#### Inherited from

Omit.radioGroup

#### Defined in

node_modules/@types/react/index.d.ts:1766

---

### readOnly

• `Optional` **readOnly**: `boolean`

#### Inherited from

Omit.readOnly

#### Defined in

node_modules/@types/react/index.d.ts:2107

---

### required

• `Optional` **required**: `boolean`

#### Inherited from

Omit.required

#### Defined in

node_modules/@types/react/index.d.ts:2108

---

### resource

• `Optional` **resource**: `string`

#### Inherited from

Omit.resource

#### Defined in

node_modules/@types/react/index.d.ts:1777

---

### results

• `Optional` **results**: `number`

#### Inherited from

Omit.results

#### Defined in

node_modules/@types/react/index.d.ts:1791

---

### role

• `Optional` **role**: `string`

#### Inherited from

Omit.role

#### Defined in

node_modules/@types/react/index.d.ts:1769

---

### security

• `Optional` **security**: `string`

#### Inherited from

Omit.security

#### Defined in

node_modules/@types/react/index.d.ts:1792

---

### size

• `Optional` **size**: `SizeType`

#### Defined in

[src/components/input/input.tsx:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L14)

---

### slot

• `Optional` **slot**: `string`

#### Inherited from

Omit.slot

#### Defined in

node_modules/@types/react/index.d.ts:1758

---

### spellCheck

• `Optional` **spellCheck**: `Booleanish`

#### Inherited from

Omit.spellCheck

#### Defined in

node_modules/@types/react/index.d.ts:1759

---

### src

• `Optional` **src**: `string`

#### Inherited from

Omit.src

#### Defined in

node_modules/@types/react/index.d.ts:2110

---

### step

• `Optional` **step**: `string` \| `number`

#### Inherited from

Omit.step

#### Defined in

node_modules/@types/react/index.d.ts:2111

---

### style

• `Optional` **style**: `CSSProperties`

#### Overrides

Omit.style

#### Defined in

[src/components/input/input.tsx:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L21)

---

### suppressContentEditableWarning

• `Optional` **suppressContentEditableWarning**: `boolean`

#### Inherited from

Omit.suppressContentEditableWarning

#### Defined in

node_modules/@types/react/index.d.ts:1744

---

### suppressHydrationWarning

• `Optional` **suppressHydrationWarning**: `boolean`

#### Inherited from

Omit.suppressHydrationWarning

#### Defined in

node_modules/@types/react/index.d.ts:1745

---

### tabIndex

• `Optional` **tabIndex**: `number`

#### Inherited from

Omit.tabIndex

#### Defined in

node_modules/@types/react/index.d.ts:1761

---

### title

• `Optional` **title**: `string`

#### Inherited from

Omit.title

#### Defined in

node_modules/@types/react/index.d.ts:1762

---

### translate

• `Optional` **translate**: `"yes"` \| `"no"`

#### Inherited from

Omit.translate

#### Defined in

node_modules/@types/react/index.d.ts:1763

---

### type

• `Optional` **type**: `LiteralUnion`<`"button"` \| `"text"` \| `"search"` \| `"checkbox"` \| `"submit"`, `string`\>

#### Overrides

Omit.type

#### Defined in

[src/components/input/input.tsx:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L15)

---

### typeof

• `Optional` **typeof**: `string`

#### Inherited from

Omit.typeof

#### Defined in

node_modules/@types/react/index.d.ts:1778

---

### unselectable

• `Optional` **unselectable**: `"on"` \| `"off"`

#### Inherited from

Omit.unselectable

#### Defined in

node_modules/@types/react/index.d.ts:1793

---

### value

• `Optional` **value**: `string`

#### Overrides

Omit.value

#### Defined in

[src/components/input/input.tsx:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L20)

---

### vocab

• `Optional` **vocab**: `string`

#### Inherited from

Omit.vocab

#### Defined in

node_modules/@types/react/index.d.ts:1779

---

### width

• `Optional` **width**: `string` \| `number`

#### Inherited from

Omit.width

#### Defined in

node_modules/@types/react/index.d.ts:2114

## Methods

### onChange

▸ `Optional` **onChange**(`e`): `void`

#### Parameters

| Name | Type                                                        |
| :--- | :---------------------------------------------------------- |
| `e`  | `ChangeEvent`<`HTMLTextAreaElement` \| `HTMLInputElement`\> |

#### Returns

`void`

#### Defined in

[src/components/input/input.tsx:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L26)

---

### onKeyDown

▸ `Optional` **onKeyDown**(`e`): `void`

#### Parameters

| Name | Type                                 |
| :--- | :----------------------------------- |
| `e`  | `KeyboardEvent`<`HTMLInputElement`\> |

#### Returns

`void`

#### Defined in

[src/components/input/input.tsx:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/input/input.tsx#L25)
