---
id: 'molecule.component.IModalFuncProps'
title: 'Interface: IModalFuncProps'
sidebar_label: 'IModalFuncProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IModalFuncProps

## Hierarchy

-   `IDialogPropTypes`

    ↳ **`IModalFuncProps`**

## Properties

### animation

• `Optional` **animation**: `any`

#### Inherited from

IDialogPropTypes.animation

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:23

---

### bodyProps

• `Optional` **bodyProps**: `any`

#### Inherited from

IDialogPropTypes.bodyProps

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:33

---

### bodyStyle

• `Optional` **bodyStyle**: `Object`

#### Inherited from

IDialogPropTypes.bodyStyle

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:26

---

### cancelButtonProps

• `Optional` **cancelButtonProps**: [`IButtonProps`](molecule.component.IButtonProps)

#### Defined in

[src/components/dialog/modal.tsx:33](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L33)

---

### cancelText

• `Optional` **cancelText**: `ReactNode`

#### Defined in

[src/components/dialog/modal.tsx:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L26)

---

### centered

• `Optional` **centered**: `boolean`

#### Defined in

[src/components/dialog/modal.tsx:34](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L34)

---

### children

• `Optional` **children**: `any`

#### Inherited from

IDialogPropTypes.children

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:8

---

### className

• `Optional` **className**: `string`

#### Inherited from

IDialogPropTypes.className

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:4

---

### closable

• `Optional` **closable**: `boolean`

#### Inherited from

IDialogPropTypes.closable

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:11

---

### closeIcon

• `Optional` **closeIcon**: `ReactNode`

#### Inherited from

IDialogPropTypes.closeIcon

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:37

---

### content

• `Optional` **content**: `ReactNode`

#### Defined in

[src/components/dialog/modal.tsx:29](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L29)

---

### destroyOnClose

• `Optional` **destroyOnClose**: `boolean`

#### Inherited from

IDialogPropTypes.destroyOnClose

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:14

---

### focusTriggerAfterClose

• `Optional` **focusTriggerAfterClose**: `boolean`

#### Inherited from

IDialogPropTypes.focusTriggerAfterClose

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:39

---

### footer

• `Optional` **footer**: `ReactNode`

#### Inherited from

IDialogPropTypes.footer

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:20

---

### forceRender

• `Optional` **forceRender**: `boolean`

#### Inherited from

IDialogPropTypes.forceRender

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:38

---

### getContainer

• `Optional` **getContainer**: `false` \| `IStringOrHtmlElement` \| () => `IStringOrHtmlElement`

#### Inherited from

IDialogPropTypes.getContainer

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:36

---

### height

• `Optional` **height**: `number`

#### Inherited from

IDialogPropTypes.height

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:31

---

### icon

• `Optional` **icon**: `string` \| `Element`

#### Defined in

[src/components/dialog/modal.tsx:28](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L28)

---

### keyboard

• `Optional` **keyboard**: `boolean`

#### Inherited from

IDialogPropTypes.keyboard

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:5

---

### mask

• `Optional` **mask**: `boolean`

#### Inherited from

IDialogPropTypes.mask

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:7

---

### maskAnimation

• `Optional` **maskAnimation**: `any`

#### Inherited from

IDialogPropTypes.maskAnimation

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:24

---

### maskClosable

• `Optional` **maskClosable**: `boolean`

#### Inherited from

IDialogPropTypes.maskClosable

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:12

---

### maskProps

• `Optional` **maskProps**: `any`

#### Inherited from

IDialogPropTypes.maskProps

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:34

---

### maskStyle

• `Optional` **maskStyle**: `Object`

#### Inherited from

IDialogPropTypes.maskStyle

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:27

---

### maskTransitionName

• `Optional` **maskTransitionName**: `string`

#### Inherited from

IDialogPropTypes.maskTransitionName

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:22

---

### mousePosition

• `Optional` **mousePosition**: `Object`

#### Type declaration

| Name | Type     |
| :--- | :------- |
| `x`  | `number` |
| `y`  | `number` |

#### Inherited from

IDialogPropTypes.mousePosition

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:15

---

### okButtonProps

• `Optional` **okButtonProps**: [`IButtonProps`](molecule.component.IButtonProps)

#### Defined in

[src/components/dialog/modal.tsx:32](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L32)

---

### okCancel

• `Optional` **okCancel**: `boolean`

#### Defined in

[src/components/dialog/modal.tsx:35](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L35)

---

### okText

• `Optional` **okText**: `ReactNode`

#### Defined in

[src/components/dialog/modal.tsx:27](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L27)

---

### prefixCls

• `Optional` **prefixCls**: `string`

#### Inherited from

IDialogPropTypes.prefixCls

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:28

---

### style

• `Optional` **style**: `CSSProperties`

#### Inherited from

IDialogPropTypes.style

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:6

---

### title

• `Optional` **title**: `ReactNode`

#### Inherited from

IDialogPropTypes.title

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:19

---

### transitionName

• `Optional` **transitionName**: `string`

#### Inherited from

IDialogPropTypes.transitionName

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:21

---

### type

• `Optional` **type**: `"warning"` \| `"confirm"`

#### Defined in

[src/components/dialog/modal.tsx:36](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L36)

---

### visible

• `Optional` **visible**: `boolean`

#### Inherited from

IDialogPropTypes.visible

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:13

---

### width

• `Optional` **width**: `number`

#### Inherited from

IDialogPropTypes.width

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:30

---

### wrapClassName

• `Optional` **wrapClassName**: `string`

#### Inherited from

IDialogPropTypes.wrapClassName

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:29

---

### wrapProps

• `Optional` **wrapProps**: `any`

#### Inherited from

IDialogPropTypes.wrapProps

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:35

---

### wrapStyle

• `Optional` **wrapStyle**: `Object`

#### Inherited from

IDialogPropTypes.wrapStyle

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:25

---

### zIndex

• `Optional` **zIndex**: `number`

#### Inherited from

IDialogPropTypes.zIndex

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:32

## Methods

### afterClose

▸ `Optional` **afterClose**(): `any`

#### Returns

`any`

#### Inherited from

IDialogPropTypes.afterClose

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:9

---

### onCancel

▸ `Optional` **onCancel**(...`args`): `void`

#### Parameters

| Name      | Type    |
| :-------- | :------ |
| `...args` | `any`[] |

#### Returns

`void`

#### Defined in

[src/components/dialog/modal.tsx:31](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L31)

---

### onClose

▸ `Optional` **onClose**(`e`): `any`

#### Parameters

| Name | Type                                         |
| :--- | :------------------------------------------- |
| `e`  | `SyntheticEvent`<`HTMLDivElement`, `Event`\> |

#### Returns

`any`

#### Inherited from

IDialogPropTypes.onClose

#### Defined in

node_modules/rc-dialog/lib/IDialogPropTypes.d.ts:10

---

### onOk

▸ `Optional` **onOk**(...`args`): `any`

#### Parameters

| Name      | Type    |
| :-------- | :------ |
| `...args` | `any`[] |

#### Returns

`any`

#### Defined in

[src/components/dialog/modal.tsx:30](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/dialog/modal.tsx#L30)
