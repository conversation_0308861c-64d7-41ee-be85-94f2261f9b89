---
id: 'molecule.component.IContextView'
title: 'Interface: IContextView'
sidebar_label: 'IContextView'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IContextView

## Properties

### view

• **view**: `HTMLElementType`

#### Defined in

[src/components/contextView/index.tsx:30](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L30)

## Methods

### dispose

▸ **dispose**(): `void`

#### Returns

`void`

#### Defined in

[src/components/contextView/index.tsx:34](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L34)

---

### hide

▸ **hide**(): `void`

#### Returns

`void`

#### Defined in

[src/components/contextView/index.tsx:32](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L32)

---

### onHide

▸ **onHide**(`callback?`): `void`

#### Parameters

| Name        | Type       |
| :---------- | :--------- |
| `callback?` | `Function` |

#### Returns

`void`

#### Defined in

[src/components/contextView/index.tsx:33](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L33)

---

### show

▸ **show**(`anchorPos`, `render?`): `void`

#### Parameters

| Name        | Type              |
| :---------- | :---------------- |
| `anchorPos` | `IPosition`       |
| `render?`   | () => `ReactNode` |

#### Returns

`void`

#### Defined in

[src/components/contextView/index.tsx:31](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L31)
