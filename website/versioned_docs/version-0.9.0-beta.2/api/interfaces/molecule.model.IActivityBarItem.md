---
id: 'molecule.model.IActivityBarItem'
title: 'Interface: IActivityBarItem'
sidebar_label: 'IActivityBarItem'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IActivityBarItem

## Hierarchy

-   `HTMLElementProps`

    ↳ **`IActivityBarItem`**

## Properties

### checked

• `Optional` **checked**: `boolean`

#### Defined in

[src/model/workbench/activityBar.ts:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L23)

---

### className

• `Optional` **className**: `string`

#### Inherited from

HTMLElementProps.className

#### Defined in

[src/common/types.ts:4](https://github.com/DTStack/molecule/blob/b5324fcf/src/common/types.ts#L4)

---

### contextMenu

• `Optional` **contextMenu**: [`IActivityMenuItemProps`](molecule.model.IActivityMenuItemProps)[]

#### Defined in

[src/model/workbench/activityBar.ts:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L26)

---

### data

• `Optional` **data**: `any`

#### Defined in

[src/model/workbench/activityBar.ts:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L21)

---

### disabled

• `Optional` **disabled**: `boolean`

#### Defined in

[src/model/workbench/activityBar.ts:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L24)

---

### hidden

• `Optional` **hidden**: `boolean`

#### Defined in

[src/model/workbench/activityBar.ts:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L20)

---

### icon

• `Optional` **icon**: `string` \| `Element`

#### Defined in

[src/model/workbench/activityBar.ts:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L22)

---

### id

• **id**: `UniqueId`

#### Defined in

[src/model/workbench/activityBar.ts:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L18)

---

### name

• `Optional` **name**: `ReactNode`

#### Defined in

[src/model/workbench/activityBar.ts:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L19)

---

### role

• `Optional` **role**: `string`

#### Inherited from

HTMLElementProps.role

#### Defined in

[src/common/types.ts:5](https://github.com/DTStack/molecule/blob/b5324fcf/src/common/types.ts#L5)

---

### sortIndex

• `Optional` **sortIndex**: `number`

#### Defined in

[src/model/workbench/activityBar.ts:27](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L27)

---

### style

• `Optional` **style**: `CSSProperties`

#### Inherited from

HTMLElementProps.style

#### Defined in

[src/common/types.ts:3](https://github.com/DTStack/molecule/blob/b5324fcf/src/common/types.ts#L3)

---

### title

• `Optional` **title**: `string`

#### Inherited from

HTMLElementProps.title

#### Defined in

[src/common/types.ts:2](https://github.com/DTStack/molecule/blob/b5324fcf/src/common/types.ts#L2)

---

### type

• `Optional` **type**: `"normal"` \| `"global"`

#### Defined in

[src/model/workbench/activityBar.ts:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L25)

## Methods

### render

▸ `Optional` **render**(): `Element` \| `ReactNode`

#### Returns

`Element` \| `ReactNode`

#### Defined in

[src/model/workbench/activityBar.ts:28](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L28)
