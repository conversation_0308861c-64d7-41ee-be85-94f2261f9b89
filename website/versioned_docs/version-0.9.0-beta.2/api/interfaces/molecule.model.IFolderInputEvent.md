---
id: 'molecule.model.IFolderInputEvent'
title: 'Interface: IFolderInputEvent'
sidebar_label: 'IFolderInputEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderInputEvent

## Methods

### onFocus

▸ **onFocus**(): `void`

#### Returns

`void`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:28](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L28)

---

### setValue

▸ **setValue**(`value`): `void`

#### Parameters

| Name    | Type     |
| :------ | :------- |
| `value` | `string` |

#### Returns

`void`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:29](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L29)
