---
id: 'molecule.model.IContribute'
title: 'Interface: IContribute'
sidebar_label: 'IContribute'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IContribute

## Properties

### commands

• `Optional` **commands**: `any`

#### Defined in

[src/model/extension.ts:30](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L30)

---

### configuration

• `Optional` **configuration**: `any`

#### Defined in

[src/model/extension.ts:31](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L31)

---

### grammars

• `Optional` **grammars**: `any`

#### Defined in

[src/model/extension.ts:32](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L32)

---

### iconThemes

• `Optional` **iconThemes**: [`IIconTheme`](molecule.model.IIconTheme)[]

#### Defined in

[src/model/extension.ts:34](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L34)

---

### languages

• `Optional` **languages**: [`ILocale`](molecule.ILocale)[]

#### Defined in

[src/model/extension.ts:29](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L29)

---

### themes

• `Optional` **themes**: [`IColorTheme`](molecule.model.IColorTheme)[]

#### Defined in

[src/model/extension.ts:33](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L33)
