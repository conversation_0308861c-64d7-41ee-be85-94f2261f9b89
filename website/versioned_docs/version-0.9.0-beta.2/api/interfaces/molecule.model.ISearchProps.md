---
id: 'molecule.model.ISearchProps'
title: 'Interface: ISearchProps'
sidebar_label: 'ISearchProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ISearchProps

## Implemented by

-   [`SearchModel`](../classes/molecule.model.SearchModel)

## Properties

### headerToolBar

• `Optional` **headerToolBar**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>[]

#### Defined in

[src/model/workbench/search.tsx:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L13)

---

### isCaseSensitive

• `Optional` **isCaseSensitive**: `boolean`

#### Defined in

[src/model/workbench/search.tsx:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L22)

---

### isRegex

• `Optional` **isRegex**: `boolean`

#### Defined in

[src/model/workbench/search.tsx:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L21)

---

### isWholeWords

• `Optional` **isWholeWords**: `boolean`

#### Defined in

[src/model/workbench/search.tsx:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L23)

---

### preserveCase

• `Optional` **preserveCase**: `boolean`

#### Defined in

[src/model/workbench/search.tsx:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L24)

---

### replaceAddons

• `Optional` **replaceAddons**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>[]

#### Defined in

[src/model/workbench/search.tsx:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L15)

---

### replaceMode

• `Optional` **replaceMode**: `boolean`

#### Defined in

[src/model/workbench/search.tsx:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L19)

---

### replaceValue

• `Optional` **replaceValue**: `string`

#### Defined in

[src/model/workbench/search.tsx:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L18)

---

### result

• **result**: [`ITreeNodeItemProps`](molecule.component.ITreeNodeItemProps)<`any`\>[]

#### Defined in

[src/model/workbench/search.tsx:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L16)

---

### searchAddons

• `Optional` **searchAddons**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>[]

#### Defined in

[src/model/workbench/search.tsx:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L14)

---

### validationInfo

• `Optional` **validationInfo**: `Object`

#### Type declaration

| Name   | Type                                 |
| :----- | :----------------------------------- |
| `text` | `string`                             |
| `type` | `"warning"` \| `"info"` \| `"error"` |

#### Defined in

[src/model/workbench/search.tsx:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L20)

---

### value

• `Optional` **value**: `string`

#### Defined in

[src/model/workbench/search.tsx:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/search.tsx#L17)
