---
id: 'IMoleculeProps'
title: 'Interface: IMoleculeProps'
sidebar_label: 'IMoleculeProps'
sidebar_position: 0
custom_edit_url: null
---

## Properties

### defaultLocale

• `Optional` **defaultLocale**: `string`

Specify a default locale Id, the Molecule built-in `zh-CN`, `en` two languages, and
default locale Id is `en`.

#### Defined in

[src/provider/molecule.tsx:29](https://github.com/DTStack/molecule/blob/b5324fcf/src/provider/molecule.tsx#L29)

---

### extensions

• `Optional` **extensions**: [`IExtension`](molecule.model.IExtension)[]

Molecule Extension instances, after the MoleculeProvider
did mount, then handle it.

#### Defined in

[src/provider/molecule.tsx:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/provider/molecule.tsx#L24)
