---
id: 'molecule.model.IProblems'
title: 'Interface: IProblems<T>'
sidebar_label: 'IProblems'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IProblems

## Type parameters

| Name | Type  |
| :--- | :---- |
| `T`  | `any` |

## Implemented by

-   [`ProblemsModel`](../classes/molecule.model.ProblemsModel)

## Properties

### data

• **data**: [`IProblemsItem`](molecule.model.IProblemsItem)<`T`\>[]

#### Defined in

[src/model/problems.tsx:27](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L27)

---

### id

• **id**: `UniqueId`

#### Defined in

[src/model/problems.tsx:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L25)

---

### name

• **name**: `string`

#### Defined in

[src/model/problems.tsx:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L26)

---

### show

• `Optional` **show**: `boolean`

#### Defined in

[src/model/problems.tsx:28](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L28)
