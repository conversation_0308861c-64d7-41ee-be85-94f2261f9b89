---
id: 'molecule.model.TokenColor'
title: 'Interface: TokenColor'
sidebar_label: 'TokenColor'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).TokenColor

## Hierarchy

-   `Object`

    ↳ **`TokenColor`**

## Properties

### constructor

• **constructor**: `Function`

The initial value of Object.prototype.constructor is the standard built-in Object constructor.

#### Inherited from

Object.constructor

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:122

---

### name

• `Optional` **name**: `string`

#### Defined in

[src/model/colorTheme.ts:5](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L5)

---

### scope

• `Optional` **scope**: `string` \| `string`[]

#### Defined in

[src/model/colorTheme.ts:6](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L6)

---

### settings

• `Optional` **settings**: `object`

#### Defined in

[src/model/colorTheme.ts:7](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L7)

## Methods

### hasOwnProperty

▸ **hasOwnProperty**(`v`): `boolean`

Determines whether an object has a property with the specified name.

#### Parameters

| Name | Type          | Description      |
| :--- | :------------ | :--------------- |
| `v`  | `PropertyKey` | A property name. |

#### Returns

`boolean`

#### Inherited from

Object.hasOwnProperty

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:137

---

### isPrototypeOf

▸ **isPrototypeOf**(`v`): `boolean`

Determines whether an object exists in another object's prototype chain.

#### Parameters

| Name | Type     | Description                                            |
| :--- | :------- | :----------------------------------------------------- |
| `v`  | `Object` | Another object whose prototype chain is to be checked. |

#### Returns

`boolean`

#### Inherited from

Object.isPrototypeOf

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:143

---

### propertyIsEnumerable

▸ **propertyIsEnumerable**(`v`): `boolean`

Determines whether a specified property is enumerable.

#### Parameters

| Name | Type          | Description      |
| :--- | :------------ | :--------------- |
| `v`  | `PropertyKey` | A property name. |

#### Returns

`boolean`

#### Inherited from

Object.propertyIsEnumerable

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:149

---

### toLocaleString

▸ **toLocaleString**(): `string`

Returns a date converted to a string using the current locale.

#### Returns

`string`

#### Inherited from

Object.toLocaleString

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:128

---

### toString

▸ **toString**(): `string`

Returns a string representation of an object.

#### Returns

`string`

#### Inherited from

Object.toString

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:125

---

### valueOf

▸ **valueOf**(): `Object`

Returns the primitive value of the specified object.

#### Returns

`Object`

#### Inherited from

Object.valueOf

#### Defined in

node_modules/typescript/lib/lib.es5.d.ts:131
