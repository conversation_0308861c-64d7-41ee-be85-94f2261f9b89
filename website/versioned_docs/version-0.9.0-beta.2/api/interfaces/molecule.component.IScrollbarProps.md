---
id: 'molecule.component.IScrollbarProps'
title: 'Interface: IScrollbarProps'
sidebar_label: 'IScrollbarProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IScrollbarProps

## Hierarchy

-   `ScrollbarProps`

    ↳ **`IScrollbarProps`**

## Properties

### about

• `Optional` **about**: `string`

#### Inherited from

ScrollbarProps.about

#### Defined in

node_modules/@types/react/index.d.ts:1772

---

### accept

• `Optional` **accept**: `string`

#### Inherited from

ScrollbarProps.accept

#### Defined in

node_modules/@types/react/index.d.ts:1810

---

### acceptCharset

• `Optional` **acceptCharset**: `string`

#### Inherited from

ScrollbarProps.acceptCharset

#### Defined in

node_modules/@types/react/index.d.ts:1811

---

### accessKey

• `Optional` **accessKey**: `string`

#### Inherited from

ScrollbarProps.accessKey

#### Defined in

node_modules/@types/react/index.d.ts:1748

---

### action

• `Optional` **action**: `string`

#### Inherited from

ScrollbarProps.action

#### Defined in

node_modules/@types/react/index.d.ts:1812

---

### allowFullScreen

• `Optional` **allowFullScreen**: `boolean`

#### Inherited from

ScrollbarProps.allowFullScreen

#### Defined in

node_modules/@types/react/index.d.ts:1813

---

### allowTransparency

• `Optional` **allowTransparency**: `boolean`

#### Inherited from

ScrollbarProps.allowTransparency

#### Defined in

node_modules/@types/react/index.d.ts:1814

---

### alt

• `Optional` **alt**: `string`

#### Inherited from

ScrollbarProps.alt

#### Defined in

node_modules/@types/react/index.d.ts:1815

---

### aria-activedescendant

• `Optional` **aria-activedescendant**: `string`

Identifies the currently active element when DOM focus is on a composite widget, textbox, group, or application.

#### Inherited from

ScrollbarProps.aria-activedescendant

#### Defined in

node_modules/@types/react/index.d.ts:1555

---

### aria-atomic

• `Optional` **aria-atomic**: `boolean` \| `"true"` \| `"false"`

Indicates whether assistive technologies will present all, or only parts of, the changed region based on the change notifications defined by the aria-relevant attribute.

#### Inherited from

ScrollbarProps.aria-atomic

#### Defined in

node_modules/@types/react/index.d.ts:1557

---

### aria-autocomplete

• `Optional` **aria-autocomplete**: `"none"` \| `"inline"` \| `"list"` \| `"both"`

Indicates whether inputting text could trigger display of one or more predictions of the user's intended value for an input and specifies how predictions would be
presented if they are made.

#### Inherited from

ScrollbarProps.aria-autocomplete

#### Defined in

node_modules/@types/react/index.d.ts:1562

---

### aria-busy

• `Optional` **aria-busy**: `boolean` \| `"true"` \| `"false"`

Indicates an element is being modified and that assistive technologies MAY want to wait until the modifications are complete before exposing them to the user.

#### Inherited from

ScrollbarProps.aria-busy

#### Defined in

node_modules/@types/react/index.d.ts:1564

---

### aria-checked

• `Optional` **aria-checked**: `boolean` \| `"true"` \| `"false"` \| `"mixed"`

Indicates the current "checked" state of checkboxes, radio buttons, and other widgets.

**`see`** aria-pressed @see aria-selected.

#### Inherited from

ScrollbarProps.aria-checked

#### Defined in

node_modules/@types/react/index.d.ts:1569

---

### aria-colcount

• `Optional` **aria-colcount**: `number`

Defines the total number of columns in a table, grid, or treegrid.

**`see`** aria-colindex.

#### Inherited from

ScrollbarProps.aria-colcount

#### Defined in

node_modules/@types/react/index.d.ts:1574

---

### aria-colindex

• `Optional` **aria-colindex**: `number`

Defines an element's column index or position with respect to the total number of columns within a table, grid, or treegrid.

**`see`** aria-colcount @see aria-colspan.

#### Inherited from

ScrollbarProps.aria-colindex

#### Defined in

node_modules/@types/react/index.d.ts:1579

---

### aria-colspan

• `Optional` **aria-colspan**: `number`

Defines the number of columns spanned by a cell or gridcell within a table, grid, or treegrid.

**`see`** aria-colindex @see aria-rowspan.

#### Inherited from

ScrollbarProps.aria-colspan

#### Defined in

node_modules/@types/react/index.d.ts:1584

---

### aria-controls

• `Optional` **aria-controls**: `string`

Identifies the element (or elements) whose contents or presence are controlled by the current element.

**`see`** aria-owns.

#### Inherited from

ScrollbarProps.aria-controls

#### Defined in

node_modules/@types/react/index.d.ts:1589

---

### aria-current

• `Optional` **aria-current**: `boolean` \| `"time"` \| `"true"` \| `"false"` \| `"page"` \| `"step"` \| `"location"` \| `"date"`

Indicates the element that represents the current item within a container or set of related elements.

#### Inherited from

ScrollbarProps.aria-current

#### Defined in

node_modules/@types/react/index.d.ts:1591

---

### aria-describedby

• `Optional` **aria-describedby**: `string`

Identifies the element (or elements) that describes the object.

**`see`** aria-labelledby

#### Inherited from

ScrollbarProps.aria-describedby

#### Defined in

node_modules/@types/react/index.d.ts:1596

---

### aria-details

• `Optional` **aria-details**: `string`

Identifies the element that provides a detailed, extended description for the object.

**`see`** aria-describedby.

#### Inherited from

ScrollbarProps.aria-details

#### Defined in

node_modules/@types/react/index.d.ts:1601

---

### aria-disabled

• `Optional` **aria-disabled**: `boolean` \| `"true"` \| `"false"`

Indicates that the element is perceivable but disabled, so it is not editable or otherwise operable.

**`see`** aria-hidden @see aria-readonly.

#### Inherited from

ScrollbarProps.aria-disabled

#### Defined in

node_modules/@types/react/index.d.ts:1606

---

### aria-dropeffect

• `Optional` **aria-dropeffect**: `"link"` \| `"none"` \| `"copy"` \| `"execute"` \| `"move"` \| `"popup"`

Indicates what functions can be performed when a dragged object is released on the drop target.

**`deprecated`** in ARIA 1.1

#### Inherited from

ScrollbarProps.aria-dropeffect

#### Defined in

node_modules/@types/react/index.d.ts:1611

---

### aria-errormessage

• `Optional` **aria-errormessage**: `string`

Identifies the element that provides an error message for the object.

**`see`** aria-invalid @see aria-describedby.

#### Inherited from

ScrollbarProps.aria-errormessage

#### Defined in

node_modules/@types/react/index.d.ts:1616

---

### aria-expanded

• `Optional` **aria-expanded**: `boolean` \| `"true"` \| `"false"`

Indicates whether the element, or another grouping element it controls, is currently expanded or collapsed.

#### Inherited from

ScrollbarProps.aria-expanded

#### Defined in

node_modules/@types/react/index.d.ts:1618

---

### aria-flowto

• `Optional` **aria-flowto**: `string`

Identifies the next element (or elements) in an alternate reading order of content which, at the user's discretion,
allows assistive technology to override the general default of reading in document source order.

#### Inherited from

ScrollbarProps.aria-flowto

#### Defined in

node_modules/@types/react/index.d.ts:1623

---

### aria-grabbed

• `Optional` **aria-grabbed**: `boolean` \| `"true"` \| `"false"`

Indicates an element's "grabbed" state in a drag-and-drop operation.

**`deprecated`** in ARIA 1.1

#### Inherited from

ScrollbarProps.aria-grabbed

#### Defined in

node_modules/@types/react/index.d.ts:1628

---

### aria-haspopup

• `Optional` **aria-haspopup**: `boolean` \| `"dialog"` \| `"menu"` \| `"true"` \| `"false"` \| `"listbox"` \| `"tree"` \| `"grid"`

Indicates the availability and type of interactive popup element, such as menu or dialog, that can be triggered by an element.

#### Inherited from

ScrollbarProps.aria-haspopup

#### Defined in

node_modules/@types/react/index.d.ts:1630

---

### aria-hidden

• `Optional` **aria-hidden**: `boolean` \| `"true"` \| `"false"`

Indicates whether the element is exposed to an accessibility API.

**`see`** aria-disabled.

#### Inherited from

ScrollbarProps.aria-hidden

#### Defined in

node_modules/@types/react/index.d.ts:1635

---

### aria-invalid

• `Optional` **aria-invalid**: `boolean` \| `"true"` \| `"false"` \| `"grammar"` \| `"spelling"`

Indicates the entered value does not conform to the format expected by the application.

**`see`** aria-errormessage.

#### Inherited from

ScrollbarProps.aria-invalid

#### Defined in

node_modules/@types/react/index.d.ts:1640

---

### aria-keyshortcuts

• `Optional` **aria-keyshortcuts**: `string`

Indicates keyboard shortcuts that an author has implemented to activate or give focus to an element.

#### Inherited from

ScrollbarProps.aria-keyshortcuts

#### Defined in

node_modules/@types/react/index.d.ts:1642

---

### aria-label

• `Optional` **aria-label**: `string`

Defines a string value that labels the current element.

**`see`** aria-labelledby.

#### Inherited from

ScrollbarProps.aria-label

#### Defined in

node_modules/@types/react/index.d.ts:1647

---

### aria-labelledby

• `Optional` **aria-labelledby**: `string`

Identifies the element (or elements) that labels the current element.

**`see`** aria-describedby.

#### Inherited from

ScrollbarProps.aria-labelledby

#### Defined in

node_modules/@types/react/index.d.ts:1652

---

### aria-level

• `Optional` **aria-level**: `number`

Defines the hierarchical level of an element within a structure.

#### Inherited from

ScrollbarProps.aria-level

#### Defined in

node_modules/@types/react/index.d.ts:1654

---

### aria-live

• `Optional` **aria-live**: `"off"` \| `"assertive"` \| `"polite"`

Indicates that an element will be updated, and describes the types of updates the user agents, assistive technologies, and user can expect from the live region.

#### Inherited from

ScrollbarProps.aria-live

#### Defined in

node_modules/@types/react/index.d.ts:1656

---

### aria-modal

• `Optional` **aria-modal**: `boolean` \| `"true"` \| `"false"`

Indicates whether an element is modal when displayed.

#### Inherited from

ScrollbarProps.aria-modal

#### Defined in

node_modules/@types/react/index.d.ts:1658

---

### aria-multiline

• `Optional` **aria-multiline**: `boolean` \| `"true"` \| `"false"`

Indicates whether a text box accepts multiple lines of input or only a single line.

#### Inherited from

ScrollbarProps.aria-multiline

#### Defined in

node_modules/@types/react/index.d.ts:1660

---

### aria-multiselectable

• `Optional` **aria-multiselectable**: `boolean` \| `"true"` \| `"false"`

Indicates that the user may select more than one item from the current selectable descendants.

#### Inherited from

ScrollbarProps.aria-multiselectable

#### Defined in

node_modules/@types/react/index.d.ts:1662

---

### aria-orientation

• `Optional` **aria-orientation**: `"horizontal"` \| `"vertical"`

Indicates whether the element's orientation is horizontal, vertical, or unknown/ambiguous.

#### Inherited from

ScrollbarProps.aria-orientation

#### Defined in

node_modules/@types/react/index.d.ts:1664

---

### aria-owns

• `Optional` **aria-owns**: `string`

Identifies an element (or elements) in order to define a visual, functional, or contextual parent/child relationship
between DOM elements where the DOM hierarchy cannot be used to represent the relationship.

**`see`** aria-controls.

#### Inherited from

ScrollbarProps.aria-owns

#### Defined in

node_modules/@types/react/index.d.ts:1670

---

### aria-placeholder

• `Optional` **aria-placeholder**: `string`

Defines a short hint (a word or short phrase) intended to aid the user with data entry when the control has no value.
A hint could be a sample value or a brief description of the expected format.

#### Inherited from

ScrollbarProps.aria-placeholder

#### Defined in

node_modules/@types/react/index.d.ts:1675

---

### aria-posinset

• `Optional` **aria-posinset**: `number`

Defines an element's number or position in the current set of listitems or treeitems. Not required if all elements in the set are present in the DOM.

**`see`** aria-setsize.

#### Inherited from

ScrollbarProps.aria-posinset

#### Defined in

node_modules/@types/react/index.d.ts:1680

---

### aria-pressed

• `Optional` **aria-pressed**: `boolean` \| `"true"` \| `"false"` \| `"mixed"`

Indicates the current "pressed" state of toggle buttons.

**`see`** aria-checked @see aria-selected.

#### Inherited from

ScrollbarProps.aria-pressed

#### Defined in

node_modules/@types/react/index.d.ts:1685

---

### aria-readonly

• `Optional` **aria-readonly**: `boolean` \| `"true"` \| `"false"`

Indicates that the element is not editable, but is otherwise operable.

**`see`** aria-disabled.

#### Inherited from

ScrollbarProps.aria-readonly

#### Defined in

node_modules/@types/react/index.d.ts:1690

---

### aria-relevant

• `Optional` **aria-relevant**: `"text"` \| `"additions"` \| `"additions removals"` \| `"additions text"` \| `"all"` \| `"removals"` \| `"removals additions"` \| `"removals text"` \| `"text additions"` \| `"text removals"`

Indicates what notifications the user agent will trigger when the accessibility tree within a live region is modified.

**`see`** aria-atomic.

#### Inherited from

ScrollbarProps.aria-relevant

#### Defined in

node_modules/@types/react/index.d.ts:1695

---

### aria-required

• `Optional` **aria-required**: `boolean` \| `"true"` \| `"false"`

Indicates that user input is required on the element before a form may be submitted.

#### Inherited from

ScrollbarProps.aria-required

#### Defined in

node_modules/@types/react/index.d.ts:1697

---

### aria-roledescription

• `Optional` **aria-roledescription**: `string`

Defines a human-readable, author-localized description for the role of an element.

#### Inherited from

ScrollbarProps.aria-roledescription

#### Defined in

node_modules/@types/react/index.d.ts:1699

---

### aria-rowcount

• `Optional` **aria-rowcount**: `number`

Defines the total number of rows in a table, grid, or treegrid.

**`see`** aria-rowindex.

#### Inherited from

ScrollbarProps.aria-rowcount

#### Defined in

node_modules/@types/react/index.d.ts:1704

---

### aria-rowindex

• `Optional` **aria-rowindex**: `number`

Defines an element's row index or position with respect to the total number of rows within a table, grid, or treegrid.

**`see`** aria-rowcount @see aria-rowspan.

#### Inherited from

ScrollbarProps.aria-rowindex

#### Defined in

node_modules/@types/react/index.d.ts:1709

---

### aria-rowspan

• `Optional` **aria-rowspan**: `number`

Defines the number of rows spanned by a cell or gridcell within a table, grid, or treegrid.

**`see`** aria-rowindex @see aria-colspan.

#### Inherited from

ScrollbarProps.aria-rowspan

#### Defined in

node_modules/@types/react/index.d.ts:1714

---

### aria-selected

• `Optional` **aria-selected**: `boolean` \| `"true"` \| `"false"`

Indicates the current "selected" state of various widgets.

**`see`** aria-checked @see aria-pressed.

#### Inherited from

ScrollbarProps.aria-selected

#### Defined in

node_modules/@types/react/index.d.ts:1719

---

### aria-setsize

• `Optional` **aria-setsize**: `number`

Defines the number of items in the current set of listitems or treeitems. Not required if all elements in the set are present in the DOM.

**`see`** aria-posinset.

#### Inherited from

ScrollbarProps.aria-setsize

#### Defined in

node_modules/@types/react/index.d.ts:1724

---

### aria-sort

• `Optional` **aria-sort**: `"none"` \| `"ascending"` \| `"descending"` \| `"other"`

Indicates if items in a table or grid are sorted in ascending or descending order.

#### Inherited from

ScrollbarProps.aria-sort

#### Defined in

node_modules/@types/react/index.d.ts:1726

---

### aria-valuemax

• `Optional` **aria-valuemax**: `number`

Defines the maximum allowed value for a range widget.

#### Inherited from

ScrollbarProps.aria-valuemax

#### Defined in

node_modules/@types/react/index.d.ts:1728

---

### aria-valuemin

• `Optional` **aria-valuemin**: `number`

Defines the minimum allowed value for a range widget.

#### Inherited from

ScrollbarProps.aria-valuemin

#### Defined in

node_modules/@types/react/index.d.ts:1730

---

### aria-valuenow

• `Optional` **aria-valuenow**: `number`

Defines the current value for a range widget.

**`see`** aria-valuetext.

#### Inherited from

ScrollbarProps.aria-valuenow

#### Defined in

node_modules/@types/react/index.d.ts:1735

---

### aria-valuetext

• `Optional` **aria-valuetext**: `string`

Defines the human readable text alternative of aria-valuenow for a range widget.

#### Inherited from

ScrollbarProps.aria-valuetext

#### Defined in

node_modules/@types/react/index.d.ts:1737

---

### as

• `Optional` **as**: `string`

#### Inherited from

ScrollbarProps.as

#### Defined in

node_modules/@types/react/index.d.ts:1816

---

### async

• `Optional` **async**: `boolean`

#### Inherited from

ScrollbarProps.async

#### Defined in

node_modules/@types/react/index.d.ts:1817

---

### autoCapitalize

• `Optional` **autoCapitalize**: `string`

#### Inherited from

ScrollbarProps.autoCapitalize

#### Defined in

node_modules/@types/react/index.d.ts:1782

---

### autoComplete

• `Optional` **autoComplete**: `string`

#### Inherited from

ScrollbarProps.autoComplete

#### Defined in

node_modules/@types/react/index.d.ts:1818

---

### autoCorrect

• `Optional` **autoCorrect**: `string`

#### Inherited from

ScrollbarProps.autoCorrect

#### Defined in

node_modules/@types/react/index.d.ts:1783

---

### autoFocus

• `Optional` **autoFocus**: `boolean`

#### Inherited from

ScrollbarProps.autoFocus

#### Defined in

node_modules/@types/react/index.d.ts:1819

---

### autoPlay

• `Optional` **autoPlay**: `boolean`

#### Inherited from

ScrollbarProps.autoPlay

#### Defined in

node_modules/@types/react/index.d.ts:1820

---

### autoSave

• `Optional` **autoSave**: `string`

#### Inherited from

ScrollbarProps.autoSave

#### Defined in

node_modules/@types/react/index.d.ts:1784

---

### capture

• `Optional` **capture**: `string` \| `boolean`

#### Inherited from

ScrollbarProps.capture

#### Defined in

node_modules/@types/react/index.d.ts:1821

---

### cellPadding

• `Optional` **cellPadding**: `string` \| `number`

#### Inherited from

ScrollbarProps.cellPadding

#### Defined in

node_modules/@types/react/index.d.ts:1822

---

### cellSpacing

• `Optional` **cellSpacing**: `string` \| `number`

#### Inherited from

ScrollbarProps.cellSpacing

#### Defined in

node_modules/@types/react/index.d.ts:1823

---

### challenge

• `Optional` **challenge**: `string`

#### Inherited from

ScrollbarProps.challenge

#### Defined in

node_modules/@types/react/index.d.ts:1825

---

### charSet

• `Optional` **charSet**: `string`

#### Inherited from

ScrollbarProps.charSet

#### Defined in

node_modules/@types/react/index.d.ts:1824

---

### checked

• `Optional` **checked**: `boolean`

#### Inherited from

ScrollbarProps.checked

#### Defined in

node_modules/@types/react/index.d.ts:1826

---

### children

• `Optional` **children**: `ReactNode`

#### Inherited from

ScrollbarProps.children

#### Defined in

node_modules/@types/react/index.d.ts:1345

---

### cite

• `Optional` **cite**: `string`

#### Inherited from

ScrollbarProps.cite

#### Defined in

node_modules/@types/react/index.d.ts:1827

---

### classID

• `Optional` **classID**: `string`

#### Inherited from

ScrollbarProps.classID

#### Defined in

node_modules/@types/react/index.d.ts:1828

---

### className

• `Optional` **className**: `string`

#### Inherited from

ScrollbarProps.className

#### Defined in

node_modules/@types/react/index.d.ts:1749

---

### colSpan

• `Optional` **colSpan**: `number`

#### Inherited from

ScrollbarProps.colSpan

#### Defined in

node_modules/@types/react/index.d.ts:1830

---

### color

• `Optional` **color**: `string`

#### Inherited from

ScrollbarProps.color

#### Defined in

node_modules/@types/react/index.d.ts:1785

---

### cols

• `Optional` **cols**: `number`

#### Inherited from

ScrollbarProps.cols

#### Defined in

node_modules/@types/react/index.d.ts:1829

---

### content

• `Optional` **content**: `string`

#### Inherited from

ScrollbarProps.content

#### Defined in

node_modules/@types/react/index.d.ts:1831

---

### contentEditable

• `Optional` **contentEditable**: `Booleanish` \| `"inherit"`

#### Inherited from

ScrollbarProps.contentEditable

#### Defined in

node_modules/@types/react/index.d.ts:1750

---

### contentProps

• `Optional` **contentProps**: `ElementPropsWithElementRefAndRenderer`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.contentProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:46

---

### contextMenu

• `Optional` **contextMenu**: `string`

#### Inherited from

ScrollbarProps.contextMenu

#### Defined in

node_modules/@types/react/index.d.ts:1751

---

### controls

• `Optional` **controls**: `boolean`

#### Inherited from

ScrollbarProps.controls

#### Defined in

node_modules/@types/react/index.d.ts:1832

---

### coords

• `Optional` **coords**: `string`

#### Inherited from

ScrollbarProps.coords

#### Defined in

node_modules/@types/react/index.d.ts:1833

---

### createContext

• `Optional` **createContext**: `boolean`

#### Inherited from

ScrollbarProps.createContext

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:8

---

### crossOrigin

• `Optional` **crossOrigin**: `string`

#### Inherited from

ScrollbarProps.crossOrigin

#### Defined in

node_modules/@types/react/index.d.ts:1834

---

### css

• `Optional` **css**: `InterpolationWithTheme`<`any`\>

#### Inherited from

ScrollbarProps.css

#### Defined in

node_modules/@emotion/core/types/index.d.ts:84

---

### dangerouslySetInnerHTML

• `Optional` **dangerouslySetInnerHTML**: `Object`

#### Type declaration

| Name     | Type     |
| :------- | :------- |
| `__html` | `string` |

#### Inherited from

ScrollbarProps.dangerouslySetInnerHTML

#### Defined in

node_modules/@types/react/index.d.ts:1346

---

### data

• `Optional` **data**: `string`

#### Inherited from

ScrollbarProps.data

#### Defined in

node_modules/@types/react/index.d.ts:1835

---

### datatype

• `Optional` **datatype**: `string`

#### Inherited from

ScrollbarProps.datatype

#### Defined in

node_modules/@types/react/index.d.ts:1773

---

### dateTime

• `Optional` **dateTime**: `string`

#### Inherited from

ScrollbarProps.dateTime

#### Defined in

node_modules/@types/react/index.d.ts:1836

---

### default

• `Optional` **default**: `boolean`

#### Inherited from

ScrollbarProps.default

#### Defined in

node_modules/@types/react/index.d.ts:1837

---

### defaultChecked

• `Optional` **defaultChecked**: `boolean`

#### Inherited from

ScrollbarProps.defaultChecked

#### Defined in

node_modules/@types/react/index.d.ts:1742

---

### defaultValue

• `Optional` **defaultValue**: `string` \| `number` \| readonly `string`[]

#### Inherited from

ScrollbarProps.defaultValue

#### Defined in

node_modules/@types/react/index.d.ts:1743

---

### defer

• `Optional` **defer**: `boolean`

#### Inherited from

ScrollbarProps.defer

#### Defined in

node_modules/@types/react/index.d.ts:1838

---

### dir

• `Optional` **dir**: `string`

#### Inherited from

ScrollbarProps.dir

#### Defined in

node_modules/@types/react/index.d.ts:1752

---

### disableTrackXMousewheelScrolling

• `Optional` **disableTrackXMousewheelScrolling**: `boolean`

#### Inherited from

ScrollbarProps.disableTrackXMousewheelScrolling

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:38

---

### disableTrackXWidthCompensation

• `Optional` **disableTrackXWidthCompensation**: `boolean`

#### Inherited from

ScrollbarProps.disableTrackXWidthCompensation

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:41

---

### disableTrackYMousewheelScrolling

• `Optional` **disableTrackYMousewheelScrolling**: `boolean`

#### Inherited from

ScrollbarProps.disableTrackYMousewheelScrolling

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:39

---

### disableTrackYWidthCompensation

• `Optional` **disableTrackYWidthCompensation**: `boolean`

#### Inherited from

ScrollbarProps.disableTrackYWidthCompensation

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:42

---

### disableTracksMousewheelScrolling

• `Optional` **disableTracksMousewheelScrolling**: `boolean`

#### Inherited from

ScrollbarProps.disableTracksMousewheelScrolling

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:37

---

### disableTracksWidthCompensation

• `Optional` **disableTracksWidthCompensation**: `boolean`

#### Inherited from

ScrollbarProps.disableTracksWidthCompensation

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:40

---

### disabled

• `Optional` **disabled**: `boolean`

#### Inherited from

ScrollbarProps.disabled

#### Defined in

node_modules/@types/react/index.d.ts:1839

---

### download

• `Optional` **download**: `any`

#### Inherited from

ScrollbarProps.download

#### Defined in

node_modules/@types/react/index.d.ts:1840

---

### draggable

• `Optional` **draggable**: `Booleanish`

#### Inherited from

ScrollbarProps.draggable

#### Defined in

node_modules/@types/react/index.d.ts:1753

---

### elementRef

• `Optional` **elementRef**: `ElementRef`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.elementRef

#### Defined in

node_modules/react-scrollbars-custom/dist/types/types.d.ts:19

---

### encType

• `Optional` **encType**: `string`

#### Inherited from

ScrollbarProps.encType

#### Defined in

node_modules/@types/react/index.d.ts:1841

---

### fallbackScrollbarWidth

• `Optional` **fallbackScrollbarWidth**: `number`

#### Inherited from

ScrollbarProps.fallbackScrollbarWidth

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:29

---

### form

• `Optional` **form**: `string`

#### Inherited from

ScrollbarProps.form

#### Defined in

node_modules/@types/react/index.d.ts:1842

---

### formAction

• `Optional` **formAction**: `string`

#### Inherited from

ScrollbarProps.formAction

#### Defined in

node_modules/@types/react/index.d.ts:1843

---

### formEncType

• `Optional` **formEncType**: `string`

#### Inherited from

ScrollbarProps.formEncType

#### Defined in

node_modules/@types/react/index.d.ts:1844

---

### formMethod

• `Optional` **formMethod**: `string`

#### Inherited from

ScrollbarProps.formMethod

#### Defined in

node_modules/@types/react/index.d.ts:1845

---

### formNoValidate

• `Optional` **formNoValidate**: `boolean`

#### Inherited from

ScrollbarProps.formNoValidate

#### Defined in

node_modules/@types/react/index.d.ts:1846

---

### formTarget

• `Optional` **formTarget**: `string`

#### Inherited from

ScrollbarProps.formTarget

#### Defined in

node_modules/@types/react/index.d.ts:1847

---

### frameBorder

• `Optional` **frameBorder**: `string` \| `number`

#### Inherited from

ScrollbarProps.frameBorder

#### Defined in

node_modules/@types/react/index.d.ts:1848

---

### headers

• `Optional` **headers**: `string`

#### Inherited from

ScrollbarProps.headers

#### Defined in

node_modules/@types/react/index.d.ts:1849

---

### height

• `Optional` **height**: `string` \| `number`

#### Inherited from

ScrollbarProps.height

#### Defined in

node_modules/@types/react/index.d.ts:1850

---

### hidden

• `Optional` **hidden**: `boolean`

#### Inherited from

ScrollbarProps.hidden

#### Defined in

node_modules/@types/react/index.d.ts:1754

---

### high

• `Optional` **high**: `number`

#### Inherited from

ScrollbarProps.high

#### Defined in

node_modules/@types/react/index.d.ts:1851

---

### href

• `Optional` **href**: `string`

#### Inherited from

ScrollbarProps.href

#### Defined in

node_modules/@types/react/index.d.ts:1852

---

### hrefLang

• `Optional` **hrefLang**: `string`

#### Inherited from

ScrollbarProps.hrefLang

#### Defined in

node_modules/@types/react/index.d.ts:1853

---

### htmlFor

• `Optional` **htmlFor**: `string`

#### Inherited from

ScrollbarProps.htmlFor

#### Defined in

node_modules/@types/react/index.d.ts:1854

---

### httpEquiv

• `Optional` **httpEquiv**: `string`

#### Inherited from

ScrollbarProps.httpEquiv

#### Defined in

node_modules/@types/react/index.d.ts:1855

---

### id

• `Optional` **id**: `string`

#### Inherited from

ScrollbarProps.id

#### Defined in

node_modules/@types/react/index.d.ts:1755

---

### inlist

• `Optional` **inlist**: `any`

#### Inherited from

ScrollbarProps.inlist

#### Defined in

node_modules/@types/react/index.d.ts:1774

---

### inputMode

• `Optional` **inputMode**: `"text"` \| `"none"` \| `"tel"` \| `"url"` \| `"email"` \| `"numeric"` \| `"decimal"` \| `"search"`

Hints at the type of data that might be entered by the user while editing the element or its contents

**`see`** https://html.spec.whatwg.org/multipage/interaction.html#input-modalities:-the-inputmode-attribute

#### Inherited from

ScrollbarProps.inputMode

#### Defined in

node_modules/@types/react/index.d.ts:1800

---

### integrity

• `Optional` **integrity**: `string`

#### Inherited from

ScrollbarProps.integrity

#### Defined in

node_modules/@types/react/index.d.ts:1856

---

### is

• `Optional` **is**: `string`

Specify that a standard HTML element should behave like a defined custom built-in element

**`see`** https://html.spec.whatwg.org/multipage/custom-elements.html#attr-is

#### Inherited from

ScrollbarProps.is

#### Defined in

node_modules/@types/react/index.d.ts:1805

---

### isShowShadow

• `Optional` **isShowShadow**: `boolean`

#### Defined in

[src/components/scrollable/index.tsx:6](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/scrollable/index.tsx#L6)

---

### itemID

• `Optional` **itemID**: `string`

#### Inherited from

ScrollbarProps.itemID

#### Defined in

node_modules/@types/react/index.d.ts:1789

---

### itemProp

• `Optional` **itemProp**: `string`

#### Inherited from

ScrollbarProps.itemProp

#### Defined in

node_modules/@types/react/index.d.ts:1786

---

### itemRef

• `Optional` **itemRef**: `string`

#### Inherited from

ScrollbarProps.itemRef

#### Defined in

node_modules/@types/react/index.d.ts:1790

---

### itemScope

• `Optional` **itemScope**: `boolean`

#### Inherited from

ScrollbarProps.itemScope

#### Defined in

node_modules/@types/react/index.d.ts:1787

---

### itemType

• `Optional` **itemType**: `string`

#### Inherited from

ScrollbarProps.itemType

#### Defined in

node_modules/@types/react/index.d.ts:1788

---

### key

• `Optional` **key**: `null` \| `Key`

#### Inherited from

ScrollbarProps.key

#### Defined in

node_modules/@types/react/index.d.ts:133

---

### keyParams

• `Optional` **keyParams**: `string`

#### Inherited from

ScrollbarProps.keyParams

#### Defined in

node_modules/@types/react/index.d.ts:1857

---

### keyType

• `Optional` **keyType**: `string`

#### Inherited from

ScrollbarProps.keyType

#### Defined in

node_modules/@types/react/index.d.ts:1858

---

### kind

• `Optional` **kind**: `string`

#### Inherited from

ScrollbarProps.kind

#### Defined in

node_modules/@types/react/index.d.ts:1859

---

### label

• `Optional` **label**: `string`

#### Inherited from

ScrollbarProps.label

#### Defined in

node_modules/@types/react/index.d.ts:1860

---

### lang

• `Optional` **lang**: `string`

#### Inherited from

ScrollbarProps.lang

#### Defined in

node_modules/@types/react/index.d.ts:1756

---

### list

• `Optional` **list**: `string`

#### Inherited from

ScrollbarProps.list

#### Defined in

node_modules/@types/react/index.d.ts:1861

---

### loop

• `Optional` **loop**: `boolean`

#### Inherited from

ScrollbarProps.loop

#### Defined in

node_modules/@types/react/index.d.ts:1862

---

### low

• `Optional` **low**: `number`

#### Inherited from

ScrollbarProps.low

#### Defined in

node_modules/@types/react/index.d.ts:1863

---

### manifest

• `Optional` **manifest**: `string`

#### Inherited from

ScrollbarProps.manifest

#### Defined in

node_modules/@types/react/index.d.ts:1864

---

### marginHeight

• `Optional` **marginHeight**: `number`

#### Inherited from

ScrollbarProps.marginHeight

#### Defined in

node_modules/@types/react/index.d.ts:1865

---

### marginWidth

• `Optional` **marginWidth**: `number`

#### Inherited from

ScrollbarProps.marginWidth

#### Defined in

node_modules/@types/react/index.d.ts:1866

---

### max

• `Optional` **max**: `string` \| `number`

#### Inherited from

ScrollbarProps.max

#### Defined in

node_modules/@types/react/index.d.ts:1867

---

### maxLength

• `Optional` **maxLength**: `number`

#### Inherited from

ScrollbarProps.maxLength

#### Defined in

node_modules/@types/react/index.d.ts:1868

---

### maximalThumbSize

• `Optional` **maximalThumbSize**: `number`

#### Inherited from

ScrollbarProps.maximalThumbSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:23

---

### maximalThumbXSize

• `Optional` **maximalThumbXSize**: `number`

#### Inherited from

ScrollbarProps.maximalThumbXSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:25

---

### maximalThumbYSize

• `Optional` **maximalThumbYSize**: `number`

#### Inherited from

ScrollbarProps.maximalThumbYSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:27

---

### media

• `Optional` **media**: `string`

#### Inherited from

ScrollbarProps.media

#### Defined in

node_modules/@types/react/index.d.ts:1869

---

### mediaGroup

• `Optional` **mediaGroup**: `string`

#### Inherited from

ScrollbarProps.mediaGroup

#### Defined in

node_modules/@types/react/index.d.ts:1870

---

### method

• `Optional` **method**: `string`

#### Inherited from

ScrollbarProps.method

#### Defined in

node_modules/@types/react/index.d.ts:1871

---

### min

• `Optional` **min**: `string` \| `number`

#### Inherited from

ScrollbarProps.min

#### Defined in

node_modules/@types/react/index.d.ts:1872

---

### minLength

• `Optional` **minLength**: `number`

#### Inherited from

ScrollbarProps.minLength

#### Defined in

node_modules/@types/react/index.d.ts:1873

---

### minimalThumbSize

• `Optional` **minimalThumbSize**: `number`

#### Inherited from

ScrollbarProps.minimalThumbSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:22

---

### minimalThumbXSize

• `Optional` **minimalThumbXSize**: `number`

#### Inherited from

ScrollbarProps.minimalThumbXSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:24

---

### minimalThumbYSize

• `Optional` **minimalThumbYSize**: `number`

#### Inherited from

ScrollbarProps.minimalThumbYSize

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:26

---

### mobileNative

• `Optional` **mobileNative**: `boolean`

#### Inherited from

ScrollbarProps.mobileNative

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:12

---

### momentum

• `Optional` **momentum**: `boolean`

#### Inherited from

ScrollbarProps.momentum

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:10

---

### multiple

• `Optional` **multiple**: `boolean`

#### Inherited from

ScrollbarProps.multiple

#### Defined in

node_modules/@types/react/index.d.ts:1874

---

### muted

• `Optional` **muted**: `boolean`

#### Inherited from

ScrollbarProps.muted

#### Defined in

node_modules/@types/react/index.d.ts:1875

---

### name

• `Optional` **name**: `string`

#### Inherited from

ScrollbarProps.name

#### Defined in

node_modules/@types/react/index.d.ts:1876

---

### native

• `Optional` **native**: `boolean`

#### Inherited from

ScrollbarProps.native

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:11

---

### noDefaultStyles

• `Optional` **noDefaultStyles**: `boolean`

#### Inherited from

ScrollbarProps.noDefaultStyles

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:36

---

### noScroll

• `Optional` **noScroll**: `boolean`

#### Inherited from

ScrollbarProps.noScroll

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:15

---

### noScrollX

• `Optional` **noScrollX**: `boolean`

#### Inherited from

ScrollbarProps.noScrollX

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:13

---

### noScrollY

• `Optional` **noScrollY**: `boolean`

#### Inherited from

ScrollbarProps.noScrollY

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:14

---

### noValidate

• `Optional` **noValidate**: `boolean`

#### Inherited from

ScrollbarProps.noValidate

#### Defined in

node_modules/@types/react/index.d.ts:1878

---

### nonce

• `Optional` **nonce**: `string`

#### Inherited from

ScrollbarProps.nonce

#### Defined in

node_modules/@types/react/index.d.ts:1877

---

### onAbort

• `Optional` **onAbort**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAbort

#### Defined in

node_modules/@types/react/index.d.ts:1401

---

### onAbortCapture

• `Optional` **onAbortCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAbortCapture

#### Defined in

node_modules/@types/react/index.d.ts:1402

---

### onAnimationEnd

• `Optional` **onAnimationEnd**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationEnd

#### Defined in

node_modules/@types/react/index.d.ts:1531

---

### onAnimationEndCapture

• `Optional` **onAnimationEndCapture**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1532

---

### onAnimationIteration

• `Optional` **onAnimationIteration**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationIteration

#### Defined in

node_modules/@types/react/index.d.ts:1533

---

### onAnimationIterationCapture

• `Optional` **onAnimationIterationCapture**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationIterationCapture

#### Defined in

node_modules/@types/react/index.d.ts:1534

---

### onAnimationStart

• `Optional` **onAnimationStart**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationStart

#### Defined in

node_modules/@types/react/index.d.ts:1529

---

### onAnimationStartCapture

• `Optional` **onAnimationStartCapture**: `AnimationEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAnimationStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1530

---

### onAuxClick

• `Optional` **onAuxClick**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAuxClick

#### Defined in

node_modules/@types/react/index.d.ts:1447

---

### onAuxClickCapture

• `Optional` **onAuxClickCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onAuxClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1448

---

### onBeforeInput

• `Optional` **onBeforeInput**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onBeforeInput

#### Defined in

node_modules/@types/react/index.d.ts:1375

---

### onBeforeInputCapture

• `Optional` **onBeforeInputCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onBeforeInputCapture

#### Defined in

node_modules/@types/react/index.d.ts:1376

---

### onBlur

• `Optional` **onBlur**: `FocusEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onBlur

#### Defined in

node_modules/@types/react/index.d.ts:1369

---

### onBlurCapture

• `Optional` **onBlurCapture**: `FocusEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onBlurCapture

#### Defined in

node_modules/@types/react/index.d.ts:1370

---

### onCanPlay

• `Optional` **onCanPlay**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCanPlay

#### Defined in

node_modules/@types/react/index.d.ts:1403

---

### onCanPlayCapture

• `Optional` **onCanPlayCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCanPlayCapture

#### Defined in

node_modules/@types/react/index.d.ts:1404

---

### onCanPlayThrough

• `Optional` **onCanPlayThrough**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCanPlayThrough

#### Defined in

node_modules/@types/react/index.d.ts:1405

---

### onCanPlayThroughCapture

• `Optional` **onCanPlayThroughCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCanPlayThroughCapture

#### Defined in

node_modules/@types/react/index.d.ts:1406

---

### onChange

• `Optional` **onChange**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onChange

#### Defined in

node_modules/@types/react/index.d.ts:1373

---

### onChangeCapture

• `Optional` **onChangeCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1374

---

### onClick

• `Optional` **onClick**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onClick

#### Defined in

node_modules/@types/react/index.d.ts:1449

---

### onClickCapture

• `Optional` **onClickCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1450

---

### onCompositionEnd

• `Optional` **onCompositionEnd**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionEnd

#### Defined in

node_modules/@types/react/index.d.ts:1359

---

### onCompositionEndCapture

• `Optional` **onCompositionEndCapture**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1360

---

### onCompositionStart

• `Optional` **onCompositionStart**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionStart

#### Defined in

node_modules/@types/react/index.d.ts:1361

---

### onCompositionStartCapture

• `Optional` **onCompositionStartCapture**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1362

---

### onCompositionUpdate

• `Optional` **onCompositionUpdate**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionUpdate

#### Defined in

node_modules/@types/react/index.d.ts:1363

---

### onCompositionUpdateCapture

• `Optional` **onCompositionUpdateCapture**: `CompositionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCompositionUpdateCapture

#### Defined in

node_modules/@types/react/index.d.ts:1364

---

### onContextMenu

• `Optional` **onContextMenu**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onContextMenu

#### Defined in

node_modules/@types/react/index.d.ts:1451

---

### onContextMenuCapture

• `Optional` **onContextMenuCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onContextMenuCapture

#### Defined in

node_modules/@types/react/index.d.ts:1452

---

### onCopy

• `Optional` **onCopy**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCopy

#### Defined in

node_modules/@types/react/index.d.ts:1351

---

### onCopyCapture

• `Optional` **onCopyCapture**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCopyCapture

#### Defined in

node_modules/@types/react/index.d.ts:1352

---

### onCut

• `Optional` **onCut**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCut

#### Defined in

node_modules/@types/react/index.d.ts:1353

---

### onCutCapture

• `Optional` **onCutCapture**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onCutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1354

---

### onDoubleClick

• `Optional` **onDoubleClick**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDoubleClick

#### Defined in

node_modules/@types/react/index.d.ts:1453

---

### onDoubleClickCapture

• `Optional` **onDoubleClickCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDoubleClickCapture

#### Defined in

node_modules/@types/react/index.d.ts:1454

---

### onDrag

• `Optional` **onDrag**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDrag

#### Defined in

node_modules/@types/react/index.d.ts:1455

---

### onDragCapture

• `Optional` **onDragCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragCapture

#### Defined in

node_modules/@types/react/index.d.ts:1456

---

### onDragEnd

• `Optional` **onDragEnd**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragEnd

#### Defined in

node_modules/@types/react/index.d.ts:1457

---

### onDragEndCapture

• `Optional` **onDragEndCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1458

---

### onDragEnter

• `Optional` **onDragEnter**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragEnter

#### Defined in

node_modules/@types/react/index.d.ts:1459

---

### onDragEnterCapture

• `Optional` **onDragEnterCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragEnterCapture

#### Defined in

node_modules/@types/react/index.d.ts:1460

---

### onDragExit

• `Optional` **onDragExit**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragExit

#### Defined in

node_modules/@types/react/index.d.ts:1461

---

### onDragExitCapture

• `Optional` **onDragExitCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragExitCapture

#### Defined in

node_modules/@types/react/index.d.ts:1462

---

### onDragLeave

• `Optional` **onDragLeave**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragLeave

#### Defined in

node_modules/@types/react/index.d.ts:1463

---

### onDragLeaveCapture

• `Optional` **onDragLeaveCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragLeaveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1464

---

### onDragOver

• `Optional` **onDragOver**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragOver

#### Defined in

node_modules/@types/react/index.d.ts:1465

---

### onDragOverCapture

• `Optional` **onDragOverCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1466

---

### onDragStart

• `Optional` **onDragStart**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragStart

#### Defined in

node_modules/@types/react/index.d.ts:1467

---

### onDragStartCapture

• `Optional` **onDragStartCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDragStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1468

---

### onDrop

• `Optional` **onDrop**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDrop

#### Defined in

node_modules/@types/react/index.d.ts:1469

---

### onDropCapture

• `Optional` **onDropCapture**: `DragEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDropCapture

#### Defined in

node_modules/@types/react/index.d.ts:1470

---

### onDurationChange

• `Optional` **onDurationChange**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDurationChange

#### Defined in

node_modules/@types/react/index.d.ts:1407

---

### onDurationChangeCapture

• `Optional` **onDurationChangeCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onDurationChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1408

---

### onEmptied

• `Optional` **onEmptied**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEmptied

#### Defined in

node_modules/@types/react/index.d.ts:1409

---

### onEmptiedCapture

• `Optional` **onEmptiedCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEmptiedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1410

---

### onEncrypted

• `Optional` **onEncrypted**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEncrypted

#### Defined in

node_modules/@types/react/index.d.ts:1411

---

### onEncryptedCapture

• `Optional` **onEncryptedCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEncryptedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1412

---

### onEnded

• `Optional` **onEnded**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEnded

#### Defined in

node_modules/@types/react/index.d.ts:1413

---

### onEndedCapture

• `Optional` **onEndedCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onEndedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1414

---

### onError

• `Optional` **onError**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onError

#### Defined in

node_modules/@types/react/index.d.ts:1389

---

### onErrorCapture

• `Optional` **onErrorCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onErrorCapture

#### Defined in

node_modules/@types/react/index.d.ts:1390

---

### onFocus

• `Optional` **onFocus**: `FocusEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onFocus

#### Defined in

node_modules/@types/react/index.d.ts:1367

---

### onFocusCapture

• `Optional` **onFocusCapture**: `FocusEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onFocusCapture

#### Defined in

node_modules/@types/react/index.d.ts:1368

---

### onGotPointerCapture

• `Optional` **onGotPointerCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onGotPointerCapture

#### Defined in

node_modules/@types/react/index.d.ts:1515

---

### onGotPointerCaptureCapture

• `Optional` **onGotPointerCaptureCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onGotPointerCaptureCapture

#### Defined in

node_modules/@types/react/index.d.ts:1516

---

### onInput

• `Optional` **onInput**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onInput

#### Defined in

node_modules/@types/react/index.d.ts:1377

---

### onInputCapture

• `Optional` **onInputCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onInputCapture

#### Defined in

node_modules/@types/react/index.d.ts:1378

---

### onInvalid

• `Optional` **onInvalid**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onInvalid

#### Defined in

node_modules/@types/react/index.d.ts:1383

---

### onInvalidCapture

• `Optional` **onInvalidCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onInvalidCapture

#### Defined in

node_modules/@types/react/index.d.ts:1384

---

### onKeyDown

• `Optional` **onKeyDown**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyDown

#### Defined in

node_modules/@types/react/index.d.ts:1393

---

### onKeyDownCapture

• `Optional` **onKeyDownCapture**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1394

---

### onKeyPress

• `Optional` **onKeyPress**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyPress

#### Defined in

node_modules/@types/react/index.d.ts:1395

---

### onKeyPressCapture

• `Optional` **onKeyPressCapture**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyPressCapture

#### Defined in

node_modules/@types/react/index.d.ts:1396

---

### onKeyUp

• `Optional` **onKeyUp**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyUp

#### Defined in

node_modules/@types/react/index.d.ts:1397

---

### onKeyUpCapture

• `Optional` **onKeyUpCapture**: `KeyboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onKeyUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1398

---

### onLoad

• `Optional` **onLoad**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoad

#### Defined in

node_modules/@types/react/index.d.ts:1387

---

### onLoadCapture

• `Optional` **onLoadCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadCapture

#### Defined in

node_modules/@types/react/index.d.ts:1388

---

### onLoadStart

• `Optional` **onLoadStart**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadStart

#### Defined in

node_modules/@types/react/index.d.ts:1419

---

### onLoadStartCapture

• `Optional` **onLoadStartCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1420

---

### onLoadedData

• `Optional` **onLoadedData**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadedData

#### Defined in

node_modules/@types/react/index.d.ts:1415

---

### onLoadedDataCapture

• `Optional` **onLoadedDataCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadedDataCapture

#### Defined in

node_modules/@types/react/index.d.ts:1416

---

### onLoadedMetadata

• `Optional` **onLoadedMetadata**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadedMetadata

#### Defined in

node_modules/@types/react/index.d.ts:1417

---

### onLoadedMetadataCapture

• `Optional` **onLoadedMetadataCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLoadedMetadataCapture

#### Defined in

node_modules/@types/react/index.d.ts:1418

---

### onLostPointerCapture

• `Optional` **onLostPointerCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLostPointerCapture

#### Defined in

node_modules/@types/react/index.d.ts:1517

---

### onLostPointerCaptureCapture

• `Optional` **onLostPointerCaptureCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onLostPointerCaptureCapture

#### Defined in

node_modules/@types/react/index.d.ts:1518

---

### onMouseDown

• `Optional` **onMouseDown**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseDown

#### Defined in

node_modules/@types/react/index.d.ts:1471

---

### onMouseDownCapture

• `Optional` **onMouseDownCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1472

---

### onMouseEnter

• `Optional` **onMouseEnter**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseEnter

#### Defined in

node_modules/@types/react/index.d.ts:1473

---

### onMouseLeave

• `Optional` **onMouseLeave**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseLeave

#### Defined in

node_modules/@types/react/index.d.ts:1474

---

### onMouseMove

• `Optional` **onMouseMove**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseMove

#### Defined in

node_modules/@types/react/index.d.ts:1475

---

### onMouseMoveCapture

• `Optional` **onMouseMoveCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1476

---

### onMouseOut

• `Optional` **onMouseOut**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseOut

#### Defined in

node_modules/@types/react/index.d.ts:1477

---

### onMouseOutCapture

• `Optional` **onMouseOutCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseOutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1478

---

### onMouseOver

• `Optional` **onMouseOver**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseOver

#### Defined in

node_modules/@types/react/index.d.ts:1479

---

### onMouseOverCapture

• `Optional` **onMouseOverCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1480

---

### onMouseUp

• `Optional` **onMouseUp**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseUp

#### Defined in

node_modules/@types/react/index.d.ts:1481

---

### onMouseUpCapture

• `Optional` **onMouseUpCapture**: `MouseEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onMouseUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1482

---

### onPaste

• `Optional` **onPaste**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPaste

#### Defined in

node_modules/@types/react/index.d.ts:1355

---

### onPasteCapture

• `Optional` **onPasteCapture**: `ClipboardEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPasteCapture

#### Defined in

node_modules/@types/react/index.d.ts:1356

---

### onPause

• `Optional` **onPause**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPause

#### Defined in

node_modules/@types/react/index.d.ts:1421

---

### onPauseCapture

• `Optional` **onPauseCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPauseCapture

#### Defined in

node_modules/@types/react/index.d.ts:1422

---

### onPlay

• `Optional` **onPlay**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPlay

#### Defined in

node_modules/@types/react/index.d.ts:1423

---

### onPlayCapture

• `Optional` **onPlayCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPlayCapture

#### Defined in

node_modules/@types/react/index.d.ts:1424

---

### onPlaying

• `Optional` **onPlaying**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPlaying

#### Defined in

node_modules/@types/react/index.d.ts:1425

---

### onPlayingCapture

• `Optional` **onPlayingCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPlayingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1426

---

### onPointerCancel

• `Optional` **onPointerCancel**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerCancel

#### Defined in

node_modules/@types/react/index.d.ts:1505

---

### onPointerCancelCapture

• `Optional` **onPointerCancelCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerCancelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1506

---

### onPointerDown

• `Optional` **onPointerDown**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerDown

#### Defined in

node_modules/@types/react/index.d.ts:1499

---

### onPointerDownCapture

• `Optional` **onPointerDownCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerDownCapture

#### Defined in

node_modules/@types/react/index.d.ts:1500

---

### onPointerEnter

• `Optional` **onPointerEnter**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerEnter

#### Defined in

node_modules/@types/react/index.d.ts:1507

---

### onPointerEnterCapture

• `Optional` **onPointerEnterCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerEnterCapture

#### Defined in

node_modules/@types/react/index.d.ts:1508

---

### onPointerLeave

• `Optional` **onPointerLeave**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerLeave

#### Defined in

node_modules/@types/react/index.d.ts:1509

---

### onPointerLeaveCapture

• `Optional` **onPointerLeaveCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerLeaveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1510

---

### onPointerMove

• `Optional` **onPointerMove**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerMove

#### Defined in

node_modules/@types/react/index.d.ts:1501

---

### onPointerMoveCapture

• `Optional` **onPointerMoveCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1502

---

### onPointerOut

• `Optional` **onPointerOut**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerOut

#### Defined in

node_modules/@types/react/index.d.ts:1513

---

### onPointerOutCapture

• `Optional` **onPointerOutCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerOutCapture

#### Defined in

node_modules/@types/react/index.d.ts:1514

---

### onPointerOver

• `Optional` **onPointerOver**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerOver

#### Defined in

node_modules/@types/react/index.d.ts:1511

---

### onPointerOverCapture

• `Optional` **onPointerOverCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerOverCapture

#### Defined in

node_modules/@types/react/index.d.ts:1512

---

### onPointerUp

• `Optional` **onPointerUp**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerUp

#### Defined in

node_modules/@types/react/index.d.ts:1503

---

### onPointerUpCapture

• `Optional` **onPointerUpCapture**: `PointerEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onPointerUpCapture

#### Defined in

node_modules/@types/react/index.d.ts:1504

---

### onProgress

• `Optional` **onProgress**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onProgress

#### Defined in

node_modules/@types/react/index.d.ts:1427

---

### onProgressCapture

• `Optional` **onProgressCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onProgressCapture

#### Defined in

node_modules/@types/react/index.d.ts:1428

---

### onRateChange

• `Optional` **onRateChange**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onRateChange

#### Defined in

node_modules/@types/react/index.d.ts:1429

---

### onRateChangeCapture

• `Optional` **onRateChangeCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onRateChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1430

---

### onReset

• `Optional` **onReset**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onReset

#### Defined in

node_modules/@types/react/index.d.ts:1379

---

### onResetCapture

• `Optional` **onResetCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onResetCapture

#### Defined in

node_modules/@types/react/index.d.ts:1380

---

### onScroll

• `Optional` **onScroll**: `UIEventHandler`<`HTMLDivElement`\> & (`scrollValues`: `ScrollState`, `prevScrollState`: `ScrollState`) => `void`

#### Inherited from

ScrollbarProps.onScroll

#### Defined in

node_modules/@types/react/index.d.ts:1521

---

### onScrollCapture

• `Optional` **onScrollCapture**: `UIEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onScrollCapture

#### Defined in

node_modules/@types/react/index.d.ts:1522

---

### onSeeked

• `Optional` **onSeeked**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSeeked

#### Defined in

node_modules/@types/react/index.d.ts:1431

---

### onSeekedCapture

• `Optional` **onSeekedCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSeekedCapture

#### Defined in

node_modules/@types/react/index.d.ts:1432

---

### onSeeking

• `Optional` **onSeeking**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSeeking

#### Defined in

node_modules/@types/react/index.d.ts:1433

---

### onSeekingCapture

• `Optional` **onSeekingCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSeekingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1434

---

### onSelect

• `Optional` **onSelect**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSelect

#### Defined in

node_modules/@types/react/index.d.ts:1485

---

### onSelectCapture

• `Optional` **onSelectCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSelectCapture

#### Defined in

node_modules/@types/react/index.d.ts:1486

---

### onStalled

• `Optional` **onStalled**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onStalled

#### Defined in

node_modules/@types/react/index.d.ts:1435

---

### onStalledCapture

• `Optional` **onStalledCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onStalledCapture

#### Defined in

node_modules/@types/react/index.d.ts:1436

---

### onSubmit

• `Optional` **onSubmit**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSubmit

#### Defined in

node_modules/@types/react/index.d.ts:1381

---

### onSubmitCapture

• `Optional` **onSubmitCapture**: `FormEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSubmitCapture

#### Defined in

node_modules/@types/react/index.d.ts:1382

---

### onSuspend

• `Optional` **onSuspend**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSuspend

#### Defined in

node_modules/@types/react/index.d.ts:1437

---

### onSuspendCapture

• `Optional` **onSuspendCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onSuspendCapture

#### Defined in

node_modules/@types/react/index.d.ts:1438

---

### onTimeUpdate

• `Optional` **onTimeUpdate**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTimeUpdate

#### Defined in

node_modules/@types/react/index.d.ts:1439

---

### onTimeUpdateCapture

• `Optional` **onTimeUpdateCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTimeUpdateCapture

#### Defined in

node_modules/@types/react/index.d.ts:1440

---

### onTouchCancel

• `Optional` **onTouchCancel**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchCancel

#### Defined in

node_modules/@types/react/index.d.ts:1489

---

### onTouchCancelCapture

• `Optional` **onTouchCancelCapture**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchCancelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1490

---

### onTouchEnd

• `Optional` **onTouchEnd**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchEnd

#### Defined in

node_modules/@types/react/index.d.ts:1491

---

### onTouchEndCapture

• `Optional` **onTouchEndCapture**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1492

---

### onTouchMove

• `Optional` **onTouchMove**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchMove

#### Defined in

node_modules/@types/react/index.d.ts:1493

---

### onTouchMoveCapture

• `Optional` **onTouchMoveCapture**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchMoveCapture

#### Defined in

node_modules/@types/react/index.d.ts:1494

---

### onTouchStart

• `Optional` **onTouchStart**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchStart

#### Defined in

node_modules/@types/react/index.d.ts:1495

---

### onTouchStartCapture

• `Optional` **onTouchStartCapture**: `TouchEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTouchStartCapture

#### Defined in

node_modules/@types/react/index.d.ts:1496

---

### onTransitionEnd

• `Optional` **onTransitionEnd**: `TransitionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTransitionEnd

#### Defined in

node_modules/@types/react/index.d.ts:1537

---

### onTransitionEndCapture

• `Optional` **onTransitionEndCapture**: `TransitionEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onTransitionEndCapture

#### Defined in

node_modules/@types/react/index.d.ts:1538

---

### onVolumeChange

• `Optional` **onVolumeChange**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onVolumeChange

#### Defined in

node_modules/@types/react/index.d.ts:1441

---

### onVolumeChangeCapture

• `Optional` **onVolumeChangeCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onVolumeChangeCapture

#### Defined in

node_modules/@types/react/index.d.ts:1442

---

### onWaiting

• `Optional` **onWaiting**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onWaiting

#### Defined in

node_modules/@types/react/index.d.ts:1443

---

### onWaitingCapture

• `Optional` **onWaitingCapture**: `ReactEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onWaitingCapture

#### Defined in

node_modules/@types/react/index.d.ts:1444

---

### onWheel

• `Optional` **onWheel**: `WheelEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onWheel

#### Defined in

node_modules/@types/react/index.d.ts:1525

---

### onWheelCapture

• `Optional` **onWheelCapture**: `WheelEventHandler`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.onWheelCapture

#### Defined in

node_modules/@types/react/index.d.ts:1526

---

### open

• `Optional` **open**: `boolean`

#### Inherited from

ScrollbarProps.open

#### Defined in

node_modules/@types/react/index.d.ts:1879

---

### optimum

• `Optional` **optimum**: `number`

#### Inherited from

ScrollbarProps.optimum

#### Defined in

node_modules/@types/react/index.d.ts:1880

---

### pattern

• `Optional` **pattern**: `string`

#### Inherited from

ScrollbarProps.pattern

#### Defined in

node_modules/@types/react/index.d.ts:1881

---

### permanentTrackX

• `Optional` **permanentTrackX**: `boolean`

#### Inherited from

ScrollbarProps.permanentTrackX

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:16

---

### permanentTrackY

• `Optional` **permanentTrackY**: `boolean`

#### Inherited from

ScrollbarProps.permanentTrackY

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:17

---

### permanentTracks

• `Optional` **permanentTracks**: `boolean`

#### Inherited from

ScrollbarProps.permanentTracks

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:18

---

### placeholder

• `Optional` **placeholder**: `string`

#### Inherited from

ScrollbarProps.placeholder

#### Defined in

node_modules/@types/react/index.d.ts:1882

---

### playsInline

• `Optional` **playsInline**: `boolean`

#### Inherited from

ScrollbarProps.playsInline

#### Defined in

node_modules/@types/react/index.d.ts:1883

---

### poster

• `Optional` **poster**: `string`

#### Inherited from

ScrollbarProps.poster

#### Defined in

node_modules/@types/react/index.d.ts:1884

---

### prefix

• `Optional` **prefix**: `string`

#### Inherited from

ScrollbarProps.prefix

#### Defined in

node_modules/@types/react/index.d.ts:1775

---

### preload

• `Optional` **preload**: `string`

#### Inherited from

ScrollbarProps.preload

#### Defined in

node_modules/@types/react/index.d.ts:1885

---

### property

• `Optional` **property**: `string`

#### Inherited from

ScrollbarProps.property

#### Defined in

node_modules/@types/react/index.d.ts:1776

---

### radioGroup

• `Optional` **radioGroup**: `string`

#### Inherited from

ScrollbarProps.radioGroup

#### Defined in

node_modules/@types/react/index.d.ts:1766

---

### readOnly

• `Optional` **readOnly**: `boolean`

#### Inherited from

ScrollbarProps.readOnly

#### Defined in

node_modules/@types/react/index.d.ts:1886

---

### ref

• `Optional` **ref**: `LegacyRef`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.ref

#### Defined in

node_modules/@types/react/index.d.ts:139

---

### rel

• `Optional` **rel**: `string`

#### Inherited from

ScrollbarProps.rel

#### Defined in

node_modules/@types/react/index.d.ts:1887

---

### removeTrackXWhenNotUsed

• `Optional` **removeTrackXWhenNotUsed**: `boolean`

#### Inherited from

ScrollbarProps.removeTrackXWhenNotUsed

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:21

---

### removeTrackYWhenNotUsed

• `Optional` **removeTrackYWhenNotUsed**: `boolean`

#### Inherited from

ScrollbarProps.removeTrackYWhenNotUsed

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:20

---

### removeTracksWhenNotUsed

• `Optional` **removeTracksWhenNotUsed**: `boolean`

#### Inherited from

ScrollbarProps.removeTracksWhenNotUsed

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:19

---

### renderer

• `Optional` **renderer**: `ElementRenderer`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.renderer

#### Defined in

node_modules/react-scrollbars-custom/dist/types/types.d.ts:20

---

### required

• `Optional` **required**: `boolean`

#### Inherited from

ScrollbarProps.required

#### Defined in

node_modules/@types/react/index.d.ts:1888

---

### resource

• `Optional` **resource**: `string`

#### Inherited from

ScrollbarProps.resource

#### Defined in

node_modules/@types/react/index.d.ts:1777

---

### results

• `Optional` **results**: `number`

#### Inherited from

ScrollbarProps.results

#### Defined in

node_modules/@types/react/index.d.ts:1791

---

### reversed

• `Optional` **reversed**: `boolean`

#### Inherited from

ScrollbarProps.reversed

#### Defined in

node_modules/@types/react/index.d.ts:1889

---

### role

• `Optional` **role**: `string`

#### Inherited from

ScrollbarProps.role

#### Defined in

node_modules/@types/react/index.d.ts:1769

---

### rowSpan

• `Optional` **rowSpan**: `number`

#### Inherited from

ScrollbarProps.rowSpan

#### Defined in

node_modules/@types/react/index.d.ts:1891

---

### rows

• `Optional` **rows**: `number`

#### Inherited from

ScrollbarProps.rows

#### Defined in

node_modules/@types/react/index.d.ts:1890

---

### rtl

• `Optional` **rtl**: `boolean`

#### Inherited from

ScrollbarProps.rtl

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:9

---

### sandbox

• `Optional` **sandbox**: `string`

#### Inherited from

ScrollbarProps.sandbox

#### Defined in

node_modules/@types/react/index.d.ts:1892

---

### scope

• `Optional` **scope**: `string`

#### Inherited from

ScrollbarProps.scope

#### Defined in

node_modules/@types/react/index.d.ts:1893

---

### scoped

• `Optional` **scoped**: `boolean`

#### Inherited from

ScrollbarProps.scoped

#### Defined in

node_modules/@types/react/index.d.ts:1894

---

### scrollDetectionThreshold

• `Optional` **scrollDetectionThreshold**: `number`

#### Inherited from

ScrollbarProps.scrollDetectionThreshold

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:32

---

### scrollLeft

• `Optional` **scrollLeft**: `number`

#### Inherited from

ScrollbarProps.scrollLeft

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:31

---

### scrollTop

• `Optional` **scrollTop**: `number`

#### Inherited from

ScrollbarProps.scrollTop

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:30

---

### scrollbarWidth

• `Optional` **scrollbarWidth**: `number`

#### Inherited from

ScrollbarProps.scrollbarWidth

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:28

---

### scrollerProps

• `Optional` **scrollerProps**: `ElementPropsWithElementRefAndRenderer`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.scrollerProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:45

---

### scrolling

• `Optional` **scrolling**: `string`

#### Inherited from

ScrollbarProps.scrolling

#### Defined in

node_modules/@types/react/index.d.ts:1895

---

### seamless

• `Optional` **seamless**: `boolean`

#### Inherited from

ScrollbarProps.seamless

#### Defined in

node_modules/@types/react/index.d.ts:1896

---

### security

• `Optional` **security**: `string`

#### Inherited from

ScrollbarProps.security

#### Defined in

node_modules/@types/react/index.d.ts:1792

---

### selected

• `Optional` **selected**: `boolean`

#### Inherited from

ScrollbarProps.selected

#### Defined in

node_modules/@types/react/index.d.ts:1897

---

### shape

• `Optional` **shape**: `string`

#### Inherited from

ScrollbarProps.shape

#### Defined in

node_modules/@types/react/index.d.ts:1898

---

### size

• `Optional` **size**: `number`

#### Inherited from

ScrollbarProps.size

#### Defined in

node_modules/@types/react/index.d.ts:1899

---

### sizes

• `Optional` **sizes**: `string`

#### Inherited from

ScrollbarProps.sizes

#### Defined in

node_modules/@types/react/index.d.ts:1900

---

### slot

• `Optional` **slot**: `string`

#### Inherited from

ScrollbarProps.slot

#### Defined in

node_modules/@types/react/index.d.ts:1758

---

### span

• `Optional` **span**: `number`

#### Inherited from

ScrollbarProps.span

#### Defined in

node_modules/@types/react/index.d.ts:1901

---

### spellCheck

• `Optional` **spellCheck**: `Booleanish`

#### Inherited from

ScrollbarProps.spellCheck

#### Defined in

node_modules/@types/react/index.d.ts:1759

---

### src

• `Optional` **src**: `string`

#### Inherited from

ScrollbarProps.src

#### Defined in

node_modules/@types/react/index.d.ts:1902

---

### srcDoc

• `Optional` **srcDoc**: `string`

#### Inherited from

ScrollbarProps.srcDoc

#### Defined in

node_modules/@types/react/index.d.ts:1903

---

### srcLang

• `Optional` **srcLang**: `string`

#### Inherited from

ScrollbarProps.srcLang

#### Defined in

node_modules/@types/react/index.d.ts:1904

---

### srcSet

• `Optional` **srcSet**: `string`

#### Inherited from

ScrollbarProps.srcSet

#### Defined in

node_modules/@types/react/index.d.ts:1905

---

### start

• `Optional` **start**: `number`

#### Inherited from

ScrollbarProps.start

#### Defined in

node_modules/@types/react/index.d.ts:1906

---

### step

• `Optional` **step**: `string` \| `number`

#### Inherited from

ScrollbarProps.step

#### Defined in

node_modules/@types/react/index.d.ts:1907

---

### style

• `Optional` **style**: `CSSProperties`

#### Inherited from

ScrollbarProps.style

#### Defined in

node_modules/@types/react/index.d.ts:1760

---

### summary

• `Optional` **summary**: `string`

#### Inherited from

ScrollbarProps.summary

#### Defined in

node_modules/@types/react/index.d.ts:1908

---

### suppressContentEditableWarning

• `Optional` **suppressContentEditableWarning**: `boolean`

#### Inherited from

ScrollbarProps.suppressContentEditableWarning

#### Defined in

node_modules/@types/react/index.d.ts:1744

---

### suppressHydrationWarning

• `Optional` **suppressHydrationWarning**: `boolean`

#### Inherited from

ScrollbarProps.suppressHydrationWarning

#### Defined in

node_modules/@types/react/index.d.ts:1745

---

### tabIndex

• `Optional` **tabIndex**: `number`

#### Inherited from

ScrollbarProps.tabIndex

#### Defined in

node_modules/@types/react/index.d.ts:1761

---

### target

• `Optional` **target**: `string`

#### Inherited from

ScrollbarProps.target

#### Defined in

node_modules/@types/react/index.d.ts:1909

---

### thumbStyle

• `Optional` **thumbStyle**: `CSSProperties`

#### Defined in

[src/components/scrollable/index.tsx:8](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/scrollable/index.tsx#L8)

---

### thumbXProps

• `Optional` **thumbXProps**: `Pick`<`ScrollbarThumbProps`, `"elementRef"` \| `"renderer"` \| keyof `HTMLProps`<`HTMLDivElement`\>\>

#### Inherited from

ScrollbarProps.thumbXProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:49

---

### thumbYProps

• `Optional` **thumbYProps**: `Pick`<`ScrollbarThumbProps`, `"elementRef"` \| `"renderer"` \| keyof `HTMLProps`<`HTMLDivElement`\>\>

#### Inherited from

ScrollbarProps.thumbYProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:50

---

### title

• `Optional` **title**: `string`

#### Inherited from

ScrollbarProps.title

#### Defined in

node_modules/@types/react/index.d.ts:1762

---

### trackClickBehavior

• `Optional` **trackClickBehavior**: `TRACK_CLICK_BEHAVIOR`

#### Inherited from

ScrollbarProps.trackClickBehavior

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:43

---

### trackStyle

• `Optional` **trackStyle**: `CSSProperties`

#### Defined in

[src/components/scrollable/index.tsx:7](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/scrollable/index.tsx#L7)

---

### trackXProps

• `Optional` **trackXProps**: `Pick`<`ScrollbarTrackProps`, `"elementRef"` \| `"renderer"` \| keyof `HTMLProps`<`HTMLDivElement`\>\>

#### Inherited from

ScrollbarProps.trackXProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:47

---

### trackYProps

• `Optional` **trackYProps**: `Pick`<`ScrollbarTrackProps`, `"elementRef"` \| `"renderer"` \| keyof `HTMLProps`<`HTMLDivElement`\>\>

#### Inherited from

ScrollbarProps.trackYProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:48

---

### translate

• `Optional` **translate**: `"yes"` \| `"no"`

#### Inherited from

ScrollbarProps.translate

#### Defined in

node_modules/@types/react/index.d.ts:1763

---

### translateContentSizeXToHolder

• `Optional` **translateContentSizeXToHolder**: `boolean`

#### Inherited from

ScrollbarProps.translateContentSizeXToHolder

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:35

---

### translateContentSizeYToHolder

• `Optional` **translateContentSizeYToHolder**: `boolean`

#### Inherited from

ScrollbarProps.translateContentSizeYToHolder

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:34

---

### translateContentSizesToHolder

• `Optional` **translateContentSizesToHolder**: `boolean`

#### Inherited from

ScrollbarProps.translateContentSizesToHolder

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:33

---

### type

• `Optional` **type**: `string`

#### Inherited from

ScrollbarProps.type

#### Defined in

node_modules/@types/react/index.d.ts:1910

---

### typeof

• `Optional` **typeof**: `string`

#### Inherited from

ScrollbarProps.typeof

#### Defined in

node_modules/@types/react/index.d.ts:1778

---

### unselectable

• `Optional` **unselectable**: `"on"` \| `"off"`

#### Inherited from

ScrollbarProps.unselectable

#### Defined in

node_modules/@types/react/index.d.ts:1793

---

### useMap

• `Optional` **useMap**: `string`

#### Inherited from

ScrollbarProps.useMap

#### Defined in

node_modules/@types/react/index.d.ts:1911

---

### value

• `Optional` **value**: `string` \| `number` \| readonly `string`[]

#### Inherited from

ScrollbarProps.value

#### Defined in

node_modules/@types/react/index.d.ts:1912

---

### vocab

• `Optional` **vocab**: `string`

#### Inherited from

ScrollbarProps.vocab

#### Defined in

node_modules/@types/react/index.d.ts:1779

---

### width

• `Optional` **width**: `string` \| `number`

#### Inherited from

ScrollbarProps.width

#### Defined in

node_modules/@types/react/index.d.ts:1913

---

### wmode

• `Optional` **wmode**: `string`

#### Inherited from

ScrollbarProps.wmode

#### Defined in

node_modules/@types/react/index.d.ts:1914

---

### wrap

• `Optional` **wrap**: `string`

#### Inherited from

ScrollbarProps.wrap

#### Defined in

node_modules/@types/react/index.d.ts:1915

---

### wrapperProps

• `Optional` **wrapperProps**: `ElementPropsWithElementRefAndRenderer`<`HTMLDivElement`\>

#### Inherited from

ScrollbarProps.wrapperProps

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:44

## Methods

### onScrollStart

▸ `Optional` **onScrollStart**(`scrollValues`): `void`

#### Parameters

| Name           | Type          |
| :------------- | :------------ |
| `scrollValues` | `ScrollState` |

#### Returns

`void`

#### Inherited from

ScrollbarProps.onScrollStart

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:53

---

### onScrollStop

▸ `Optional` **onScrollStop**(`scrollValues`): `void`

#### Parameters

| Name           | Type          |
| :------------- | :------------ |
| `scrollValues` | `ScrollState` |

#### Returns

`void`

#### Inherited from

ScrollbarProps.onScrollStop

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:54

---

### onUpdate

▸ `Optional` **onUpdate**(`scrollValues`, `prevScrollState`): `void`

#### Parameters

| Name              | Type          |
| :---------------- | :------------ |
| `scrollValues`    | `ScrollState` |
| `prevScrollState` | `ScrollState` |

#### Returns

`void`

#### Inherited from

ScrollbarProps.onUpdate

#### Defined in

node_modules/react-scrollbars-custom/dist/types/Scrollbar.d.ts:51
