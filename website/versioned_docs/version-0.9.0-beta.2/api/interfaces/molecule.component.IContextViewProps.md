---
id: 'molecule.component.IContextViewProps'
title: 'Interface: IContextViewProps'
sidebar_label: 'IContextViewProps'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[component](../namespaces/molecule.component).IContextViewProps

## Properties

### shadowOutline

• `Optional` **shadowOutline**: `boolean`

Default true

#### Defined in

[src/components/contextView/index.tsx:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L25)

## Methods

### render

▸ `Optional` **render**(): `ReactNode`

#### Returns

`ReactNode`

#### Defined in

[src/components/contextView/index.tsx:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/components/contextView/index.tsx#L26)
