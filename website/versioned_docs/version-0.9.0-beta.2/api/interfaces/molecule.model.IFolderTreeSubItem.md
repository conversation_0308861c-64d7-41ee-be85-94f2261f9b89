---
id: 'molecule.model.IFolderTreeSubItem'
title: 'Interface: IFolderTreeSubItem'
sidebar_label: 'IFolderTreeSubItem'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTreeSubItem

## Properties

### contextMenu

• `Optional` **contextMenu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:34](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L34)

---

### current

• `Optional` **current**: `null` \| [`IFolderTreeNodeProps`](molecule.model.IFolderTreeNodeProps)

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:36](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L36)

---

### data

• `Optional` **data**: [`IFolderTreeNodeProps`](molecule.model.IFolderTreeNodeProps)[]

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:33](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L33)

---

### folderPanelContextMenu

• `Optional` **folderPanelContextMenu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:35](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L35)
