---
id: 'molecule.model.IColorTheme'
title: 'Interface: IColorTheme'
sidebar_label: 'IColorTheme'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IColorTheme

## Properties

### colors

• `Optional` **colors**: [`IColors`](molecule.model.IColors)

#### Defined in

[src/model/colorTheme.ts:30](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L30)

---

### description

• `Optional` **description**: `string`

#### Defined in

[src/model/colorTheme.ts:28](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L28)

---

### id

• **id**: `string`

The id of component, theme will be applied by this ID

#### Defined in

[src/model/colorTheme.ts:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L23)

---

### label

• **label**: `string`

#### Defined in

[src/model/colorTheme.ts:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L24)

---

### name

• `Optional` **name**: `string`

#### Defined in

[src/model/colorTheme.ts:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L25)

---

### path

• `Optional` **path**: `string`

#### Defined in

[src/model/colorTheme.ts:27](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L27)

---

### semanticHighlighting

• `Optional` **semanticHighlighting**: `boolean`

The semanticTokenColors mappings as well as
the semanticHighlighting setting
allow to enhance the highlighting in the editor
More info visit: https://code.visualstudio.com/api/language-extensions/semantic-highlight-guide

#### Defined in

[src/model/colorTheme.ts:38](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L38)

---

### tokenColors

• `Optional` **tokenColors**: [`TokenColor`](molecule.model.TokenColor)[]

#### Defined in

[src/model/colorTheme.ts:31](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L31)

---

### type

• `Optional` **type**: [`ColorScheme`](../enums/molecule.model.ColorScheme)

#### Defined in

[src/model/colorTheme.ts:29](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L29)

---

### uiTheme

• `Optional` **uiTheme**: `string`

#### Defined in

[src/model/colorTheme.ts:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/colorTheme.ts#L26)
