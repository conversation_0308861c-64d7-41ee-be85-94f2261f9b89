---
id: 'molecule.model.IStatusBar'
title: 'Interface: IStatusBar'
sidebar_label: 'IStatusBar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IStatusBar

## Implemented by

-   [`StatusBarModel`](../classes/molecule.model.StatusBarModel)

## Properties

### contextMenu

• `Optional` **contextMenu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[src/model/workbench/statusBar.tsx:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/statusBar.tsx#L22)

---

### leftItems

• **leftItems**: [`IStatusBarItem`](molecule.model.IStatusBarItem)<`any`\>[]

#### Defined in

[src/model/workbench/statusBar.tsx:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/statusBar.tsx#L21)

---

### rightItems

• **rightItems**: [`IStatusBarItem`](molecule.model.IStatusBarItem)<`any`\>[]

#### Defined in

[src/model/workbench/statusBar.tsx:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/statusBar.tsx#L20)
