---
id: 'molecule.model.IRelatedInformation'
title: 'Interface: IRelatedInformation'
sidebar_label: 'IRelatedInformation'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IRelatedInformation

## Properties

### code

• **code**: `string`

#### Defined in

[src/model/problems.tsx:11](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L11)

---

### endColumn

• **endColumn**: `number`

#### Defined in

[src/model/problems.tsx:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L16)

---

### endLineNumber

• **endLineNumber**: `number`

#### Defined in

[src/model/problems.tsx:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L15)

---

### message

• **message**: `string`

#### Defined in

[src/model/problems.tsx:12](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L12)

---

### startColumn

• **startColumn**: `number`

#### Defined in

[src/model/problems.tsx:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L14)

---

### startLineNumber

• **startLineNumber**: `number`

#### Defined in

[src/model/problems.tsx:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L13)

---

### status

• **status**: [`MarkerSeverity`](../enums/molecule.model.MarkerSeverity)

#### Defined in

[src/model/problems.tsx:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/problems.tsx#L17)
