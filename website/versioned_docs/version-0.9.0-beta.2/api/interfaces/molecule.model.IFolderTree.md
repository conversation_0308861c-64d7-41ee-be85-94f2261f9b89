---
id: 'molecule.model.IFolderTree'
title: 'Interface: IFolderTree'
sidebar_label: 'IFolderTree'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTree

## Implemented by

-   [`IFolderTreeModel`](../classes/molecule.model.IFolderTreeModel)

## Properties

### entry

• `Optional` **entry**: `ReactNode`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:40](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L40)

---

### folderTree

• `Optional` **folderTree**: [`IFolderTreeSubItem`](molecule.model.IFolderTreeSubItem)

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:39](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L39)
