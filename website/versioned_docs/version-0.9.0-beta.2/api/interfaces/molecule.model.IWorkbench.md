---
id: 'molecule.model.IWorkbench'
title: 'Interface: IWorkbench'
sidebar_label: 'IWorkbench'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IWorkbench

## Properties

### activityBar

• **activityBar**: [`IActivityBar`](molecule.model.IActivityBar)

#### Defined in

[src/model/workbench/index.ts:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/index.ts#L15)

---

### menuBar

• **menuBar**: [`IMenuBar`](molecule.model.IMenuBar)

#### Defined in

[src/model/workbench/index.ts:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/index.ts#L16)

---

### panel

• **panel**: [`IPanel`](molecule.model.IPanel)

#### Defined in

[src/model/workbench/index.ts:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/index.ts#L14)

---

### sidebar

• **sidebar**: [`ISidebar`](molecule.model.ISidebar)

#### Defined in

[src/model/workbench/index.ts:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/index.ts#L18)

---

### statusBar

• **statusBar**: [`IStatusBar`](molecule.model.IStatusBar)

#### Defined in

[src/model/workbench/index.ts:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/index.ts#L17)
