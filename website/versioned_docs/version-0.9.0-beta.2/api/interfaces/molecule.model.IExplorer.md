---
id: 'molecule.model.IExplorer'
title: 'Interface: IExplorer'
sidebar_label: 'IExplorer'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IExplorer

## Implemented by

-   [`IExplorerModel`](../classes/molecule.model.IExplorerModel)

## Properties

### data

• **data**: [`IExplorerPanelItem`](molecule.model.IExplorerPanelItem)[]

#### Defined in

[src/model/workbench/explorer/explorer.tsx:39](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L39)

---

### headerToolBar

• `Optional` **headerToolBar**: [`IActionBarItemProps`](molecule.component.IActionBarItemProps)<`any`\>

#### Defined in

[src/model/workbench/explorer/explorer.tsx:40](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L40)
