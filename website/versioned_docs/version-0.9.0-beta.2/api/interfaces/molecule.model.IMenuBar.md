---
id: 'molecule.model.IMenuBar'
title: 'Interface: IMenuBar'
sidebar_label: 'IMenuBar'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IMenuBar

## Implemented by

-   [`MenuBarModel`](../classes/molecule.model.MenuBarModel)

## Properties

### data

• **data**: [`IMenuBarItem`](molecule.model.IMenuBarItem)[]

#### Defined in

[src/model/workbench/menuBar.ts:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/menuBar.ts#L25)

---

### logo

• `Optional` **logo**: `ReactNode`

#### Defined in

[src/model/workbench/menuBar.ts:27](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/menuBar.ts#L27)

---

### mode

• `Optional` **mode**: `"horizontal"` \| `"vertical"`

#### Defined in

[src/model/workbench/menuBar.ts:26](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/menuBar.ts#L26)
