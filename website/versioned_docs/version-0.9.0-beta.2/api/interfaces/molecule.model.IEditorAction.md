---
id: 'molecule.model.IEditorAction'
title: 'Interface: IEditorAction'
sidebar_label: 'IEditorAction'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IEditorAction

## Properties

### actions

• `Optional` **actions**: [`IEditorActionsProps`](molecule.model.IEditorActionsProps)[]

#### Defined in

[src/model/workbench/editor.ts:44](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L44)

---

### menu

• `Optional` **menu**: [`IMenuItemProps`](molecule.component.IMenuItemProps)[]

#### Defined in

[src/model/workbench/editor.ts:45](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L45)
