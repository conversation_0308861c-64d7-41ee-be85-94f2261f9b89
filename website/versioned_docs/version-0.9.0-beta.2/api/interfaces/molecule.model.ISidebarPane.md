---
id: 'molecule.model.ISidebarPane'
title: 'Interface: ISidebarPane'
sidebar_label: 'ISidebarPane'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ISidebarPane

## Properties

### id

• **id**: `UniqueId`

#### Defined in

[src/model/workbench/sidebar.ts:4](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/sidebar.ts#L4)

---

### title

• `Optional` **title**: `string`

#### Defined in

[src/model/workbench/sidebar.ts:5](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/sidebar.ts#L5)

## Methods

### render

▸ `Optional` **render**(): `ReactNode`

#### Returns

`ReactNode`

#### Defined in

[src/model/workbench/sidebar.ts:6](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/sidebar.ts#L6)
