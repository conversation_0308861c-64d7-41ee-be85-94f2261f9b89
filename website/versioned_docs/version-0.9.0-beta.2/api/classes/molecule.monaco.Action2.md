---
id: 'molecule.monaco.Action2'
title: 'Class: Action2'
sidebar_label: 'Action2'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[monaco](../namespaces/molecule.monaco).Action2

## Constructors

### constructor

• **new Action2**(`desc`)

#### Parameters

| Name   | Type                  |
| :----- | :-------------------- |
| `desc` | `Readonly`<`Object`\> |

#### Defined in

[src/monaco/common.ts:45](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L45)

## Properties

### desc

• `Readonly` **desc**: `Readonly`<`Object`\>

## Methods

### run

▸ `Abstract` **run**(`accessor`, ...`args`): `any`

#### Parameters

| Name       | Type    |
| :--------- | :------ |
| `accessor` | `any`   |
| `...args`  | `any`[] |

#### Returns

`any`

#### Defined in

[src/monaco/common.ts:54](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L54)
