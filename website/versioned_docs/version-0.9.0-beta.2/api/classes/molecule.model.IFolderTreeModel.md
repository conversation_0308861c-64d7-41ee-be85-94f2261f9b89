---
id: 'molecule.model.IFolderTreeModel'
title: 'Class: IFolderTreeModel'
sidebar_label: 'IFolderTreeModel'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IFolderTreeModel

## Implements

-   [`IFolderTree`](../interfaces/molecule.model.IFolderTree)

## Constructors

### constructor

• **new IFolderTreeModel**(`folderTree?`, `entry?`)

#### Parameters

| Name         | Type                                                                    |
| :----------- | :---------------------------------------------------------------------- |
| `folderTree` | [`IFolderTreeSubItem`](../interfaces/molecule.model.IFolderTreeSubItem) |
| `entry?`     | `ReactNode`                                                             |

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:93](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L93)

## Properties

### entry

• **entry**: `ReactNode`

#### Implementation of

[IFolderTree](../interfaces/molecule.model.IFolderTree).[entry](../interfaces/molecule.model.IFolderTree#entry)

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:91](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L91)

---

### folderTree

• **folderTree**: [`IFolderTreeSubItem`](../interfaces/molecule.model.IFolderTreeSubItem)

#### Implementation of

[IFolderTree](../interfaces/molecule.model.IFolderTree).[folderTree](../interfaces/molecule.model.IFolderTree#foldertree)

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:90](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L90)
