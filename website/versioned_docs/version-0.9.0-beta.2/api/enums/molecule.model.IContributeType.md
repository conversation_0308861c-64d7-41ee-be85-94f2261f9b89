---
id: 'molecule.model.IContributeType'
title: 'Enumeration: IContributeType'
sidebar_label: 'IContributeType'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IContributeType

## Enumeration members

### Commands

• **Commands** = `"commands"`

#### Defined in

[src/model/extension.ts:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L21)

---

### Configuration

• **Configuration** = `"configuration"`

#### Defined in

[src/model/extension.ts:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L22)

---

### Grammar

• **Grammar** = `"grammars"`

#### Defined in

[src/model/extension.ts:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L23)

---

### IconTheme

• **IconTheme** = `"iconThemes"`

#### Defined in

[src/model/extension.ts:25](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L25)

---

### Languages

• **Languages** = `"languages"`

#### Defined in

[src/model/extension.ts:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L20)

---

### Themes

• **Themes** = `"themes"`

#### Defined in

[src/model/extension.ts:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L24)
