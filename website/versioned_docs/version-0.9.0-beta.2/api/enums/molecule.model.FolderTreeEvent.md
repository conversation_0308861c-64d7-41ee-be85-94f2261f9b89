---
id: 'molecule.model.FolderTreeEvent'
title: 'Enumeration: FolderTreeEvent'
sidebar_label: 'FolderTreeEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).FolderTreeEvent

## Enumeration members

### onContextMenuClick

• **onContextMenuClick** = `"folderTree.onContextMenuClick"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:21](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L21)

---

### onCreate

• **onCreate** = `"folderTree.onCreate"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:22](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L22)

---

### onDelete

• **onDelete** = `"folderTree.onDelete"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L17)

---

### onDrop

• **onDrop** = `"folderTree.onDrop"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:24](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L24)

---

### onLoadData

• **onLoadData** = `"folderTree.onLoadData"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:23](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L23)

---

### onRename

• **onRename** = `"folderTree.onRename"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L18)

---

### onRightClick

• **onRightClick** = `"folderTree.onRightClick"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L20)

---

### onSelectFile

• **onSelectFile** = `"folderTree.onSelectFile"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L16)

---

### onUpdateFileName

• **onUpdateFileName** = `"folderTree.onUpdateFileName"`

#### Defined in

[src/model/workbench/explorer/folderTree.tsx:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/folderTree.tsx#L19)
