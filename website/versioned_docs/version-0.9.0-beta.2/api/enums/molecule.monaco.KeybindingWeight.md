---
id: 'molecule.monaco.KeybindingWeight'
title: 'Enumeration: KeybindingWeight'
sidebar_label: 'KeybindingWeight'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[monaco](../namespaces/molecule.monaco).KeybindingWeight

## Enumeration members

### BuiltinExtension

• **BuiltinExtension** = `300`

#### Defined in

[src/monaco/common.ts:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L19)

---

### EditorContrib

• **EditorContrib** = `100`

#### Defined in

[src/monaco/common.ts:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L17)

---

### EditorCore

• **EditorCore** = `0`

#### Defined in

[src/monaco/common.ts:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L16)

---

### ExternalExtension

• **ExternalExtension** = `400`

#### Defined in

[src/monaco/common.ts:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L20)

---

### WorkbenchContrib

• **WorkbenchContrib** = `200`

#### Defined in

[src/monaco/common.ts:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/monaco/common.ts#L18)
