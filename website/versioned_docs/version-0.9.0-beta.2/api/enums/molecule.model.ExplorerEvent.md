---
id: 'molecule.model.ExplorerEvent'
title: 'Enumeration: ExplorerEvent'
sidebar_label: 'ExplorerEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ExplorerEvent

## Enumeration members

### onClick

• **onClick** = `"explorer.onClick"`

#### Defined in

[src/model/workbench/explorer/explorer.tsx:6](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L6)

---

### onCollapseChange

• **onCollapseChange** = `"explorer.onCollapseChange"`

#### Defined in

[src/model/workbench/explorer/explorer.tsx:8](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L8)

---

### onPanelToolbarClick

• **onPanelToolbarClick** = `"explorer.onPanelToolbarClick"`

#### Defined in

[src/model/workbench/explorer/explorer.tsx:7](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L7)

---

### onRemovePanel

• **onRemovePanel** = `"explorer.onRemovePanel"`

#### Defined in

[src/model/workbench/explorer/explorer.tsx:9](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/explorer.tsx#L9)
