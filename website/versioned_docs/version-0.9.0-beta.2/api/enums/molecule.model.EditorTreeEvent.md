---
id: 'molecule.model.EditorTreeEvent'
title: 'Enumeration: EditorTreeEvent'
sidebar_label: 'EditorTreeEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).EditorTreeEvent

## Enumeration members

### onClose

• **onClose** = `"editorTree.close"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:2](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L2)

---

### onCloseAll

• **onCloseAll** = `"editorTree.closeAll"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:6](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L6)

---

### onCloseOthers

• **onCloseOthers** = `"editorTree.closeOthers"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:4](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L4)

---

### onCloseSaved

• **onCloseSaved** = `"editorTree.closeSaved"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:5](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L5)

---

### onContextMenu

• **onContextMenu** = `"editorTree.contextMenuClick"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:10](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L10)

---

### onSaveAll

• **onSaveAll** = `"editorTree.saveAll"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:7](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L7)

---

### onSelect

• **onSelect** = `"editorTree.select"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:3](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L3)

---

### onSplitEditorLayout

• **onSplitEditorLayout** = `"editorTree.splitEditorLayout"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:8](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L8)

---

### onToolbarClick

• **onToolbarClick** = `"editorTree.toolbarClick"`

#### Defined in

[src/model/workbench/explorer/editorTree.ts:9](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/explorer/editorTree.ts#L9)
