---
id: 'molecule.model.StatusBarEvent'
title: 'Enumeration: StatusBarEvent'
sidebar_label: 'StatusBarEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).StatusBarEvent

The activity bar event definition

## Enumeration members

### DataChanged

• **DataChanged** = `"statusBar.data"`

Activity bar data changed

#### Defined in

[src/model/workbench/statusBar.tsx:36](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/statusBar.tsx#L36)

---

### onClick

• **onClick** = `"statusBar.onClick"`

Selected an activity bar

#### Defined in

[src/model/workbench/statusBar.tsx:32](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/statusBar.tsx#L32)
