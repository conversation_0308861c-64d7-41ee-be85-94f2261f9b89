---
id: 'molecule.model.PanelEvent'
title: 'Enumeration: PanelEvent'
sidebar_label: 'PanelEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).PanelEvent

## Enumeration members

### onTabChange

• **onTabChange** = `"panel.onTabChange"`

#### Defined in

[src/model/workbench/panel.tsx:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/panel.tsx#L18)

---

### onTabClose

• **onTabClose** = `"panel.onTabClose"`

#### Defined in

[src/model/workbench/panel.tsx:20](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/panel.tsx#L20)

---

### onToolbarClick

• **onToolbarClick** = `"panel.onToolbarClick"`

#### Defined in

[src/model/workbench/panel.tsx:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/panel.tsx#L19)
