---
id: 'molecule.model.ActivityBarEvent'
title: 'Enumeration: ActivityBarEvent'
sidebar_label: 'ActivityBarEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).ActivityBarEvent

The activity bar event definition

## Enumeration members

### DataChanged

• **DataChanged** = `"activityBar.data"`

Activity bar data changed

#### Defined in

[src/model/workbench/activityBar.ts:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L13)

---

### OnChange

• **OnChange** = `"activityBar.onChange"`

#### Defined in

[src/model/workbench/activityBar.ts:9](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L9)

---

### OnClick

• **OnClick** = `"activityBar.onClick"`

#### Defined in

[src/model/workbench/activityBar.ts:8](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L8)

---

### ReRender

• **ReRender** = `"activityBar.reRender"`

#### Defined in

[src/model/workbench/activityBar.ts:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/activityBar.ts#L14)
