---
id: 'molecule.model.EditorEvent'
title: 'Enumeration: EditorEvent'
sidebar_label: 'EditorEvent'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).EditorEvent

## Enumeration members

### OnCloseAll

• **OnCloseAll** = `"editor.closeAll"`

#### Defined in

[src/model/workbench/editor.ts:10](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L10)

---

### OnCloseOther

• **OnCloseOther** = `"editor.closeOther"`

#### Defined in

[src/model/workbench/editor.ts:11](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L11)

---

### OnCloseTab

• **OnCloseTab** = `"editor.closeTab"`

#### Defined in

[src/model/workbench/editor.ts:9](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L9)

---

### OnCloseToLeft

• **OnCloseToLeft** = `"editor.closeToLeft"`

#### Defined in

[src/model/workbench/editor.ts:12](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L12)

---

### OnCloseToRight

• **OnCloseToRight** = `"editor.closeToRight"`

#### Defined in

[src/model/workbench/editor.ts:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L13)

---

### OnMoveTab

• **OnMoveTab** = `"editor.moveTab"`

#### Defined in

[src/model/workbench/editor.ts:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L14)

---

### OnSelectTab

• **OnSelectTab** = `"editor.selectTab"`

#### Defined in

[src/model/workbench/editor.ts:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L16)

---

### OnSplitEditorRight

• **OnSplitEditorRight** = `"editor.splitEditorRight"`

#### Defined in

[src/model/workbench/editor.ts:19](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L19)

---

### OnUpdateTab

• **OnUpdateTab** = `"editor.updateTab"`

#### Defined in

[src/model/workbench/editor.ts:17](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L17)

---

### OpenTab

• **OpenTab** = `"editor.openTab"`

#### Defined in

[src/model/workbench/editor.ts:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L15)

---

### onActionsClick

• **onActionsClick** = `"editor.actionsClick"`

#### Defined in

[src/model/workbench/editor.ts:18](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/workbench/editor.ts#L18)
