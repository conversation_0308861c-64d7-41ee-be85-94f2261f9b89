---
id: 'molecule.model.IExtensionType'
title: 'Enumeration: IExtensionType'
sidebar_label: 'IExtensionType'
custom_edit_url: null
---

[molecule](../namespaces/molecule).[model](../namespaces/molecule.model).IExtensionType

Defines extension types

## Enumeration members

### Locals

• **Locals** = `"locales"`

#### Defined in

[src/model/extension.ts:14](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L14)

---

### Menus

• **Menus** = `"menus"`

#### Defined in

[src/model/extension.ts:15](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L15)

---

### Normal

• **Normal** = `"normal"`

#### Defined in

[src/model/extension.ts:12](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L12)

---

### Settings

• **Settings** = `"settings"`

#### Defined in

[src/model/extension.ts:13](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L13)

---

### Theme

• **Theme** = `"Themes"`

#### Defined in

[src/model/extension.ts:11](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L11)

---

### Workbench

• **Workbench** = `"workbench"`

#### Defined in

[src/model/extension.ts:16](https://github.com/DTStack/molecule/blob/b5324fcf/src/model/extension.ts#L16)
