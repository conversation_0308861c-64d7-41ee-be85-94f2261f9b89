---
id: 'index'
title: 'Molecule'
slug: '/api/'
sidebar_label: 'Exports'
sidebar_position: 0.5
custom_edit_url: null
---

## Namespaces

-   [molecule](namespaces/molecule)

## Classes

-   [MoleculeProvider](classes/MoleculeProvider)

## Interfaces

-   [IMoleculeProps](interfaces/IMoleculeProps)

## References

### default

Renames and re-exports [molecule](namespaces/molecule)

## Variables

### Workbench

• **Workbench**: `ComponentType`<`any`\>

#### Defined in

[src/workbench/workbench.tsx:158](https://github.com/DTStack/molecule/blob/b5324fcf/src/workbench/workbench.tsx#L158)
