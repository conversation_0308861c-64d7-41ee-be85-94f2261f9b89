{"name": "website", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "start:chinese": "docusaurus start --locale zh-CN", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "version:cut": "docusaurus docs:version", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^2.0.0-beta.13", "@docusaurus/preset-classic": "^2.0.0-beta.13", "@mdx-js/react": "^1.6.21", "@svgr/webpack": "^5.5.0", "clsx": "^1.1.1", "file-loader": "^6.2.0", "prism-react-renderer": "^1.2.1", "react": "^17.0.1", "react-dom": "^17.0.1", "typescript": "4.4", "url-loader": "^4.1.1"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"docusaurus-plugin-typedoc": "^0.16.3", "typedoc": "^0.22.4", "typedoc-plugin-markdown": "^3.11.1"}}