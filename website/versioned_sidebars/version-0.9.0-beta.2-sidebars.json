{"docs": ["version-0.9.0-beta.2/introduction", "version-0.9.0-beta.2/overview", "version-0.9.0-beta.2/quick-start", "version-0.9.0-beta.2/the-first-extension", {"type": "category", "label": "Guides", "collapsed": false, "items": ["version-0.9.0-beta.2/guides/extension", {"type": "category", "collapsed": false, "label": "UI", "items": ["version-0.9.0-beta.2/guides/extend-workbench", "version-0.9.0-beta.2/guides/extend-builtin-ui"]}, "version-0.9.0-beta.2/guides/extend-color-theme", "version-0.9.0-beta.2/guides/extend-keybinding", "version-0.9.0-beta.2/guides/extend-quick-access", "version-0.9.0-beta.2/guides/extend-locales", "version-0.9.0-beta.2/guides/extend-settings", "version-0.9.0-beta.2/guides/icons"]}, {"type": "category", "collapsed": false, "label": "Advanced Guides", "items": ["version-0.9.0-beta.2/advanced/customize-workbench"]}, "version-0.9.0-beta.2/contributing"], "api": ["version-0.9.0-beta.2/api/index", "version-0.9.0-beta.2/api/namespaces/molecule", {"type": "category", "label": "Namespaces", "collapsed": false, "items": ["version-0.9.0-beta.2/api/namespaces/molecule.component", "version-0.9.0-beta.2/api/namespaces/molecule.model", "version-0.9.0-beta.2/api/namespaces/molecule.react", "version-0.9.0-beta.2/api/namespaces/molecule.event", "version-0.9.0-beta.2/api/namespaces/molecule.monaco"]}, {"type": "category", "label": "Interfaces", "items": [{"type": "autogenerated", "dirName": "api/interfaces"}]}, {"type": "category", "label": "Classes", "items": [{"type": "autogenerated", "dirName": "api/classes"}]}, {"type": "category", "label": "Enums", "items": [{"type": "autogenerated", "dirName": "api/enums"}]}]}