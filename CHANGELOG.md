# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.3.4](https://github.com/DTStack/molecule/compare/v1.3.3...v1.3.4) (2023-06-12)

### [1.3.3](https://github.com/DTStack/molecule/compare/v1.3.2...v1.3.3) (2023-04-03)

### Bug Fixes

-   replace the Codicon.gear.classNames with codicon string ([#847](https://github.com/DTStack/molecule/issues/847)) ([ded6b52](https://github.com/DTStack/molecule/commit/ded6b52be2124520333f708fef928b8eeb8d9dbe))

### [1.3.2](https://github.com/DTStack/molecule/compare/v1.2.0...v1.3.2) (2023-04-03)

### Features

-   develop auxiliary bar ([#833](https://github.com/DTStack/molecule/issues/833)) ([cfd7c7c](https://github.com/DTStack/molecule/commit/cfd7c7cebbacbbc2893e69b0934b4724eac5b24c))
-   develop scrollBar to replace scrollable component ([#826](https://github.com/DTStack/molecule/issues/826)) ([a276038](https://github.com/DTStack/molecule/commit/a27603864143596d4169e670ffe4b387d795de1a))
-   support to set the direction of editor group ([#822](https://github.com/DTStack/molecule/issues/822)) ([f0ca348](https://github.com/DTStack/molecule/commit/f0ca348ec82b43fc2dfb3556dcd0ea087b251f94))

### Bug Fixes

-   adjust the order of controller ([#831](https://github.com/DTStack/molecule/issues/831)) ([8a63067](https://github.com/DTStack/molecule/commit/8a630677d9a509951109fead67a11f8cb91d90ed))
-   can't prevent close tab with confirm ([#828](https://github.com/DTStack/molecule/issues/828)) ([9418d40](https://github.com/DTStack/molecule/commit/9418d40d6767ec05d83f150bb5ede371e9dadd75))
-   fix lost open animation of collapse ([#827](https://github.com/DTStack/molecule/issues/827)) ([d1cdbe1](https://github.com/DTStack/molecule/commit/d1cdbe110ea1f82615945789d23585c39adde11a))
-   get container after Workbench is mounted ([#848](https://github.com/DTStack/molecule/issues/848)) ([222e536](https://github.com/DTStack/molecule/commit/222e53688cfada84ffe38ef9ecebd3a21cde8655))
-   improve the style of edit's input ([#838](https://github.com/DTStack/molecule/issues/838)) ([534b050](https://github.com/DTStack/molecule/commit/534b050d756680b320262eaa1473f05ff7e87082))
-   remove contextView warning in console ([#832](https://github.com/DTStack/molecule/issues/832)) ([2cf87db](https://github.com/DTStack/molecule/commit/2cf87db11b6ec9d310d6fae59f4500f91cb69bad))
-   update the previewUrl of online-code-formatting ([#844](https://github.com/DTStack/molecule/issues/844)) ([bc31969](https://github.com/DTStack/molecule/commit/bc31969e6100564df349ebf61743583ddb399d68))

## [1.3.0](https://github.com/DTStack/molecule/compare/v1.2.0...v1.3.0) (2022-12-16)

### Features

-   develop auxiliary bar ([#833](https://github.com/DTStack/molecule/issues/833)) ([cfd7c7c](https://github.com/DTStack/molecule/commit/cfd7c7cebbacbbc2893e69b0934b4724eac5b24c))
-   develop scrollBar to replace scrollable component ([#826](https://github.com/DTStack/molecule/issues/826)) ([a276038](https://github.com/DTStack/molecule/commit/a27603864143596d4169e670ffe4b387d795de1a))
-   support to set the direction of editor group ([#822](https://github.com/DTStack/molecule/issues/822)) ([f0ca348](https://github.com/DTStack/molecule/commit/f0ca348ec82b43fc2dfb3556dcd0ea087b251f94))

### Bug Fixes

-   adjust the order of controller ([#831](https://github.com/DTStack/molecule/issues/831)) ([8a63067](https://github.com/DTStack/molecule/commit/8a630677d9a509951109fead67a11f8cb91d90ed))
-   can't prevent close tab with confirm ([#828](https://github.com/DTStack/molecule/issues/828)) ([9418d40](https://github.com/DTStack/molecule/commit/9418d40d6767ec05d83f150bb5ede371e9dadd75))
-   fix lost open animation of collapse ([#827](https://github.com/DTStack/molecule/issues/827)) ([d1cdbe1](https://github.com/DTStack/molecule/commit/d1cdbe110ea1f82615945789d23585c39adde11a))
-   remove contextView warning in console ([#832](https://github.com/DTStack/molecule/issues/832)) ([2cf87db](https://github.com/DTStack/molecule/commit/2cf87db11b6ec9d310d6fae59f4500f91cb69bad))

## [1.2.0](https://github.com/DTStack/molecule/compare/v1.1.1...v1.2.0) (2022-10-21)

### Features

-   define loadedKey to be controlled ([#803](https://github.com/DTStack/molecule/issues/803)) ([219b937](https://github.com/DTStack/molecule/commit/219b937215994a17beff99f745c75aec15be35d5))
-   support stop event emit ([#807](https://github.com/DTStack/molecule/issues/807)) ([f8451eb](https://github.com/DTStack/molecule/commit/f8451ebfd40bdc8ad1e235dee19d6a991d442b77))

### Bug Fixes

-   delcare the useEffect before return ([#808](https://github.com/DTStack/molecule/issues/808)) ([3b73285](https://github.com/DTStack/molecule/commit/3b73285bdcd37f4b425b007df0f554127c17d5cb))
-   fix switch panels between problem and output ([32641b9](https://github.com/DTStack/molecule/commit/32641b9730586e408740d7ad5c150493fad17476))
-   improve breadcrumb ([#815](https://github.com/DTStack/molecule/issues/815)) ([60a66ab](https://github.com/DTStack/molecule/commit/60a66abed67148127f0faf1189661d15b77e83ed))

### [1.1.1](https://github.com/DTStack/molecule/compare/v1.1.0...v1.1.1) (2022-08-31)

### Bug Fixes

-   prevent call loadData when editing ([#789](https://github.com/DTStack/molecule/issues/789)) ([80d99bc](https://github.com/DTStack/molecule/commit/80d99bcfed349a27345ac602a0f373f3c004f856))
-   remove findDOMNode usage ([#790](https://github.com/DTStack/molecule/issues/790)) ([289288b](https://github.com/DTStack/molecule/commit/289288b83fccec277215152ca912c74ea170d542))
-   remove useless generic type ([bee51e2](https://github.com/DTStack/molecule/commit/bee51e2b46a942442195999c2b4eea58567003de))

## [1.1.0](https://github.com/DTStack/molecule/compare/v1.0.2...v1.1.0) (2022-07-15)

### Features

-   bump react'version to 18.x ([#774](https://github.com/DTStack/molecule/issues/774)) ([819696e](https://github.com/DTStack/molecule/commit/819696e20001112e8dbfdbd2715702ffa88c4337))
-   the text of the optimized tree item is too long ([#773](https://github.com/DTStack/molecule/issues/773)) ([32c877a](https://github.com/DTStack/molecule/commit/32c877a635bf65eb250451508d613464be3150c4))

### Bug Fixes

-   fix set entry failed at some situations ([#778](https://github.com/DTStack/molecule/issues/778)) ([a8b608d](https://github.com/DTStack/molecule/commit/a8b608d7ad85f4bfd033909adc97e016ae71e8ed))
-   prevent updateTab with renderPanel tab ([#781](https://github.com/DTStack/molecule/issues/781)) ([2d77923](https://github.com/DTStack/molecule/commit/2d779231d64fbef9fff7eed35517aea567b8c043))
-   remove invalid Regx in Safari ([#777](https://github.com/DTStack/molecule/issues/777)) ([66da95e](https://github.com/DTStack/molecule/commit/66da95e9a181b9182eb74ba418127c15ee5f263c))
-   updateTab will change the reference of current tab ([#779](https://github.com/DTStack/molecule/issues/779)) ([3114bd5](https://github.com/DTStack/molecule/commit/3114bd50aaeddff8d08c91f585009365c86585e4))

### [1.0.2](https://github.com/DTStack/molecule/compare/v1.0.1...v1.0.2) (2022-06-22)

### [1.0.1](https://github.com/DTStack/molecule/compare/v1.0.0...v1.0.1) (2022-06-22)

### Bug Fixes

-   prevent multiply calls of render function ([#768](https://github.com/DTStack/molecule/issues/768)) ([b34a8f2](https://github.com/DTStack/molecule/commit/b34a8f235b2fe8defad406077b7824e299a8fed7))

## [1.0.0](https://github.com/DTStack/molecule/compare/v0.9.0-beta.4.2...v1.0.0) (2022-06-17)

### Features

-   add getParentNode interface for folderTreeService ([#752](https://github.com/DTStack/molecule/issues/752)) ([4c09eea](https://github.com/DTStack/molecule/commit/4c09eea2a90a3739190915232793be9afd53f639))
-   create instanceService ([#733](https://github.com/DTStack/molecule/issues/733)) ([3093a21](https://github.com/DTStack/molecule/commit/3093a21078c33144532df54532e015dbfc572695))
-   the tree in Problems panel supports onSelect event ([#712](https://github.com/DTStack/molecule/issues/712)) ([4c809eb](https://github.com/DTStack/molecule/commit/4c809eb4ad49dbaac62f79c7b0cf428d9c5a87df))

### Bug Fixes

-   add hover style to tab within group ([#716](https://github.com/DTStack/molecule/issues/716)) ([#751](https://github.com/DTStack/molecule/issues/751)) ([0f97d83](https://github.com/DTStack/molecule/commit/0f97d8346b85b63459f1d0763be9e039623027c4))
-   change sort default value 0 to be compatible with Firefox ([#722](https://github.com/DTStack/molecule/issues/722)) ([#748](https://github.com/DTStack/molecule/issues/748)) ([22f310f](https://github.com/DTStack/molecule/commit/22f310fe92965f882c4c58b6a629d57ac4861817))
-   compatible background color for buttons and menu items ([#711](https://github.com/DTStack/molecule/issues/711)) ([a5134b0](https://github.com/DTStack/molecule/commit/a5134b08522b2be675d6cf2eb4d7782d798c5047))
-   **components:** add color variables for breadcrumb ([#740](https://github.com/DTStack/molecule/issues/740)) ([a87accd](https://github.com/DTStack/molecule/commit/a87accdbadf6394083628742e90a16cbe5789097))
-   disable click event when action is disabled ([#745](https://github.com/DTStack/molecule/issues/745)) ([ad0310d](https://github.com/DTStack/molecule/commit/ad0310d1c0856570651f39d42939fa91edb27ae4)), closes [#725](https://github.com/DTStack/molecule/issues/725)
-   **editor:** fix styles in editor tab extra ([#736](https://github.com/DTStack/molecule/issues/736)) ([a5bf2af](https://github.com/DTStack/molecule/commit/a5bf2af38f4fa2ac173419707bb2afae0914732d))
-   enable the editor to save viewState ([#696](https://github.com/DTStack/molecule/issues/696)) ([1d4f6da](https://github.com/DTStack/molecule/commit/1d4f6daddab56f433c74439af82347262e4a3148))
-   make editor not re-initialize when switching tabs ([#698](https://github.com/DTStack/molecule/issues/698)) ([814c5b2](https://github.com/DTStack/molecule/commit/814c5b2425c89dd72c083dd658b9ae0325afc4b1))
-   **service:** improve the styles in colorTheme Service ([#739](https://github.com/DTStack/molecule/issues/739)) ([5d93078](https://github.com/DTStack/molecule/commit/5d930784a85d493180f0027d148926428c7f1229))
-   throws the full Error message when the Extension load exception ([#735](https://github.com/DTStack/molecule/issues/735)) ([61b1753](https://github.com/DTStack/molecule/commit/61b1753fe8b6a1d3030dfa46d9ab4a1c3a3f7804))
-   update tab set value ([#750](https://github.com/DTStack/molecule/issues/750)) ([f97e00c](https://github.com/DTStack/molecule/commit/f97e00c6d68ad9780e6b82dff488ecad043ac2fd)), closes [#714](https://github.com/DTStack/molecule/issues/714)
-   use activeKey to control highlighting ([#724](https://github.com/DTStack/molecule/issues/724)) ([#742](https://github.com/DTStack/molecule/issues/742)) ([52d79b0](https://github.com/DTStack/molecule/commit/52d79b092502d71f30b4bad9f83b9aa45d1e8f5e))
-   use the enter character \u21a9 ([#746](https://github.com/DTStack/molecule/issues/746)) ([7fd02ab](https://github.com/DTStack/molecule/commit/7fd02ab5ba78fd319ecdc12f8916017099c9fbc1))
-   yarn web failed in windows ([#719](https://github.com/DTStack/molecule/issues/719)) ([05ad264](https://github.com/DTStack/molecule/commit/05ad264912b169d86ff503c243940821bb5d7273))

## [0.9.0-beta.4.2](https://github.com/DTStack/molecule/compare/v0.9.0-beta.3.2...v0.9.0-beta.4.2) (2022-03-04)

### Features

-   develop a Split Pane component ([#509](https://github.com/DTStack/molecule/issues/509)) ([3317aee](https://github.com/DTStack/molecule/commit/3317aeecefa4caa7612fbfccb5ee6ca68b423f89)), closes [#510](https://github.com/DTStack/molecule/issues/510)
-   Improve usage examples in TestPane ([#669](https://github.com/DTStack/molecule/issues/669)) ([0d42c0f](https://github.com/DTStack/molecule/commit/0d42c0ffd53dfb21845650d6f784f0ad84d3acc8))
-   support listen to onCollapseAllFolders ([#673](https://github.com/DTStack/molecule/issues/673)) ([c230885](https://github.com/DTStack/molecule/commit/c23088564c1a3a3d00e2da9de2f6bb31c0fc2971))
-   support to set the expandKeys ([#664](https://github.com/DTStack/molecule/issues/664)) ([e9a8cdd](https://github.com/DTStack/molecule/commit/e9a8cdd670664fab67872f115be2ffd42b1a8d63))

### Bug Fixes

-   filename missing and file path in EditorTree is not updated ([#659](https://github.com/DTStack/molecule/issues/659)) ([aafa07d](https://github.com/DTStack/molecule/commit/aafa07d461b706f4f325363a5e86ff43a446428a))
-   fix usage of useMemo ([#694](https://github.com/DTStack/molecule/issues/694)) ([24e6e2e](https://github.com/DTStack/molecule/commit/24e6e2ed4e74c042433d17743bddc3c266431211))
-   import Icon, Toolbar from relative path ([#692](https://github.com/DTStack/molecule/issues/692)) ([a95fbe2](https://github.com/DTStack/molecule/commit/a95fbe26ca0b0e77a895430af2dfc85eb17521d4))
-   improve scrollbar style on windows platform ([#665](https://github.com/DTStack/molecule/issues/665)) ([0be561d](https://github.com/DTStack/molecule/commit/0be561dfa8541c4a435d8d1e703ff91c05684f19))
-   make Keybinding work after reloading Molecule ([#686](https://github.com/DTStack/molecule/issues/686)) ([a88a71e](https://github.com/DTStack/molecule/commit/a88a71e1c05e6f35384398bb8daca27f1046489c))
-   move the style of statusBarView into mo.scss ([#689](https://github.com/DTStack/molecule/issues/689)) ([08d799b](https://github.com/DTStack/molecule/commit/08d799b9dc729c4956a0c6367853d5a808aa945e))
-   prevent the contextmenu event of Notification ([#670](https://github.com/DTStack/molecule/issues/670)) ([1aa9f7f](https://github.com/DTStack/molecule/commit/1aa9f7fa8ab24ad4c27a14819696ff37d021cbe2))

## [0.9.0-beta.3.2](https://github.com/DTStack/molecule/compare/v0.9.0-beta.3.1...v0.9.0-beta.3.2) (2022-02-18)

### Features

-   LocaleNotification supports to trigger reload by pressing the Enter key ([#643](https://github.com/DTStack/molecule/issues/643)) ([4b7d8de](https://github.com/DTStack/molecule/commit/4b7d8de47dc49fed1a40c21f6911916a7f89d249))
-   support listen to the theme changed event ([#646](https://github.com/DTStack/molecule/issues/646)) ([2590217](https://github.com/DTStack/molecule/commit/2590217ecd8c039afb6282713c312e750e1f1085))

### Bug Fixes

-   add icons to EditorTree and optimize its style ([#656](https://github.com/DTStack/molecule/issues/656)) ([859fd73](https://github.com/DTStack/molecule/commit/859fd7344d137f1b13a50464f35e33e954669287))
-   customized MenuBar data will be lost when modifying the MenuBar's mode ([#650](https://github.com/DTStack/molecule/issues/650)) ([15e219b](https://github.com/DTStack/molecule/commit/15e219b4e718e530ccbf0d7705e8153b8cc03d9c))
-   fix panels didn't active the next one after closing automatically ([#653](https://github.com/DTStack/molecule/issues/653)) ([784dc07](https://github.com/DTStack/molecule/commit/784dc075562e2414722c2d6385d09a7fe0d19609))
-   improve the style of the Button in Notification ([#654](https://github.com/DTStack/molecule/issues/654)) ([4a3470f](https://github.com/DTStack/molecule/commit/4a3470fa023c67e642869f315f4d3f50713348cf))
-   improve the style of the EditorTree node's icon ([#657](https://github.com/DTStack/molecule/issues/657)) ([ffe6955](https://github.com/DTStack/molecule/commit/ffe69557cdf8924f19b70fd6ec0c82b22977ccce))
-   the interaction bug of MenuBar in horizontal mode ([#647](https://github.com/DTStack/molecule/issues/647)) ([2294b3d](https://github.com/DTStack/molecule/commit/2294b3d2f147c2c5dea8c130ffa98162d4919f94))
-   the onClick property of MenuItem will be overridden by custom data ([#651](https://github.com/DTStack/molecule/issues/651)) ([467a7c0](https://github.com/DTStack/molecule/commit/467a7c0b0597eb48677be80787885eb37dbdbc9b))
-   update explorer when closing editor tab ([#652](https://github.com/DTStack/molecule/issues/652)) ([953b2c0](https://github.com/DTStack/molecule/commit/953b2c069e325b0070d3f797c8d8b37a36c9a9e1))

## [0.9.0-beta.3.1](https://github.com/DTStack/molecule/compare/v0.9.0-beta.3...v0.9.0-beta.3.1) (2022-02-08)

## [0.9.0-beta.3](https://github.com/DTStack/molecule/compare/v0.9.0-beta.1.1...v0.9.0-beta.3) (2022-01-27)

### Features

-   add a Display component that controlling the Workbench Parts ([#564](https://github.com/DTStack/molecule/issues/564)) ([948f09c](https://github.com/DTStack/molecule/commit/948f09c05ec508c8e29f2c79d81fac4a598cfcbb))
-   add Korean language pack ([#558](https://github.com/DTStack/molecule/issues/558)) ([b9f817d](https://github.com/DTStack/molecule/commit/b9f817d1761ff6329634909226eb4fb8598ce735))
-   change the interaction of MenuBar in horizontal mode ([#636](https://github.com/DTStack/molecule/issues/636)) ([b352afd](https://github.com/DTStack/molecule/commit/b352afda1043a1c16f34cfb58e8d6b879ea0f783))
-   provide types of Keybinding and QuickAccess ([#567](https://github.com/DTStack/molecule/issues/567)) ([0118b43](https://github.com/DTStack/molecule/commit/0118b4335b8557f2fbd4f256fcb23c08b617e16d))
-   remove the onRemove processing logic in the built-in FolderTree extension ([#617](https://github.com/DTStack/molecule/issues/617)) ([7a77374](https://github.com/DTStack/molecule/commit/7a7737480a98c87e2c97a148e7f1730e28d075dd))
-   support the horizontal layout of MenuBar ([#553](https://github.com/DTStack/molecule/issues/553)) ([2cf2abb](https://github.com/DTStack/molecule/commit/2cf2abba809cfa63d778a20e4b3d6c65f8f070f3))
-   support to dispose the Action ([#599](https://github.com/DTStack/molecule/issues/599)) ([bfdf0bb](https://github.com/DTStack/molecule/commit/bfdf0bb34511254de3bb4b15e3b3796ef68fa023))
-   support to get the mode of the current Color Theme ([#641](https://github.com/DTStack/molecule/issues/641)) ([57f8fab](https://github.com/DTStack/molecule/commit/57f8fab95182e4074fc66de2b0fef5a195dc972e))
-   support to set the folderTree nodes whether to sort by default ([#614](https://github.com/DTStack/molecule/issues/614)) ([99754f9](https://github.com/DTStack/molecule/commit/99754f96db39403ab6edafd11f7730e6f059f9c4))

### Bug Fixes

-   Connect component supports to unsubscribe event by pass the callback ([#635](https://github.com/DTStack/molecule/issues/635)) ([bde0274](https://github.com/DTStack/molecule/commit/bde0274857bc3b56b62931824af2537e228dd472))
-   correct the style of input and select ([#578](https://github.com/DTStack/molecule/issues/578)) ([e0ba0fc](https://github.com/DTStack/molecule/commit/e0ba0fc91cfe968c13357667b5eb0b375484eafa))
-   increase the z-index value of SubMenu in horizontal mode ([#611](https://github.com/DTStack/molecule/issues/611)) ([b11b4e9](https://github.com/DTStack/molecule/commit/b11b4e90970d79a2000c2eb7fba8860348145262))
-   let defaultLocale works ([#639](https://github.com/DTStack/molecule/issues/639)) ([2faaa9f](https://github.com/DTStack/molecule/commit/2faaa9f9977ea2ce6e4a3573cc788ca945fbc99c))
-   not allowed to reload the initialize method ([#580](https://github.com/DTStack/molecule/issues/580)) ([7229fc5](https://github.com/DTStack/molecule/commit/7229fc5e4c01e2777934641d30676aacc31b6454))
-   optimize the style of MenuBar ([#629](https://github.com/DTStack/molecule/issues/629)) ([f355c88](https://github.com/DTStack/molecule/commit/f355c88d6151ccddab773317776adf8c834790de))
-   re-layout the Workbench when StatusBar is hidden ([#557](https://github.com/DTStack/molecule/issues/557)) ([2d89f56](https://github.com/DTStack/molecule/commit/2d89f568289c62b4c36ee506ac3b62e7f1fec20b))
-   sync menuBar mode status ([#637](https://github.com/DTStack/molecule/issues/637)) ([ecdb312](https://github.com/DTStack/molecule/commit/ecdb3128f3308751f52db134ebf8019ff1e0e366))
-   the style of the context menu of ActivityBarItem ([#613](https://github.com/DTStack/molecule/issues/613)) ([08fd30f](https://github.com/DTStack/molecule/commit/08fd30f8478028f5e05c3e855cbacd1f4844f827))

## [0.9.0-beta.2](https://github.com/DTStack/molecule/compare/v0.9.0-beta.1.1...v0.9.0-beta.2) (2022-01-05)

### Features

-   add a Display component that controlling the Workbench Parts ([#564](https://github.com/DTStack/molecule/issues/564)) ([948f09c](https://github.com/DTStack/molecule/commit/948f09c05ec508c8e29f2c79d81fac4a598cfcbb))
-   add Korean language pack ([#558](https://github.com/DTStack/molecule/issues/558)) ([b9f817d](https://github.com/DTStack/molecule/commit/b9f817d1761ff6329634909226eb4fb8598ce735))
-   provide types of Keybinding and QuickAccess ([#567](https://github.com/DTStack/molecule/issues/567)) ([0118b43](https://github.com/DTStack/molecule/commit/0118b4335b8557f2fbd4f256fcb23c08b617e16d))
-   support the horizontal layout of MenuBar ([#553](https://github.com/DTStack/molecule/issues/553)) ([2cf2abb](https://github.com/DTStack/molecule/commit/2cf2abba809cfa63d778a20e4b3d6c65f8f070f3))

### Bug Fixes

-   correct the style of input and select ([#578](https://github.com/DTStack/molecule/issues/578)) ([e0ba0fc](https://github.com/DTStack/molecule/commit/e0ba0fc91cfe968c13357667b5eb0b375484eafa))
-   not allowed to reload the initialize method ([#580](https://github.com/DTStack/molecule/issues/580)) ([7229fc5](https://github.com/DTStack/molecule/commit/7229fc5e4c01e2777934641d30676aacc31b6454))
-   re-layout the Workbench when StatusBar is hidden ([#557](https://github.com/DTStack/molecule/issues/557)) ([2d89f56](https://github.com/DTStack/molecule/commit/2d89f568289c62b4c36ee506ac3b62e7f1fec20b))

## [0.9.0-beta.1.1](https://github.com/DTStack/molecule/compare/v0.9.0-beta.1...v0.9.0-beta.1.1) (2021-12-13)

### Features

-   improve the colorTheme detail ([#541](https://github.com/DTStack/molecule/issues/541)) ([049f003](https://github.com/DTStack/molecule/commit/049f00311f4596356695cb2c6b9c62656a52775c))

## [0.9.0-beta.1](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.6...v0.9.0-beta.1) (2021-12-06)

### Features

-   support a new Welcome page ([#508](https://github.com/DTStack/molecule/issues/508)) ([e5e0a0c](https://github.com/DTStack/molecule/commit/e5e0a0c42ffc4b717b87f8e1d7ffa6259c557788))
-   support sort in folderTree ([#524](https://github.com/DTStack/molecule/issues/524)) ([8802b1f](https://github.com/DTStack/molecule/commit/8802b1f70ec85a597038f003ebf60feafb2ef127))

### Bug Fixes

-   adjust the menu icon size ([ed9cb8d](https://github.com/DTStack/molecule/commit/ed9cb8d7f50c0d75893593edaf83e7d7360812d5))
-   fix can't clear the notifications ([#518](https://github.com/DTStack/molecule/issues/518)) ([2503f4a](https://github.com/DTStack/molecule/commit/2503f4a5699700b56247a94a51f38d64dfbf4ba0))
-   fix create file node incorrect on contextMenu ([#522](https://github.com/DTStack/molecule/issues/522)) ([14c6057](https://github.com/DTStack/molecule/commit/14c6057b34e27cb69997f932867f4bebbf27a6eb))
-   improve the circular dep error when execute yarn link ([#528](https://github.com/DTStack/molecule/issues/528)) ([486182b](https://github.com/DTStack/molecule/commit/486182b1ef62f72f954edfc08f3edb84ababa62c))
-   improve the exports of models ([#507](https://github.com/DTStack/molecule/issues/507)) ([4b7ab03](https://github.com/DTStack/molecule/commit/4b7ab0331a0374567c4ad404413e49c40a0387aa))
-   improve the insert strategy in folderTree ([#486](https://github.com/DTStack/molecule/issues/486)) ([3061b68](https://github.com/DTStack/molecule/commit/3061b68ecea6887aacdf1db19ae56261a144a76d))
-   initView will override the setDefaultValue ([#513](https://github.com/DTStack/molecule/issues/513)) ([586335e](https://github.com/DTStack/molecule/commit/********************c79861bf01c0346427c2))
-   remove the warning in console ([#529](https://github.com/DTStack/molecule/issues/529)) ([059016c](https://github.com/DTStack/molecule/commit/059016cb4ca19854c200ac0791a198eb521a2d3f))
-   show the SubMenu in right place when the Menu is horizontal mode ([#526](https://github.com/DTStack/molecule/issues/526)) ([0d76520](https://github.com/DTStack/molecule/commit/0d76520eb03076314ff2f5fda7f51ddfcf0cbdb9))

## [0.9.0-alpha.6](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.5...v0.9.0-alpha.6) (2021-10-27)

### Features

-   add built-in service ([#471](https://github.com/DTStack/molecule/issues/471)) ([d96e2b3](https://github.com/DTStack/molecule/commit/d96e2b3785c36f6c59ac530be68c3ec8dafbb271))
-   provider supports custom locale ([#473](https://github.com/DTStack/molecule/issues/473)) ([f1d9012](https://github.com/DTStack/molecule/commit/f1d9012088a392bcbf77d378ce21e119c1ea04e9)), closes [#478](https://github.com/DTStack/molecule/issues/478)
-   remove rc-tree and develop the new Tree component ([#418](https://github.com/DTStack/molecule/issues/418)) ([8690180](https://github.com/DTStack/molecule/commit/8690180a9cd8cccd524672b5a0bf77c38dcac6fd))
-   set sidebar default width to 300px ([d15a621](https://github.com/DTStack/molecule/commit/d15a621c694cca53ffd3f6964c8c8aac34a453f5))
-   support render icon in tab title ([#447](https://github.com/DTStack/molecule/issues/447)) ([d67c2e0](https://github.com/DTStack/molecule/commit/d67c2e08413048f055fd01926e04a68fe487c9e6))
-   support to get groupId via tabId ([#431](https://github.com/DTStack/molecule/issues/431)) ([44c2a7b](https://github.com/DTStack/molecule/commit/44c2a7b1cd6a0d7a9b7755c988bb56fe28c2945b))
-   support to subscribe the drop event in tree ([#450](https://github.com/DTStack/molecule/issues/450)) ([8626600](https://github.com/DTStack/molecule/commit/86266002ab032d06d324c6f34ce473d1ea5e45c2))
-   **editor:** add the onOpenTab event ([#416](https://github.com/DTStack/molecule/issues/416)) ([cdbff6f](https://github.com/DTStack/molecule/commit/cdbff6f2fccd8e24e700f308930b4770c23828b7))

### Bug Fixes

-   add built-in titles for the activityBar items ([#424](https://github.com/DTStack/molecule/issues/424)) ([0a5c31d](https://github.com/DTStack/molecule/commit/0a5c31d39d9af5bc9535b248c8dcc66ebda993ce))
-   fix editor size render incorrect when toggle the sidebar ([9655a45](https://github.com/DTStack/molecule/commit/9655a454658a1512411892b8e17b5d0265b1b62f))
-   fix select all action always select the editor content ([#415](https://github.com/DTStack/molecule/issues/415)) ([a1feb18](https://github.com/DTStack/molecule/commit/a1feb1802bda9a4520175af5d315336ca476bd50))
-   fix the searchById method ([#469](https://github.com/DTStack/molecule/issues/469)) ([03e32fb](https://github.com/DTStack/molecule/commit/03e32fb24897ac81aab3ef249e2041981e8ed1ce))
-   fix undo and redo actions ([#440](https://github.com/DTStack/molecule/issues/440)) ([b76f372](https://github.com/DTStack/molecule/commit/b76f3720726616d50a79a4f5f36b7a175eb11ada))
-   improve the forceUpdate after editor open and close ([#441](https://github.com/DTStack/molecule/issues/441)) ([50e0de2](https://github.com/DTStack/molecule/commit/50e0de2c8bcbb1e4ab434b0ec3603520b1b3f4f5))
-   optimize the shadow effect of Scrollable ([#443](https://github.com/DTStack/molecule/issues/443)) ([76ad9b3](https://github.com/DTStack/molecule/commit/76ad9b37500478e15e9547579ed11783176fd65f))
-   perfect the Tree component style ([#428](https://github.com/DTStack/molecule/issues/428)) ([8ad91e0](https://github.com/DTStack/molecule/commit/8ad91e0d7c0e21894895bbdd06d10701e7563ad4))
-   prevent miniMapSlider covered by other colors ([#448](https://github.com/DTStack/molecule/issues/448)) ([1b0aa04](https://github.com/DTStack/molecule/commit/1b0aa043735c4eba331978567ea76ddc92723254))
-   remove the pointer-events CSS property ([#423](https://github.com/DTStack/molecule/issues/423)) ([b3f6be6](https://github.com/DTStack/molecule/commit/b3f6be6094468ff924f647782a835f82deca6ba9))
-   remove the useless constructor arguments ([e4699bc](https://github.com/DTStack/molecule/commit/e4699bc56f2179c8b45177a2f06ecc21c3df25fc))
-   remove useless code ([#482](https://github.com/DTStack/molecule/issues/482)) ([5fbc961](https://github.com/DTStack/molecule/commit/5fbc961e2b4c3280f32c633a188da1b22c06613b))
-   renderPane exec twice ([#439](https://github.com/DTStack/molecule/issues/439)) ([5eb14a5](https://github.com/DTStack/molecule/commit/5eb14a51d4233b36e20f3db3962de579966d60fe))
-   set the collapse content width to initial value ([366a635](https://github.com/DTStack/molecule/commit/366a6355b51b89d997893dcd61036a53fe400377))
-   the sidebar collapse title style ([c27df72](https://github.com/DTStack/molecule/commit/c27df72f73d6e34e874975f14d770f9ff7f3e600))
-   the tsyringe inject the EditorModel error beacuse of the current is undefined ([#468](https://github.com/DTStack/molecule/issues/468)) ([fbb4113](https://github.com/DTStack/molecule/commit/fbb4113c985002cb032ff1ae9e5e90fa6caa5e58))
-   update the getBuiltInColors method to immutable ([b744265](https://github.com/DTStack/molecule/commit/b744265376a912b6ac18e244646c1dac751b2ba1))
-   update the hover style for list ([#476](https://github.com/DTStack/molecule/issues/476)) ([35e66fd](https://github.com/DTStack/molecule/commit/35e66fde0fc3f64d11e593a4c87222f93ca16e1f))
-   **color theme:** optimize the Panel, actionBar ([#445](https://github.com/DTStack/molecule/issues/445)) ([9ac1962](https://github.com/DTStack/molecule/commit/9ac1962f711969bc2f8f1524ef6ddc600add9b51))

## [0.9.0-alpha.5](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.4...v0.9.0-alpha.5) (2021-09-07)

### Features

-   adjust the import method ([#400](https://github.com/DTStack/molecule/issues/400)) ([1ea6238](https://github.com/DTStack/molecule/commit/1ea6238c2f77a5d40fa5a3237a2f4c9e50de08d4))
-   folderTree support to loadData ([#367](https://github.com/DTStack/molecule/issues/367)) ([a9c6ded](https://github.com/DTStack/molecule/commit/a9c6ded812350b0cb294fad5f16b70875a41deac))
-   support set the sorting of the panel ([#351](https://github.com/DTStack/molecule/issues/351)) ([95470fe](https://github.com/DTStack/molecule/commit/95470fe6696c4be0407c9205c54b3fc0ab8820da))
-   support to set the default context menu in folderTree ([#363](https://github.com/DTStack/molecule/issues/363)) ([bbb3021](https://github.com/DTStack/molecule/commit/bbb30211b59ddc8e7d85a588f76bb44776e12251))

### Bug Fixes

-   update the LocaleService initial logic ([#397](https://github.com/DTStack/molecule/issues/397)) ([c98df53](https://github.com/DTStack/molecule/commit/c98df53e633307f10e838ecf1967199c0e899966))
-   add an if statement before calling the unmountComponentAtNode ([#319](https://github.com/DTStack/molecule/issues/319)) ([99a7d81](https://github.com/DTStack/molecule/commit/99a7d819df2130a45036a70c77de81be21bb320b))
-   can't add the new panel when open it ([#390](https://github.com/DTStack/molecule/issues/390)) ([f8eddab](https://github.com/DTStack/molecule/commit/f8eddabfa9d5d66d7cc1f85efd96a065c80c2571))
-   ellipsis the title when width is too narrow ([#412](https://github.com/DTStack/molecule/issues/412)) ([55efbb7](https://github.com/DTStack/molecule/commit/55efbb7ad040b781ec7b373e0ed0cc07a4a9bbb2))
-   height is lost after adjusting the input text ([#347](https://github.com/DTStack/molecule/issues/347)) ([d4f5512](https://github.com/DTStack/molecule/commit/d4f5512b1c24b0fc37030702c77b010b4ac3f665)), closes [#317](https://github.com/DTStack/molecule/issues/317)
-   improve the style of tab title ([#409](https://github.com/DTStack/molecule/issues/409)) ([832a034](https://github.com/DTStack/molecule/commit/832a0347c3b28d58251bca60cedc9434c44798e2))
-   improve type definition of editor tab ([#341](https://github.com/DTStack/molecule/issues/341)) ([22597b4](https://github.com/DTStack/molecule/commit/22597b478620ef68478e266272cc4353890cfd65))
-   optimize the Color Theme ([#395](https://github.com/DTStack/molecule/issues/395)) ([9a6ed01](https://github.com/DTStack/molecule/commit/9a6ed01a53ab9e8ef66e16b680096b778c879cdc))
-   prevent page broken via folderTree ([#358](https://github.com/DTStack/molecule/issues/358)) ([160a798](https://github.com/DTStack/molecule/commit/160a79826188f3cd29cc294169b27f4eb53c45cb))
-   prevent page broken when data is undefined ([#361](https://github.com/DTStack/molecule/issues/361)) ([26d611a](https://github.com/DTStack/molecule/commit/26d611a1cd9e687ef23d0cef7f5b9ffb07cac7c4))
-   tree can't update root node ([#368](https://github.com/DTStack/molecule/issues/368)) ([45fe3f6](https://github.com/DTStack/molecule/commit/45fe3f6a34bf80b20bde8f112135d3da5e78dc77))

## [0.9.0-alpha.4](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.2...v0.9.0-alpha.4) (2021-08-09)

### ⚠ BREAKING CHANGES

-   **api:** different invoke methods based on molecule API

### Features

-   **menubar:** add menu bar methods to service and support shortcut keys ([#298](https://github.com/DTStack/molecule/issues/298)) ([3370b0f](https://github.com/DTStack/molecule/commit/3370b0f98466ae5075ebea2e0b0e88cb2e0a9faa)), closes [#292](https://github.com/DTStack/molecule/issues/292)
-   access the color themes globally ([1a627d5](https://github.com/DTStack/molecule/commit/1a627d5c8ae7e92f127be760254992b419f9d945))
-   access the CommandPalette globally ([f37989b](https://github.com/DTStack/molecule/commit/f37989bef65bfa325d1b23102622f87e8975acd4))
-   add checked color for activityBar item ([e09f07d](https://github.com/DTStack/molecule/commit/e09f07df8bd52a6c5f24ef4bd9ab898a80905728))
-   add CommandQuickAccessProvider and CommandQuickAccessViewAction ([5a42e22](https://github.com/DTStack/molecule/commit/5a42e223af8c7db377d86b2093bd44bba178c293))
-   add the outputEditorInstance property in panelService ([#229](https://github.com/DTStack/molecule/issues/229)) ([14ab294](https://github.com/DTStack/molecule/commit/14ab29427c982aa3ef0472f6f5cc6250911cf191))
-   add the quickAccess for the Settings ([458a4ca](https://github.com/DTStack/molecule/commit/458a4ca067acaee6d6ec54f806390cea2e0e6df2))
-   alter the Workbench tabIndex default to 0 ([f1c278b](https://github.com/DTStack/molecule/commit/f1c278bdec5cb8088cd18322a8ad62ba6c15cbc6))
-   appearance needs re layout after show or hide ([#171](https://github.com/DTStack/molecule/issues/171)) ([a809494](https://github.com/DTStack/molecule/commit/a8094943ad85fc839784e4f25b3c8bc1d8c6a630))
-   develop explorer panels ([#174](https://github.com/DTStack/molecule/issues/174)) ([921ecc5](https://github.com/DTStack/molecule/commit/921ecc564903755e74eb4e8a895be78bfe3eb7d4))
-   develop open editors window ([#183](https://github.com/DTStack/molecule/issues/183)) ([098c78c](https://github.com/DTStack/molecule/commit/098c78c05c11918a00271d4d70e90d0ae294f966))
-   editor actions & actionBar support tooltip ([#260](https://github.com/DTStack/molecule/issues/260)) ([325eea0](https://github.com/DTStack/molecule/commit/325eea001b4c559e8193acdbf78b819698b0043e))
-   editorTree & editorTabs support scroll into view ([#275](https://github.com/DTStack/molecule/issues/275)) ([07b9a0f](https://github.com/DTStack/molecule/commit/07b9a0f6ac9c0a8f96107b555d0dccd9164573e2))
-   encapsulate Action2 for workbench ([39d9e07](https://github.com/DTStack/molecule/commit/39d9e07f593501dc9a39ce9c4f9d0b93aeb62e15))
-   encapsulate common workspace Actions API based on moanco api ([1d49058](https://github.com/DTStack/molecule/commit/1d4905853d9e788348c3c527192e434edeb6805e))
-   encapsulate the quickAccessProvider ([223ea3c](https://github.com/DTStack/molecule/commit/223ea3cf163452fc1100ef0b6e48cff9f3946fdc))
-   extract layoutService ([#176](https://github.com/DTStack/molecule/issues/176)) ([a15245f](https://github.com/DTStack/molecule/commit/a15245f7a88db19a94293c3a70b66d29872e6ebc))
-   folderTree support to set entry page ([#209](https://github.com/DTStack/molecule/issues/209)) ([8c4096a](https://github.com/DTStack/molecule/commit/8c4096a9ea101451e55ef2512fd43027d2a19c06))
-   gulp task support to watch file changed ([#251](https://github.com/DTStack/molecule/issues/251)) ([7e7b963](https://github.com/DTStack/molecule/commit/7e7b963932cf9eac1f0b40c789fff1f47fe98a05))
-   improve problems panel ([#242](https://github.com/DTStack/molecule/issues/242)) ([16be69c](https://github.com/DTStack/molecule/commit/16be69c3d85252fb356cc5a7ebe417cf5a645c85))
-   menu support to set divider ([#205](https://github.com/DTStack/molecule/issues/205)) ([c8aca25](https://github.com/DTStack/molecule/commit/c8aca2577bbb4c7850c4e814e5c5f422a8e630d7))
-   move the default problems extension to stories ([#227](https://github.com/DTStack/molecule/issues/227)) ([3210191](https://github.com/DTStack/molecule/commit/3210191278f885802b2fe721d969ab13a96f5849))
-   support keybinding via extensions ([#198](https://github.com/DTStack/molecule/issues/198)) ([fb3c370](https://github.com/DTStack/molecule/commit/fb3c3703aacc519b849c1f446f5671ba79902f44))
-   support onPanelToolbarClick in explorer panels ([#218](https://github.com/DTStack/molecule/issues/218)) ([a78594f](https://github.com/DTStack/molecule/commit/a78594f1b7d552ec9d64495b56c96a6537bdf3a5))
-   support to customize welcome page ([#202](https://github.com/DTStack/molecule/issues/202)) ([1f9b24b](https://github.com/DTStack/molecule/commit/1f9b24b9fdeedd51f7ed8ccbae42840ef975f779))
-   support to toggle panels via keybinding ([#208](https://github.com/DTStack/molecule/issues/208)) ([7ae4310](https://github.com/DTStack/molecule/commit/7ae4310090dd031cd8813f6b9d210bf94ca5d0be))
-   support to toggle side bar visibility by command ([#203](https://github.com/DTStack/molecule/issues/203)) ([72b7dcc](https://github.com/DTStack/molecule/commit/72b7dcc1feb2287c2277875e9c32ca460dff41a6))
-   **collapse:** collapse panel content support to scroll ([#185](https://github.com/DTStack/molecule/issues/185)) ([c2fb3d5](https://github.com/DTStack/molecule/commit/c2fb3d51496bf903d788625485ad90852a828e76))
-   **collapse:** collaspe support to specify grow attr ([#188](https://github.com/DTStack/molecule/issues/188)) ([d5dca2e](https://github.com/DTStack/molecule/commit/d5dca2e0c7f367005af6982e30267dab96479118))
-   **component:** search input support text wrap ([#169](https://github.com/DTStack/molecule/issues/169)) ([0052f52](https://github.com/DTStack/molecule/commit/0052f5214b3d3dd901981ef5b7b5642084a24709))
-   **component:** search input support to specify validateInfo ([#167](https://github.com/DTStack/molecule/issues/167)) ([8e40782](https://github.com/DTStack/molecule/commit/8e40782d725fddb7a12d533c91f161772d0b7176))
-   **i18n:** localize support to provide dynamic replace text ([#186](https://github.com/DTStack/molecule/issues/186)) ([ad1bbc1](https://github.com/DTStack/molecule/commit/ad1bbc14dddcef720b0185d537cf8d93f360955e))
-   **i18n:** support basic il8n feature ([#170](https://github.com/DTStack/molecule/issues/170)) ([ce3c202](https://github.com/DTStack/molecule/commit/ce3c202c22bb01c5695a9ebf2eb1f2fe9a33365a)), closes [#106](https://github.com/DTStack/molecule/issues/106) [#107](https://github.com/DTStack/molecule/issues/107)
-   remove the default active animation ([b3ee198](https://github.com/DTStack/molecule/commit/b3ee1980e4746ac8530cc1816da7a6ddc2f21df9))
-   support cache SplitPane Position of Panel and reset SplitPane p… ([#161](https://github.com/DTStack/molecule/issues/161)) ([e23a6b0](https://github.com/DTStack/molecule/commit/e23a6b07bcddafcc99f6774a141ed7a745a8ea70))
-   support show or hide statusBar when rightClik statusBar panel ([#154](https://github.com/DTStack/molecule/issues/154)) ([b26c532](https://github.com/DTStack/molecule/commit/b26c532a9529da4802e91b57b56df9c0c154f441))
-   update builtin color theme ([d02f552](https://github.com/DTStack/molecule/commit/d02f5527e81c5c2d605b946836bd8645caba889a))
-   **notification:** modify styles for notification panel ([8d20149](https://github.com/DTStack/molecule/commit/8d20149955142c4717962463ad02bf862f1656e2))
-   **panel service:** modify panel service test code ([c808b12](https://github.com/DTStack/molecule/commit/c808b12cccb319926790d531b4c82105a7554ea8))
-   **problems and notification:** modify problems and notification styles and rename panel ([7c1a08d](https://github.com/DTStack/molecule/commit/7c1a08d31b30f53e5411aed1d6fbc890c39353f8))
-   **problems and notification:** update problems and notification code ([aa6b32d](https://github.com/DTStack/molecule/commit/aa6b32d6018e591a49062c3b8ced25506045aaa3)), closes [#103](https://github.com/DTStack/molecule/issues/103)
-   **tsconfig:** modify tsconfig for yarn build esm ([6c9e308](https://github.com/DTStack/molecule/commit/6c9e3086ead35c749373c5873a57cd94fae3bf0a))

### Bug Fixes

-   add animation for expand the collapse ([17995c9](https://github.com/DTStack/molecule/commit/17995c9befa8a97a83c0a1461cb5d4f80fe72598))
-   add the built-in addons for the searchPane ([#193](https://github.com/DTStack/molecule/issues/193)) ([793876d](https://github.com/DTStack/molecule/commit/793876da18709ab9bec348382962f3002621b01d))
-   add the keyword type when exporting the interface or type object ([b3966f9](https://github.com/DTStack/molecule/commit/b3966f99e3c76e91740c38750e536d4bea4b8fe3))
-   add the type keyword to the ILocale interface ([#273](https://github.com/DTStack/molecule/issues/273)) ([f58f0a1](https://github.com/DTStack/molecule/commit/f58f0a102e9ea7aed4fec05b58493133e7e10e91))
-   adjust the margin of Sidebar search control ([ad5319f](https://github.com/DTStack/molecule/commit/ad5319ffaf93a03e8045ad22dbe0931db61452bd))
-   auto adjust the output after resize the panel ([#240](https://github.com/DTStack/molecule/issues/240)) ([7cbe2ed](https://github.com/DTStack/molecule/commit/7cbe2edb27b55c1686d53bd99988bccd35b534d7))
-   code Editor content is wrong when EditorTabs are changed ([#160](https://github.com/DTStack/molecule/issues/160)) ([2b05642](https://github.com/DTStack/molecule/commit/2b056424ebb8f8c25414c8e5df5ee8c960f994f5))
-   default regsiter the SettingsController singleton ([#204](https://github.com/DTStack/molecule/issues/204)) ([442b1a9](https://github.com/DTStack/molecule/commit/442b1a9d605c20e127e954f29df13031ea8e4465))
-   disable update the tab when onUpdateTab ([aa25528](https://github.com/DTStack/molecule/commit/aa25528d059410b9888c00ce54a54c5f3c523646))
-   duplicate command palette in editor context menu ([#243](https://github.com/DTStack/molecule/issues/243)) ([111a4cd](https://github.com/DTStack/molecule/commit/111a4cdc915c829db54f6abc52b31f87e7d3f76e))
-   editor disappear when closing a maximized panel ([#287](https://github.com/DTStack/molecule/issues/287)) ([7b32028](https://github.com/DTStack/molecule/commit/7b32028782f8c9a2b1b3a478e66cfb84fea10515))
-   fix editor tabs cannot scroll ([#219](https://github.com/DTStack/molecule/issues/219)) ([3b8db4c](https://github.com/DTStack/molecule/commit/3b8db4c908d1efe027acbe8fac5214f39eae85eb))
-   fix keybinding invalidation when dispose an editor ([#258](https://github.com/DTStack/molecule/issues/258)) ([4399719](https://github.com/DTStack/molecule/commit/43997191a3346160c74f5f91c32863caa585adad))
-   fix problem panel cannot open after hiding ([#239](https://github.com/DTStack/molecule/issues/239)) ([4d80bf9](https://github.com/DTStack/molecule/commit/4d80bf96e517a12ec8f6635df21b5e298234b61c))
-   icon support spin ([#250](https://github.com/DTStack/molecule/issues/250)) ([f8a89e2](https://github.com/DTStack/molecule/commit/f8a89e2e11abd00f20a073884260f9b56e5b7ed8))
-   icon support to customize ([#256](https://github.com/DTStack/molecule/issues/256)) ([2bf9b7d](https://github.com/DTStack/molecule/commit/2bf9b7d39f606e731226b97d732ec8d406407b06))
-   improve active style and prevent the show of native contextMenu ([#225](https://github.com/DTStack/molecule/issues/225)) ([70f3804](https://github.com/DTStack/molecule/commit/70f3804f01435c0d40a7ff79b40ea94aaf4d5311))
-   improve activity context menu behavior ([#206](https://github.com/DTStack/molecule/issues/206)) ([20c191d](https://github.com/DTStack/molecule/commit/20c191d2480d969060c0a86cfe46999e41ceaeb2))
-   improve close group in editor ([#267](https://github.com/DTStack/molecule/issues/267)) ([26bd958](https://github.com/DTStack/molecule/commit/26bd958c06525fb2da2cf3c78b8a73faf6ec063b))
-   improve dropdown get relative position ([#222](https://github.com/DTStack/molecule/issues/222)) ([e46c5de](https://github.com/DTStack/molecule/commit/e46c5debd8fd82f2b74ad0ce0d8f4c78c52e830b))
-   improve duplicated key in tree ([#255](https://github.com/DTStack/molecule/issues/255)) ([b482f8f](https://github.com/DTStack/molecule/commit/b482f8f69eceaeb56f8eba98098f761e635aa739))
-   improve editor actions ([#234](https://github.com/DTStack/molecule/issues/234)) ([443d4da](https://github.com/DTStack/molecule/commit/443d4dae10278f56b8f44b54fcdcf6a865192f9a))
-   improve editor content change ([a9d2bcf](https://github.com/DTStack/molecule/commit/a9d2bcfef6ed9a0321722c99761a7d865c9f6aca))
-   improve editor icon ([#268](https://github.com/DTStack/molecule/issues/268)) ([3d45c87](https://github.com/DTStack/molecule/commit/3d45c875f7a06677c7c640d56dc20b862c145451))
-   improve editor tabs change ([c1c34e2](https://github.com/DTStack/molecule/commit/c1c34e2c69a1510dc9b62f5219639da407ee4861))
-   improve global contextMenu in activity bar ([#200](https://github.com/DTStack/molecule/issues/200)) ([7ad58ab](https://github.com/DTStack/molecule/commit/7ad58abbe460e8c17404f2c81a64a4eec1d431aa))
-   improve minimap color theme & problem color theme ([#270](https://github.com/DTStack/molecule/issues/270)) ([958928e](https://github.com/DTStack/molecule/commit/958928ec4c9d51c95a44781bce393e95cee1cd4a))
-   improve problems service functions ([#254](https://github.com/DTStack/molecule/issues/254)) ([7382c03](https://github.com/DTStack/molecule/commit/7382c030a1d3234da80fe21d894935278a95f99a))
-   improve the timing when to check empty of panels ([#233](https://github.com/DTStack/molecule/issues/233)) ([5ef6a82](https://github.com/DTStack/molecule/commit/5ef6a823177b30cef7195444186aaefb2ee7c204))
-   improve tooltip in different color theme ([#304](https://github.com/DTStack/molecule/issues/304)) ([219c241](https://github.com/DTStack/molecule/commit/219c241c55a49ba15757ec23bec63fd15b358418))
-   improve tree helper types ([#248](https://github.com/DTStack/molecule/issues/248)) ([1dfee56](https://github.com/DTStack/molecule/commit/****************************************))
-   improve unreasonable design ([#232](https://github.com/DTStack/molecule/issues/232)) ([74f8976](https://github.com/DTStack/molecule/commit/74f8976f17c9297aab15744754fa8dcb20be07cf))
-   improve workbench height & box-sizing ([#223](https://github.com/DTStack/molecule/issues/223)) ([9f25dd5](https://github.com/DTStack/molecule/commit/9f25dd57de2ccdbaf40042a35cf5236f8747d54e))
-   location render incorrect after editing ([d0877f0](https://github.com/DTStack/molecule/commit/d0877f004f1bbac7f424745241f6a39f3a6025f7))
-   maximized icon displayed incorrect ([#281](https://github.com/DTStack/molecule/issues/281)) ([58888d5](https://github.com/DTStack/molecule/commit/58888d5dd1d5536122adb76bdbd184c576851333))
-   output panel didn't save data when switching panels ([#252](https://github.com/DTStack/molecule/issues/252)) ([92b7321](https://github.com/DTStack/molecule/commit/92b73210a2109322a978594e04d2766f66e6c7f0))
-   panels support to close ([#257](https://github.com/DTStack/molecule/issues/257)) ([3d10746](https://github.com/DTStack/molecule/commit/3d10746484f6190d67cb46a68d003c6515a114f6))
-   remove the updateTab action ([a95e4db](https://github.com/DTStack/molecule/commit/a95e4db789b9962b2a12bddfac597e1a2931d83c))
-   remove the useless injectable decorator ([ec61d0f](https://github.com/DTStack/molecule/commit/ec61d0fbe9f6a117368c4aabecf1cca8a6072598))
-   set the outline as 0 for the root element ([#177](https://github.com/DTStack/molecule/issues/177)) ([d2e2c6d](https://github.com/DTStack/molecule/commit/d2e2c6d48effe56240b4c22136b8805a216aeca7))
-   setting config not working when deleting property ([#309](https://github.com/DTStack/molecule/issues/309)) ([23116af](https://github.com/DTStack/molecule/commit/23116af9249488e25f39f84394696eee806c946b))
-   the editor content incorrect when changing tabs ([#210](https://github.com/DTStack/molecule/issues/210)) ([7d59c07](https://github.com/DTStack/molecule/commit/7d59c07e3d8eb8b2e56bccfc41c9222647271d82))
-   the value still in when file reopen ([#228](https://github.com/DTStack/molecule/issues/228)) ([2b9002b](https://github.com/DTStack/molecule/commit/2b9002b72cf608b56b52d1d84ea2ce31123b709b))
-   update foldertree new folder ([#247](https://github.com/DTStack/molecule/issues/247)) ([c505abe](https://github.com/DTStack/molecule/commit/c505abec149e252d2b529fc1a19abca2418815d6))
-   **build:** replace the path named mo with relative path ([8146eee](https://github.com/DTStack/molecule/commit/8146eeede60a8436aafb679cd979aea7ec9c909b))
-   **collapse:** fix calc content height failed because of height: 100% ([#194](https://github.com/DTStack/molecule/issues/194)) ([f6a6ba6](https://github.com/DTStack/molecule/commit/f6a6ba649969b68a0adafc7c37049801f8e5f362))
-   **collapse:** improve the timing to detect panel content whether empty ([#184](https://github.com/DTStack/molecule/issues/184)) ([eb70c6d](https://github.com/DTStack/molecule/commit/eb70c6da486802c00b545b090b1b409753ce9b5e))
-   **collapse:** open editor not expand expected ([#196](https://github.com/DTStack/molecule/issues/196)) ([2af530f](https://github.com/DTStack/molecule/commit/2af530f27abac057581c3cf46a4b10e8c0a433a5))
-   **editor:** fix report error message when close tab in editor ([#207](https://github.com/DTStack/molecule/issues/207)) ([32daee2](https://github.com/DTStack/molecule/commit/32daee232258ae1f1aa6530c1acb529c3dc222b7))
-   **folderTree:** improve add root folder & delete dialog ([#192](https://github.com/DTStack/molecule/issues/192)) ([c49e534](https://github.com/DTStack/molecule/commit/c49e534139ffa4c3f48607e15a020709b9a47bf4))
-   **global:** set the color of Tag a as inherit ([#216](https://github.com/DTStack/molecule/issues/216)) ([54cc07c](https://github.com/DTStack/molecule/commit/54cc07c80ebaa83819c4d932e94fa5d66c4e7fc3)), closes [#187](https://github.com/DTStack/molecule/issues/187)
-   **menu:** improve menu interactive behavior ([#199](https://github.com/DTStack/molecule/issues/199)) ([a8ba3e3](https://github.com/DTStack/molecule/commit/a8ba3e3049e91719bc8fe63d128f153fb54a9fcf))
-   update the color theme ([58df79b](https://github.com/DTStack/molecule/commit/58df79b8c519b9ab4e415f4cc6acb0ecf95e64d8))
-   **monaco:** import default language contributions ([6d0fdbc](https://github.com/DTStack/molecule/commit/6d0fdbc1a6382e926053bea1f48be94f388b93ac))

-   **api:** refactor the molecule API and interfaces ([fa01143](https://github.com/DTStack/molecule/commit/fa0114304c9d511775131e0d1b0d36f8900a7f36)), closes [#143](https://github.com/DTStack/molecule/issues/143) [#153](https://github.com/DTStack/molecule/issues/153)

## [0.9.0-alpha.3.1](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.3...v0.9.0-alpha.3.1) (2021-07-15)

### Features

-   improve problems panel ([#242](https://github.com/DTStack/molecule/issues/242)) ([16be69c](https://github.com/DTStack/molecule/commit/16be69c3d85252fb356cc5a7ebe417cf5a645c85))

### Bug Fixes

-   auto adjust the output after resize the panel ([#240](https://github.com/DTStack/molecule/issues/240)) ([7cbe2ed](https://github.com/DTStack/molecule/commit/7cbe2edb27b55c1686d53bd99988bccd35b534d7))
-   duplicate command palette in editor context menu ([#243](https://github.com/DTStack/molecule/issues/243)) ([111a4cd](https://github.com/DTStack/molecule/commit/111a4cdc915c829db54f6abc52b31f87e7d3f76e))
-   fix problem panel cannot open after hiding ([#239](https://github.com/DTStack/molecule/issues/239)) ([4d80bf9](https://github.com/DTStack/molecule/commit/4d80bf96e517a12ec8f6635df21b5e298234b61c))
-   improve editor actions ([#234](https://github.com/DTStack/molecule/issues/234)) ([443d4da](https://github.com/DTStack/molecule/commit/443d4dae10278f56b8f44b54fcdcf6a865192f9a))
-   improve the timing when to check empty of panels ([#233](https://github.com/DTStack/molecule/issues/233)) ([5ef6a82](https://github.com/DTStack/molecule/commit/5ef6a823177b30cef7195444186aaefb2ee7c204))
-   improve tree helper types ([#248](https://github.com/DTStack/molecule/issues/248)) ([1dfee56](https://github.com/DTStack/molecule/commit/****************************************))
-   improve unreasonable design ([#232](https://github.com/DTStack/molecule/issues/232)) ([74f8976](https://github.com/DTStack/molecule/commit/74f8976f17c9297aab15744754fa8dcb20be07cf))
-   update foldertree new folder ([#247](https://github.com/DTStack/molecule/issues/247)) ([c505abe](https://github.com/DTStack/molecule/commit/c505abec149e252d2b529fc1a19abca2418815d6))

## 0.9.0-alpha.3 (2021-07-10)

### ⚠ BREAKING CHANGES

-   **api:** different invoke methods based on molecule API

### Features

-   access the color themes globally ([1a627d5](https://github.com/DTStack/molecule/commit/1a627d5c8ae7e92f127be760254992b419f9d945))
-   access the CommandPalette globally ([f37989b](https://github.com/DTStack/molecule/commit/f37989bef65bfa325d1b23102622f87e8975acd4))
-   add checked color for activityBar item ([e09f07d](https://github.com/DTStack/molecule/commit/e09f07df8bd52a6c5f24ef4bd9ab898a80905728))
-   add colorRegistery ([ff55f06](https://github.com/DTStack/molecule/commit/ff55f06f3e7886588cd1c9efd93ecbac8ef53097))
-   add colorRegistery ([84f5780](https://github.com/DTStack/molecule/commit/84f57804064d885f538a8d950bb3be5aa98b62b1))
-   add CommandQuickAccessProvider and CommandQuickAccessViewAction ([5a42e22](https://github.com/DTStack/molecule/commit/5a42e223af8c7db377d86b2093bd44bba178c293))
-   add testing sidebar panel ([d9b9fdc](https://github.com/DTStack/molecule/commit/d9b9fdc0f526a1e8af6820746aada7a8415da32f))
-   add the outputEditorInstance property in panelService ([#229](https://github.com/DTStack/molecule/issues/229)) ([14ab294](https://github.com/DTStack/molecule/commit/14ab29427c982aa3ef0472f6f5cc6250911cf191))
-   add the quickAccess for the Settings ([458a4ca](https://github.com/DTStack/molecule/commit/458a4ca067acaee6d6ec54f806390cea2e0e6df2))
-   alter the Workbench tabIndex default to 0 ([f1c278b](https://github.com/DTStack/molecule/commit/f1c278bdec5cb8088cd18322a8ad62ba6c15cbc6))
-   appearance needs re layout after show or hide ([#171](https://github.com/DTStack/molecule/issues/171)) ([a809494](https://github.com/DTStack/molecule/commit/a8094943ad85fc839784e4f25b3c8bc1d8c6a630))
-   code format premitter ([519a651](https://github.com/DTStack/molecule/commit/519a65162b2801857dc6eb6cfd55a3548d844095))
-   delete debugger ([2493cd7](https://github.com/DTStack/molecule/commit/2493cd772b6c92429519d948f5a8184faf7aec8a))
-   delete unless code ([be4091d](https://github.com/DTStack/molecule/commit/be4091df8c179028b79cdecb8c0d80a7dce15df4))
-   develop explorer panels ([#174](https://github.com/DTStack/molecule/issues/174)) ([921ecc5](https://github.com/DTStack/molecule/commit/921ecc564903755e74eb4e8a895be78bfe3eb7d4))
-   develop open editors window ([#183](https://github.com/DTStack/molecule/issues/183)) ([098c78c](https://github.com/DTStack/molecule/commit/098c78c05c11918a00271d4d70e90d0ae294f966))
-   display the editor line and column info on statusBar ([f502adb](https://github.com/DTStack/molecule/commit/f502adb35b87af0dc33adde8fca9b256bf11aa72))
-   encapsulate Action2 for workbench ([39d9e07](https://github.com/DTStack/molecule/commit/39d9e07f593501dc9a39ce9c4f9d0b93aeb62e15))
-   encapsulate common workspace Actions API based on moanco api ([1d49058](https://github.com/DTStack/molecule/commit/1d4905853d9e788348c3c527192e434edeb6805e))
-   encapsulate the quickAccessProvider ([223ea3c](https://github.com/DTStack/molecule/commit/223ea3cf163452fc1100ef0b6e48cff9f3946fdc))
-   explorer service add ([9963180](https://github.com/DTStack/molecule/commit/9963180e4007f5182ebf4f305e505ef79e54d864))
-   extract layoutService ([#176](https://github.com/DTStack/molecule/issues/176)) ([a15245f](https://github.com/DTStack/molecule/commit/a15245f7a88db19a94293c3a70b66d29872e6ebc))
-   extract ITab interface to tabComponent ([3645f46](https://github.com/DTStack/molecule/commit/3645f466bb4cd05647140e1eb16c780bb0f7e3d3))
-   extract overside tabDataInterface ([c7a6857](https://github.com/DTStack/molecule/commit/c7a6857be5f5a02812b1913b365dbfb645a1bcc1))
-   folder service test ([4d13957](https://github.com/DTStack/molecule/commit/4d139578c0a0ad5b2aa6a32142b7c81e12447755))
-   folderTree support to set entry page ([#209](https://github.com/DTStack/molecule/issues/209)) ([8c4096a](https://github.com/DTStack/molecule/commit/8c4096a9ea101451e55ef2512fd43027d2a19c06))
-   menu support to set divider ([#205](https://github.com/DTStack/molecule/issues/205)) ([c8aca25](https://github.com/DTStack/molecule/commit/c8aca2577bbb4c7850c4e814e5c5f422a8e630d7))
-   move the default problems extension to stories ([#227](https://github.com/DTStack/molecule/issues/227)) ([3210191](https://github.com/DTStack/molecule/commit/3210191278f885802b2fe721d969ab13a96f5849))
-   remove the default active animation ([b3ee198](https://github.com/DTStack/molecule/commit/b3ee1980e4746ac8530cc1816da7a6ddc2f21df9))
-   support cache SplitPane Position of Panel and reset SplitPane p… ([#161](https://github.com/DTStack/molecule/issues/161)) ([e23a6b0](https://github.com/DTStack/molecule/commit/e23a6b07bcddafcc99f6774a141ed7a745a8ea70))
-   support keybinding via extensions ([#198](https://github.com/DTStack/molecule/issues/198)) ([fb3c370](https://github.com/DTStack/molecule/commit/fb3c3703aacc519b849c1f446f5671ba79902f44))
-   support onPanelToolbarClick in explorer panels ([#218](https://github.com/DTStack/molecule/issues/218)) ([a78594f](https://github.com/DTStack/molecule/commit/a78594f1b7d552ec9d64495b56c96a6537bdf3a5))
-   support to customize welcome page ([#202](https://github.com/DTStack/molecule/issues/202)) ([1f9b24b](https://github.com/DTStack/molecule/commit/1f9b24b9fdeedd51f7ed8ccbae42840ef975f779))
-   support to toggle panels via keybinding ([#208](https://github.com/DTStack/molecule/issues/208)) ([7ae4310](https://github.com/DTStack/molecule/commit/7ae4310090dd031cd8813f6b9d210bf94ca5d0be))
-   support to toggle side bar visibility by command ([#203](https://github.com/DTStack/molecule/issues/203)) ([72b7dcc](https://github.com/DTStack/molecule/commit/72b7dcc1feb2287c2277875e9c32ca460dff41a6))
-   **collapse:** collapse panel content support to scroll ([#185](https://github.com/DTStack/molecule/issues/185)) ([c2fb3d5](https://github.com/DTStack/molecule/commit/c2fb3d51496bf903d788625485ad90852a828e76))
-   **collapse:** collaspe support to specify grow attr ([#188](https://github.com/DTStack/molecule/issues/188)) ([d5dca2e](https://github.com/DTStack/molecule/commit/d5dca2e0c7f367005af6982e30267dab96479118))
-   **component:** search input support text wrap ([#169](https://github.com/DTStack/molecule/issues/169)) ([0052f52](https://github.com/DTStack/molecule/commit/0052f5214b3d3dd901981ef5b7b5642084a24709))
-   **component:** search input support to specify validateInfo ([#167](https://github.com/DTStack/molecule/issues/167)) ([8e40782](https://github.com/DTStack/molecule/commit/8e40782d725fddb7a12d533c91f161772d0b7176))
-   **editor:** add split window and some features for editor ([3bf957b](https://github.com/DTStack/molecule/commit/3bf957b079f228476d0c4560d4bb55af2cd8539e))
-   **i18n:** localize support to provide dynamic replace text ([#186](https://github.com/DTStack/molecule/issues/186)) ([ad1bbc1](https://github.com/DTStack/molecule/commit/ad1bbc14dddcef720b0185d537cf8d93f360955e))
-   **i18n:** support basic il8n feature ([#170](https://github.com/DTStack/molecule/issues/170)) ([ce3c202](https://github.com/DTStack/molecule/commit/ce3c202c22bb01c5695a9ebf2eb1f2fe9a33365a)), closes [#106](https://github.com/DTStack/molecule/issues/106) [#107](https://github.com/DTStack/molecule/issues/107)
-   support show or hide statusBar when rightClik statusBar panel ([#154](https://github.com/DTStack/molecule/issues/154)) ([b26c532](https://github.com/DTStack/molecule/commit/b26c532a9529da4802e91b57b56df9c0c154f441))
-   update builtin color theme ([d02f552](https://github.com/DTStack/molecule/commit/d02f5527e81c5c2d605b946836bd8645caba889a))
-   **editor:** custom the TabPane renderer ([b65d221](https://github.com/DTStack/molecule/commit/b65d221bd726853b17297096eb293e496482dbd2))
-   **monaco:** add json language ([dd4846c](https://github.com/DTStack/molecule/commit/dd4846c75a51b6f18f1e2c9d0c55ea72b91fc3c8))
-   **notification:** modify styles for notification panel ([8d20149](https://github.com/DTStack/molecule/commit/8d20149955142c4717962463ad02bf862f1656e2))
-   **panel:** add intitial service, model and controller ([2c4eac3](https://github.com/DTStack/molecule/commit/2c4eac318c0dd87ba81b5de22a38107c7a0d0500))
-   **panel:** support add, remove and update Panel features, builtin OUTPUT and PROBLEMS panel ([909bcc8](https://github.com/DTStack/molecule/commit/909bcc840b2a8ee2883ea0e3ed5e2dd41e126703)), closes [#19](https://github.com/DTStack/molecule/issues/19) [#22](https://github.com/DTStack/molecule/issues/22)
-   **panel service:** modify panel service test code ([c808b12](https://github.com/DTStack/molecule/commit/c808b12cccb319926790d531b4c82105a7554ea8))
-   **problems and notification:** update problems and notification code ([aa6b32d](https://github.com/DTStack/molecule/commit/aa6b32d6018e591a49062c3b8ced25506045aaa3)), closes [#103](https://github.com/DTStack/molecule/issues/103)
-   **settings:** add basic Settings feature ([8bceabd](https://github.com/DTStack/molecule/commit/8bceabdfb1734f7d35c797c4362e5ec18a867707)), closes [#104](https://github.com/DTStack/molecule/issues/104)
-   **tsconfig:** modify tsconfig for yarn build esm ([6c9e308](https://github.com/DTStack/molecule/commit/6c9e3086ead35c749373c5873a57cd94fae3bf0a))
-   **utils:** add flatObject and normalizeFlattedObject functions ([cb93d7c](https://github.com/DTStack/molecule/commit/cb93d7c9f67100f8045138da4944755ae1931a3d))
-   add addConextMenu removeContextMenu activityServices ([10411e3](https://github.com/DTStack/molecule/commit/10411e338e664bacde0f559b4e8dc5ad3623526c))
-   add onTabChange and onToolbarClick interfaces in panelService ([6cab8f0](https://github.com/DTStack/molecule/commit/6cab8f05f68e6851167f5e38e775cead16c54bbd)), closes [#45](https://github.com/DTStack/molecule/issues/45)
-   connect state and view by connect method ([fc9188f](https://github.com/DTStack/molecule/commit/fc9188fb1c61d73d64a313dd4c3687604aa6fa09))
-   define contextMenu command id ([b08f56f](https://github.com/DTStack/molecule/commit/b08f56f280d90d846aa0b62eb4385a9ede9f5235))
-   editorService support subscribe onMoveTab, onSelectTab, onCloseAll, onCloseTab, onCloseOth etc ([#140](https://github.com/DTStack/molecule/issues/140)) ([48c8984](https://github.com/DTStack/molecule/commit/48c898469e84538b93730389a93fb7103cb37f53))
-   explorer headerToolBar feats add ([c3ac8af](https://github.com/DTStack/molecule/commit/c3ac8af266ec9eeed080a69a609be2427d88e6e3))
-   export the shadowClassName of contextView ([7875f19](https://github.com/DTStack/molecule/commit/7875f19107ba0c63fc81fd62c86fbcefe2cca82e))
-   extract editor modified logic to extensions ([#138](https://github.com/DTStack/molecule/issues/138)) ([70aafcf](https://github.com/DTStack/molecule/commit/70aafcf30ccb9af536babdb5fbb7c5961050de68))
-   extract logic to extensions ([cd47341](https://github.com/DTStack/molecule/commit/cd473417a31870132bee8add434ebb2e0631b545))
-   extract overside logic ([1f089fb](https://github.com/DTStack/molecule/commit/1f089fb527f4fa28defd2750193abc87b001decd))
-   extract randomId ([459d867](https://github.com/DTStack/molecule/commit/459d867c4be348b048534c3c7216f8589cc9e8ab))
-   extract TreeViewUtil to help file ([883de0f](https://github.com/DTStack/molecule/commit/883de0fba6241c1378a399608cd251cc710149ab))
-   import mo.scss manually ([1d4fb98](https://github.com/DTStack/molecule/commit/1d4fb9878538f7c30cc263c97fd31b7f887b98dd))
-   listen sample folder contextmenu event ([46343fd](https://github.com/DTStack/molecule/commit/46343fd9745ef862266f6b039858e9e21816f52b))
-   optimize fileType definition ([768f176](https://github.com/DTStack/molecule/commit/768f17633c9988fb44190abcd21101981309d40d))
-   optimize folderPanel contextMenu logic and extract style to mo ([d1191a5](https://github.com/DTStack/molecule/commit/d1191a57ec155b6cba33bdbd7de926878352c6a6))
-   optimize moduleName ([eb68f89](https://github.com/DTStack/molecule/commit/eb68f895033e2152e592bf81329cb5c436d9d4dc))
-   optimize some named ([315d520](https://github.com/DTStack/molecule/commit/315d520718ad84d17e0bb0d40c2825cce0c0dda8))
-   optimzie activityBar onContextMenuClick method ([60056ad](https://github.com/DTStack/molecule/commit/60056ade362debcfa3d4de679a496a1a64bdd3ac))
-   optmize stories ([c2bf6b0](https://github.com/DTStack/molecule/commit/c2bf6b0efedee73e5c5dff45efe8d069d6287cc1))
-   prettier format code ([768885c](https://github.com/DTStack/molecule/commit/768885c385d06b6c149c64c7e41ccfd62dfaaf18))
-   prettier validate format ([3c6ffe4](https://github.com/DTStack/molecule/commit/3c6ffe4ab1392d5424510c5ba756430f4da8d6e2))
-   resolve [#37](https://github.com/DTStack/molecule/issues/37) ([3597665](https://github.com/DTStack/molecule/commit/35976653f588aee432e410af30c1a0bbb6c0b1e7))
-   support show or hide menuBar search and explorer ([0618d12](https://github.com/DTStack/molecule/commit/0618d12f6cd2aa1fd89efbd7fbdf9792ae05337b))
-   sync main code ([381f11a](https://github.com/DTStack/molecule/commit/381f11a066360ec6090cd6da45284df7c2dbb3fe))
-   **icon:** add onClick prop in Icon ([b9fed09](https://github.com/DTStack/molecule/commit/b9fed0966566fc0d6d59d5f1a591b77fafffc811))
-   **notification:** add simple Notification module ([a99d855](https://github.com/DTStack/molecule/commit/a99d855ff5e05d14ba60bd50d0bdbd4c88a64fdc)), closes [#18](https://github.com/DTStack/molecule/issues/18)
-   **panel:** support show or hide, and maximize or restore the Panel ([a3c8d85](https://github.com/DTStack/molecule/commit/a3c8d855680b289f01a45b2e5228cc19335ca002)), closes [#19](https://github.com/DTStack/molecule/issues/19)
-   **problems and notification:** modify problems and notification styles and rename panel ([7c1a08d](https://github.com/DTStack/molecule/commit/7c1a08d31b30f53e5411aed1d6fbc890c39353f8))
-   **status bar:** tuning the status bar component ([5db28ec](https://github.com/DTStack/molecule/commit/5db28ec3ab7d96ed9bf9be42b3356552329c5d17))
-   **status/bar:** modify status bar item ([cc14adf](https://github.com/DTStack/molecule/commit/cc14adfb656fc78a12cd592f1ce651784da89140))
-   update the menu item height to 1.8em ([ac345cc](https://github.com/DTStack/molecule/commit/ac345cce965ffc8b6581615a1e41c24e4cf1e1f9))
-   **components:** add Breadcrumb ([70d6dd5](https://github.com/DTStack/molecule/commit/70d6dd58f763376669db663143a6e6780f878d59))
-   **react:** add abstract class Controller ([a55dc0f](https://github.com/DTStack/molecule/commit/a55dc0f4a2e0106dbfa681ba9340afa277a1601a))
-   **react:** add Connector for service, view and controller ([88dcc80](https://github.com/DTStack/molecule/commit/88dcc80304b896e197eac00f2dc7b49a00d41f4f))
-   **settings:** add intitial service and model ([57597f1](https://github.com/DTStack/molecule/commit/57597f1572d990c5354b09e2036c04e508543bd1))
-   **theme:** add default palenight theme ([d5382f8](https://github.com/DTStack/molecule/commit/d5382f8afce8b4dc346ec8c0d75b310ce576cdce))
-   **theme:** add default palenight theme ([6df509a](https://github.com/DTStack/molecule/commit/6df509afe652f12c552bddd141de91f7727133ff))
-   **theme:** compatible with vscode theme ([768abf2](https://github.com/DTStack/molecule/commit/768abf22c7de618abdc630a66bf54fd9bdc8d75a))
-   **theme:** compatible with vscode theme ([4839c84](https://github.com/DTStack/molecule/commit/4839c842b50b35cabb4a790ce1dccc2f481ecf09))
-   add basic Checkbox component ([550d04a](https://github.com/DTStack/molecule/commit/550d04ac3aadc0ef994649cf07e19b714e3e7e0f))
-   add basic Select component ([027535d](https://github.com/DTStack/molecule/commit/027535dc69ac2f648a682ce9e8e851a5a9e50537))
-   add basic statusBar ([4c9b91c](https://github.com/DTStack/molecule/commit/4c9b91c4a13113ce17c120b3271e351463d8c782))
-   add draft dialog component ([02aa023](https://github.com/DTStack/molecule/commit/02aa0234baa06ea19b2eac78fe7d58982f15619b))
-   add getAttr function ([10199b8](https://github.com/DTStack/molecule/commit/10199b8c24ac5f76866bd1ea0915b76fe122a51b))
-   add serviceTab logic ([bbc1973](https://github.com/DTStack/molecule/commit/bbc197330547ec291bd971b901d6fd36c0bbbb6b))
-   delete service add ([293d0ee](https://github.com/DTStack/molecule/commit/293d0ee17767f6bc6818f0537ea62ac6d9d1415d))
-   eliminate omit.js NPM package ([baac047](https://github.com/DTStack/molecule/commit/baac047e247b556d141f8d32a03154d07744b907))
-   enable afterClose callback ([939f16e](https://github.com/DTStack/molecule/commit/939f16e70058c492d736a58a7b8decb04581260d))
-   extract colorValue to theme ([dee0a71](https://github.com/DTStack/molecule/commit/dee0a715f02a26a446bcc0af589aa3c3f1ed7956))
-   extract css to theme ([966c0ae](https://github.com/DTStack/molecule/commit/966c0aea8257d3d1c3020e11e74006574a1960f0))
-   extract the ClassName out of the function; Prevent duplicate execution ([056ef35](https://github.com/DTStack/molecule/commit/056ef3562db74e806b3b6c5640362391914c24bc))
-   finish dialog interface ([8642a90](https://github.com/DTStack/molecule/commit/8642a90913f394f9596f5742670c7e73aa0be3f9))
-   lock rc package version ([c8a7999](https://github.com/DTStack/molecule/commit/c8a79992408b72053e5d19f46b71a5bb1e71fd7b))
-   optimize actionButton interface ([aca2fbb](https://github.com/DTStack/molecule/commit/aca2fbb1e74eae82996054981437010f46b197f1))
-   optimize code ([f97c818](https://github.com/DTStack/molecule/commit/f97c818456840c580a0157960ad68de8ce7e7eba))
-   optimize code ([2ddcbb8](https://github.com/DTStack/molecule/commit/2ddcbb83d55e79ed82d6f0dd5671bf11eed571a9))
-   optimize dataType interface name ([7023127](https://github.com/DTStack/molecule/commit/7023127dcc040fc8a511447107f961ceeb6a681e))
-   optimize default action ([36919df](https://github.com/DTStack/molecule/commit/36919dfaf9874c267edf72aee211b80b2862dcb6))
-   optmize type interface ([f299d4e](https://github.com/DTStack/molecule/commit/f299d4e0375e04c4613a78a0ed5eab8bf66d8b50))
-   refactor closeTab logic ([70f85e4](https://github.com/DTStack/molecule/commit/70f85e49b53c150e4c69428570ba5c1d9643c33e))
-   refactor scss ([af491c4](https://github.com/DTStack/molecule/commit/af491c41b3af227b9cceec4d89a0a32a420b1a6d))
-   relation to issue [#10](https://github.com/DTStack/molecule/issues/10) ([6e77c1f](https://github.com/DTStack/molecule/commit/6e77c1f04f78b667d31e19bbfd34250f49df2673))
-   remove noUsed functionDefine ([4591cf5](https://github.com/DTStack/molecule/commit/4591cf52cb99ff87144b961062758def52c6e12b))
-   remove unless code ([ec9a643](https://github.com/DTStack/molecule/commit/ec9a6434cc16863305e35dd16738fe1151b5bd33))
-   resolve closeTab backfill ([32acfd5](https://github.com/DTStack/molecule/commit/32acfd581b0f0f76c3af97a8ecbb0f2741155f25))
-   resolve conflicts ([9c38fee](https://github.com/DTStack/molecule/commit/9c38fee5bc83d2636cbc9f152973752c1967c954))
-   resolve conflicts ([2347396](https://github.com/DTStack/molecule/commit/2347396ba4de8f32764128a6535d5e1722f6097b))
-   resolve emitEvent params missing ([fa829a7](https://github.com/DTStack/molecule/commit/fa829a78506a0a595210fa01267fbfa20cd24eaf))
-   resovle [#37](https://github.com/DTStack/molecule/issues/37) ([dd2577d](https://github.com/DTStack/molecule/commit/dd2577d4ec2f5ffbab491c2d255ea29d6ae9ea27))
-   sync code ([2406e72](https://github.com/DTStack/molecule/commit/2406e72d8f3afc695419b12f256adb74046aef95))
-   sync code ([34263ca](https://github.com/DTStack/molecule/commit/34263ca6f9533a2d76d3c292afab4a553f37f1f3))
-   sync dev code ([e81554d](https://github.com/DTStack/molecule/commit/e81554dddb0b2b7557f5cad7612d554a04df15aa))
-   test editor logic ([6141663](https://github.com/DTStack/molecule/commit/61416631223c7b4283812e169cdb00707bbe9ee5))
-   use resetProps ([d6200a9](https://github.com/DTStack/molecule/commit/d6200a976e5c153a28d4ab6730807707c263b699))
-   **contextview:** add more contextview demo ([868e190](https://github.com/DTStack/molecule/commit/868e190867f8e40c5ce206d6b9074b9ae268c047))
-   add basic Input and TextArea component ([e00d81e](https://github.com/DTStack/molecule/commit/e00d81e78308604c66377d2365e8430939d9af65))
-   add Button component ([daa88ee](https://github.com/DTStack/molecule/commit/daa88ee1f47e9aba2262c197097afbabfe95c111))
-   add Button component ([9cb82b7](https://github.com/DTStack/molecule/commit/9cb82b7d1d82859bb926a4d61fa3d8bc233d8542))
-   add draft inputbox component ([6fd365e](https://github.com/DTStack/molecule/commit/6fd365ebe754b84e90e287f48c0f4fb0535a1506))
-   add draft scrollabe ([d77b6b0](https://github.com/DTStack/molecule/commit/d77b6b0afb878d64357df1be059b171a45448852))
-   add draft tabs stories ([78c25f9](https://github.com/DTStack/molecule/commit/78c25f9ea38495fa779468282d6ec909ace9b16a))
-   add Icon component ([d342606](https://github.com/DTStack/molecule/commit/d3426067ee81a94bb762994dfdc9bab335057ad9))
-   add Icon component ([bff6978](https://github.com/DTStack/molecule/commit/bff697885ead0a10bb8ba6b3f5b32f95e1574f24))
-   add keyCodes.ts ([5b968d1](https://github.com/DTStack/molecule/commit/5b968d1b867ac0e50a5cb072cf76d0a8d0423709))
-   add Scrollable component ([8691459](https://github.com/DTStack/molecule/commit/869145917e3de5b098dcf6cd60c4c528d9cd52ba))
-   add second version of Tab component ([6aabcf6](https://github.com/DTStack/molecule/commit/6aabcf685d4fe8831b19c86e36aaad0770e126cb))
-   adjust filePath ([a839ef7](https://github.com/DTStack/molecule/commit/a839ef7e8bb99e8e9faea885192a792f7dc729af))
-   conetxtMenu add ([d3f334a](https://github.com/DTStack/molecule/commit/d3f334ad66355a4178733c26fac36a39e36fb4e8))
-   delete scrollble component ([656ec89](https://github.com/DTStack/molecule/commit/656ec89efa7b34d9c1645be4e12c538486d4a28b))
-   delete unless file ([4b3573e](https://github.com/DTStack/molecule/commit/4b3573e8d85f64c5a05a4c3860238e471ee087c8))
-   extend the ClassNames interface ([9fdc1d9](https://github.com/DTStack/molecule/commit/9fdc1d93cdceade55723d64822f0746caa06f1cb))
-   extract color styles to theme.scss ([6dd927f](https://github.com/DTStack/molecule/commit/6dd927fa34ca3ec943d6b0f8cf65f96f6a5db7ac))
-   extract data to service ([c74adaf](https://github.com/DTStack/molecule/commit/c74adafeef730ad1196420eebc57b74d270389da))
-   finish input interface and UI ([a680aa4](https://github.com/DTStack/molecule/commit/a680aa41d5a825a2fb554863184243fe5704463c))
-   finish TextArea interface and UI and Stories ([052b16f](https://github.com/DTStack/molecule/commit/052b16f530095e66afbb527fbaeab77546e2957c))
-   intergate typings ([a7be36f](https://github.com/DTStack/molecule/commit/a7be36f3d09cae9c4adf2034a7ed7f9e0407fc9b))
-   open file ([ffa5708](https://github.com/DTStack/molecule/commit/ffa57087049831af9fb069e9d423674d35865a51))
-   process empty logic ([ce3afd4](https://github.com/DTStack/molecule/commit/ce3afd4e460ae54a305812c8d64ce059a70275be))
-   refactor service interface ([78f3eba](https://github.com/DTStack/molecule/commit/78f3eba8f85556f14a4fab79f4f6fd2a871b8d81))
-   remove dists ([6aea7ef](https://github.com/DTStack/molecule/commit/6aea7ef687a810ab3cf3407d55ef8870caedf42f))
-   remove fileIcon component and debugger webpack plugin ([52c089f](https://github.com/DTStack/molecule/commit/52c089ff92c707800d2debb11e0220e22c4c3fe4))
-   remove unless code ([af80ee3](https://github.com/DTStack/molecule/commit/af80ee3af4eb7cc34d9acc1d8b2c70add8a06fdf))
-   remove unused NPM packages ([ecd97ec](https://github.com/DTStack/molecule/commit/ecd97ecd3b4c46e2f3c8ef54fff83cde75a91376))
-   resolve conflicts ([a99d131](https://github.com/DTStack/molecule/commit/a99d131923e210993b2ddef9c99b7f4a89304473))
-   some service add ([93bd10e](https://github.com/DTStack/molecule/commit/93bd10efbb326b2d112ae5c473655a07e5c051b8))
-   stories optimzie ([c017b7c](https://github.com/DTStack/molecule/commit/c017b7cd3de047be398feb7cb09e0f9bf501d76a))
-   sync Code ([e46213f](https://github.com/DTStack/molecule/commit/e46213f5c55ec815ebc9dbfccb5a3733eda0fcfe))
-   **classname:** add BEM wrapper functions ([cd12f91](https://github.com/DTStack/molecule/commit/cd12f91e577f207212d6a1d010d521e8b2316770))
-   **contextview:** add onHide, dispose function, and shadowOutline option ([9f527f5](https://github.com/DTStack/molecule/commit/9f527f550b19c6958af9df98d6977673391ca7f8))
-   **eventemitter:** add unsubscribe function ([697c431](https://github.com/DTStack/molecule/commit/697c431ee9e3bede35d965dc7bbd2efeee6e0b32))
-   sync code ([7f5dd53](https://github.com/DTStack/molecule/commit/7f5dd5373fc93c041f9dcb6ff171e55379c64803))
-   sync sourceCode ([304e31a](https://github.com/DTStack/molecule/commit/304e31a582199f33c3d0439766054f9f5d5afcd2))
-   test scriptFile icons ([4232028](https://github.com/DTStack/molecule/commit/4232028d91d212838a213b774323946d4d1e72d5))
-   update actionBar icon ([1c8def7](https://github.com/DTStack/molecule/commit/1c8def7cfa5ce8cb0a7efa90e55608970ba2dfd2))
-   **contextview:** attach mac css class and default append view to workbench ([40f70ea](https://github.com/DTStack/molecule/commit/40f70ea0b29a87bd661519d4aeb86ad3b2b21a2a))
-   **contextview:** attach mac css class and default append view to workbench ([4bd9b7e](https://github.com/DTStack/molecule/commit/4bd9b7ea32004dc4fac49e4693ee2e150e8d8cc7))
-   **dropdown:** add basic component and stories ([f00df97](https://github.com/DTStack/molecule/commit/f00df97f45d4f4bab3a94a860aa4d3d8a829ad2b))
-   **dropdown:** add basic component and stories ([5391d05](https://github.com/DTStack/molecule/commit/5391d05a94e45986fdf69b34203fd98b82120bfb))
-   **dropdown:** add basic component and stories ([77941be](https://github.com/DTStack/molecule/commit/77941be836efc75350bcb42a87b8c51c9496798e))
-   **dropdown:** support the overlay display by different placement ([47dadcd](https://github.com/DTStack/molecule/commit/47dadcdc19fa53afcc0d5c790f6e7d885ba0eaf7))
-   **gitlab:** add mr, issue template ([447b3a1](https://github.com/DTStack/molecule/commit/447b3a1403d0975802b5ec4da53d256187097b1c))
-   **global:** add favicon ([675f803](https://github.com/DTStack/molecule/commit/675f803f5ae787cd8f23eb6b25195d10b72587f9))
-   **global:** add favicon ([9db841a](https://github.com/DTStack/molecule/commit/9db841a99fb4b09d20e79d3cd6f27dbbac041e12))
-   **icon:** add icon component based on codicons ([2f6a071](https://github.com/DTStack/molecule/commit/2f6a071c46408e622e7b5bd00df24deb34438f9d))
-   **icon:** add icon component based on codicons ([664247c](https://github.com/DTStack/molecule/commit/664247c748a098a1cd01cd9cb9eee4b344df4486))
-   **id.ts:** add const ID_APP ([4f2b567](https://github.com/DTStack/molecule/commit/4f2b567a1d53f52830187442e3e1434dc627989b))
-   **list:** add basic List component ([8c3a691](https://github.com/DTStack/molecule/commit/8c3a691bf861ac5c5095d4de105bea1297c6a913))
-   **list:** add basic List component ([6cd3531](https://github.com/DTStack/molecule/commit/6cd35316f0ff72b2b6c578d940a40dc6bcf9b35e))
-   **list:** initial list component ([0c52a1c](https://github.com/DTStack/molecule/commit/0c52a1cdc19c1f3ef2ff17cd2597e6417bfbf2ce))
-   **list:** initial list component ([77d2c43](https://github.com/DTStack/molecule/commit/77d2c432371613ec4001d8ce2424c3669569217c))
-   **menu:** add menu component ([afab6dc](https://github.com/DTStack/molecule/commit/afab6dcbb5c2402c60e970fa67ab6a2893b9367c))
-   **menu:** add menu component ([1fe6831](https://github.com/DTStack/molecule/commit/1fe68310a6c3201e085052819d475ee51a8b6636))
-   **react:** add cloneReactChildren function ([3c38959](https://github.com/DTStack/molecule/commit/3c38959b9d9081e6076debe73af86ac55d562af2))
-   **react:** add cloneReactChildren function ([699f96a](https://github.com/DTStack/molecule/commit/699f96ad4bd36b06948cfed9b0d4e1c93c4072a7))
-   add em2Px function ([c9e291e](https://github.com/DTStack/molecule/commit/c9e291e12b47650dd7ac082c016f1a4a1e995054))
-   getPositionByPlacement ([18c4b75](https://github.com/DTStack/molecule/commit/18c4b75c312584b4c10ced8c83325487e58a211c))
-   **typings:** declare HTMLElementProps ([a32c78c](https://github.com/DTStack/molecule/commit/a32c78c7eacd780de59c4133cdd398a1ad37c659))
-   add dropDown menu to menuBar ([607a46f](https://github.com/DTStack/molecule/commit/607a46f4b6b5ab30fa066ff405f07aa0f4eed648))
-   add findParentByClassName, getEventPosition, triggerEvent, TriggerEvent ([7121416](https://github.com/DTStack/molecule/commit/71214169c7936e07b68f2ed9ad51b5754bca8084))
-   optimize code ([aa772a4](https://github.com/DTStack/molecule/commit/aa772a415079e2320a7a00026e025a0aba2dc9e5))
-   test fileIcon render ([4bd0725](https://github.com/DTStack/molecule/commit/4bd072516ab199b781279728888e7963406670cd))
-   **id.ts:** add const ID_APP ([c80b2b7](https://github.com/DTStack/molecule/commit/c80b2b746458fdd731763be4bc2c16e4eff8fbae))
-   **statusbar:** update status bar controller ([77b43d7](https://github.com/DTStack/molecule/commit/77b43d7de25a56192eeaecc2d0a94e34cab17a19)), closes [#17](https://github.com/DTStack/molecule/issues/17)
-   **typings:** declare HTMLElementProps ([3aed642](https://github.com/DTStack/molecule/commit/3aed6420a2dbf8fd14ba7c61962b25cede9a2689))
-   **utils:** add mergeFunctions ([4c09efa](https://github.com/DTStack/molecule/commit/4c09efa4c7a679d11da434a15d24a4dcb9e83806))
-   activityBar selected ([5de680f](https://github.com/DTStack/molecule/commit/5de680f416de0fe19a523d7d4d3dbce1ba6e411b))
-   add actionBar and samples ([83e5947](https://github.com/DTStack/molecule/commit/83e59472543412034d66969755474d26c23ddb63))
-   add classNames common function ([56886e8](https://github.com/DTStack/molecule/commit/56886e8463d72311389bc48154b677e574a0a601))
-   add common animation.scss ([278e71a](https://github.com/DTStack/molecule/commit/278e71a5b7459a8390933d1c62027231d07ed956))
-   add contextMenu component ([606f73c](https://github.com/DTStack/molecule/commit/606f73cb32a2291f76b802b2f8b2e752e368f442))
-   add contextMenu for activityBar ([6648524](https://github.com/DTStack/molecule/commit/6648524d7e6998a488d01b3b5beb1e90319cda95))
-   add contextView and actionBar examples ([8c5bece](https://github.com/DTStack/molecule/commit/8c5becec4bd5dc0d1e957ff2d467efea8b2574cb))
-   add dom helper module ([bb728c4](https://github.com/DTStack/molecule/commit/bb728c495765732ffcebc67f06dd17526169264c))
-   add draft contextMenu component ([7ad4483](https://github.com/DTStack/molecule/commit/7ad448396bc375baf26ebb2ebec85483fc3f5c5a))
-   add draft contextView component ([4ae6678](https://github.com/DTStack/molecule/commit/4ae6678925f9dd1a4038ee1ef73092bb2efa266c))
-   add editorGroup model ([06a4a22](https://github.com/DTStack/molecule/commit/06a4a223c73febcba40dc86112cfddc1004c2537))
-   add em2Px function ([58c8e3f](https://github.com/DTStack/molecule/commit/58c8e3fc2be4ed8ecf4ee940b5234dff2e239505))
-   add findParentByClassName, getEventPosition, triggerEvent, TriggerEvent ([c42afcf](https://github.com/DTStack/molecule/commit/c42afcfe0ec82fdcb26a5f18ea5707a3ecb04a73))
-   add first version of tabComponent ([f05c506](https://github.com/DTStack/molecule/commit/f05c506ad52cdfe5eb9a511a3918debbc2467c2c))
-   add Menu component ([df20a80](https://github.com/DTStack/molecule/commit/df20a80e2f4bf2da6bc0c89020a4b68221f7f616))
-   add model layer and extract view interfaces to model file ([85eed7e](https://github.com/DTStack/molecule/commit/85eed7edc35ed5e9aae0b7ae76d65f2ae4c947e8))
-   add observable decorator ([e54aa51](https://github.com/DTStack/molecule/commit/e54aa510d69fd1a36bc7cdd7b7183abd05628ab1))
-   add onClose event ([7574845](https://github.com/DTStack/molecule/commit/7574845f9bf394ef1789aa2b904e7bd840c2d6eb))
-   add propsTable component ([8a60f66](https://github.com/DTStack/molecule/commit/8a60f66d376eb453fdbade7f4bbd68647cf47914))
-   add servcie declare ([17a237c](https://github.com/DTStack/molecule/commit/17a237c488062996541e39915f1ee8195290d250))
-   add ToolBar component ([0c35c3b](https://github.com/DTStack/molecule/commit/0c35c3be31b893f06f065899b266b647f67faf2b))
-   common stories style ([0171801](https://github.com/DTStack/molecule/commit/0171801d84ad69768e044b90e613d2bed272400c))
-   compoent ui opti ([e120d46](https://github.com/DTStack/molecule/commit/e120d468b4f970fb4fefb00f85a5288dcb683b3d))
-   declare service interface ([6ccd70c](https://github.com/DTStack/molecule/commit/6ccd70c1a51f731f4ac406f850db342d7012c8a7))
-   extract GlobalEvent abstract class ([20c5dbe](https://github.com/DTStack/molecule/commit/20c5dbe4fb37ce901c845fa9e37df2da7c6a4752))
-   extract react service, and re render by observable model ([4b9582a](https://github.com/DTStack/molecule/commit/4b9582a2398832968f4e7e5d6057a77cac2e9c63))
-   global import animation.css ([33103ac](https://github.com/DTStack/molecule/commit/33103ac27df1f6796738b304115c061f00ffe871))
-   only push item ([1363592](https://github.com/DTStack/molecule/commit/13635926c3bb33568cb3fa2c0c1f6703aef986bf))
-   optimize code ([bd657b9](https://github.com/DTStack/molecule/commit/bd657b9eb68ec9080b970bf59b5690627155d635))
-   tree ui add ([b7d37a9](https://github.com/DTStack/molecule/commit/b7d37a90cb1ce9ab3aa5441dae9fe5074a663c9b))
-   **component:** collapse init ([9b10669](https://github.com/DTStack/molecule/commit/9b106699fbc6fdc613566b9f5cc1694d931a8321))
-   add global items in actiitybar ([cd31e15](https://github.com/DTStack/molecule/commit/cd31e151b5b27d511d9ad2e0c2da890b0c49eb30))
-   add logger ([c31031b](https://github.com/DTStack/molecule/commit/c31031b12c513d0646284babd39e6b43474cee60))
-   add mapping state service ([65d20b2](https://github.com/DTStack/molecule/commit/65d20b25079e63db251dc902a5c799b927955eee))
-   add menuBarProvider ([77f5fcd](https://github.com/DTStack/molecule/commit/77f5fcd2d75be757137643bd8795ba787c9d6ad2))
-   add menuBarService ([600ba8e](https://github.com/DTStack/molecule/commit/600ba8edca9d2f3349b80ee3c30e9a7c119ce484))
-   add sidebar, theme layout and others core modules ([2196711](https://github.com/DTStack/molecule/commit/2196711d0a8f8cdd5ed5d0c2b973e7da5a3f3c0a))
-   add singleton decorator ([b676e55](https://github.com/DTStack/molecule/commit/b676e55a802aa6a9af4c560ae8ae5a4bc9932937))
-   add the main entry file for molecule ([63064c9](https://github.com/DTStack/molecule/commit/63064c96bef2e5b533e4dc69eb38feea2a8f89fb))
-   add the ui extension ([0e23b74](https://github.com/DTStack/molecule/commit/0e23b748acc9a194af84bcc6fea0f783e2464ad3))
-   add welcome page ([d49731e](https://github.com/DTStack/molecule/commit/d49731e8114e82f8d2f1bfcc40813d3713b084f8))
-   construct the workbench struct ([2bbf4a5](https://github.com/DTStack/molecule/commit/2bbf4a5bd9ee6dce96a130beea598bc6ea639dd0))
-   declare molecule service ([9cbec33](https://github.com/DTStack/molecule/commit/9cbec339213d860e4722160e7965c9148b6c395e))
-   declare ui interface ([513ce9c](https://github.com/DTStack/molecule/commit/513ce9c818dba8b970f606330e467bf00bb84bbe))
-   encapsulate monaco-editor as react component ([2452d49](https://github.com/DTStack/molecule/commit/2452d49417faa71147362cc31fc64f8c98618329))
-   extract common ids ([fdfa1df](https://github.com/DTStack/molecule/commit/fdfa1df01ec81965d61b163670df45b6d4beb0d1))
-   init common definitions for molecule ([3a4316f](https://github.com/DTStack/molecule/commit/3a4316feea40de36f1502dc50020a8cc36200b01))
-   init the activity bar of workbench ([558843e](https://github.com/DTStack/molecule/commit/558843e0bacd1eeb4dbcc30e684b7c34d57c1cb6))
-   init the editor of workbench ([6f3dbf3](https://github.com/DTStack/molecule/commit/6f3dbf34f4425328b76864af1967ba376fb2e38d))
-   init the menu bar of workbench ([db03bc5](https://github.com/DTStack/molecule/commit/db03bc52ddb8b51e819755525c0ab01202781835))
-   init the panel of workbench ([97eccfe](https://github.com/DTStack/molecule/commit/97eccfee2d2a6211997b894ec2d4c401f9f264ce))
-   init the settings of workbench ([2ff9c9e](https://github.com/DTStack/molecule/commit/2ff9c9e8d3581496620adb4d720e3de01625a005))
-   init the sidebar of workbench ([403ef36](https://github.com/DTStack/molecule/commit/403ef36bccada54835d913d7d6d1f84c9a4b5897))
-   init the status bar of workbench ([abcae7e](https://github.com/DTStack/molecule/commit/abcae7e1ca7b2f623d810f2968150320acc541d4))
-   init workbench ([7446677](https://github.com/DTStack/molecule/commit/74466774f9d6faa8e0daf7f88c4e6052ec2e9525))
-   logger moleculeCtx, its convenient for development ([25db6cb](https://github.com/DTStack/molecule/commit/25db6cbfe18a8518ab18db36a2e127f2928ad105))
-   provide abstract class baseService ([5590dca](https://github.com/DTStack/molecule/commit/5590dca71ec48d89a974218091a6cca9f205dc08))
-   provide the eventService class for event management ([d5708b5](https://github.com/DTStack/molecule/commit/d5708b5fc037d4ad547d05f896fc428b2d9e5099))
-   **utils:** add cloneInstance function ([03f66a5](https://github.com/DTStack/molecule/commit/03f66a594871324634121b433f6b916d1cf502eb))
-   some default extensions ([bbc891e](https://github.com/DTStack/molecule/commit/bbc891e7fa3b8e740ca76b77e5c545261c7caa1c))
-   styled editor breadcrumbs ([ca96140](https://github.com/DTStack/molecule/commit/ca96140337e948a95ca6f20ef6fcafa71e6d3e08))
-   support multiple subscribe for the same event ([84ce57a](https://github.com/DTStack/molecule/commit/84ce57a27b6b5bc20831b43fd6eff4f887d7d2fe))
-   the parameter of prefixClaName supports Symbol ([8b258de](https://github.com/DTStack/molecule/commit/8b258deab138ec680b646706a6347af488769aed))

### Bug Fixes

-   add animation for expand the collapse ([17995c9](https://github.com/DTStack/molecule/commit/17995c9befa8a97a83c0a1461cb5d4f80fe72598))
-   add default generic type ([4fc75cf](https://github.com/DTStack/molecule/commit/4fc75cfc9f8e14c37a8cc03f78d128c8944b4322))
-   add lib gitignore ([6d97e5a](https://github.com/DTStack/molecule/commit/6d97e5a653c1a77350d95bcae8bf94a7e0c04578))
-   add the built-in addons for the searchPane ([#193](https://github.com/DTStack/molecule/issues/193)) ([793876d](https://github.com/DTStack/molecule/commit/793876da18709ab9bec348382962f3002621b01d))
-   add the keyword type when exporting the interface or type object ([b3966f9](https://github.com/DTStack/molecule/commit/b3966f99e3c76e91740c38750e536d4bea4b8fe3))
-   adjust the margin of Sidebar search control ([ad5319f](https://github.com/DTStack/molecule/commit/ad5319ffaf93a03e8045ad22dbe0931db61452bd))
-   alter the type of Connector props and status to any ([53fc63e](https://github.com/DTStack/molecule/commit/53fc63e5987d964a2d85feb5df4a6da4c0a0c0f0))
-   change file name ([9cb0760](https://github.com/DTStack/molecule/commit/9cb0760d9469e6226691f4023ab099311cf519f9))
-   code Editor content is wrong when EditorTabs are changed ([#160](https://github.com/DTStack/molecule/issues/160)) ([2b05642](https://github.com/DTStack/molecule/commit/2b056424ebb8f8c25414c8e5df5ee8c960f994f5))
-   code format premitter ([de3d81a](https://github.com/DTStack/molecule/commit/de3d81a87d42d11915bd668261a5abaca9710674))
-   currentTab of the currentGroup is selected by default when add EditorGroup ([1353943](https://github.com/DTStack/molecule/commit/135394389ec19b05d52d8cdc8484afe6db11abb4))
-   default regsiter the SettingsController singleton ([#204](https://github.com/DTStack/molecule/issues/204)) ([442b1a9](https://github.com/DTStack/molecule/commit/442b1a9d605c20e127e954f29df13031ea8e4465))
-   delete some test code ([05098f3](https://github.com/DTStack/molecule/commit/05098f34827c2ee3aede0bb09dff118c37bc090a))
-   disable update the tab when onUpdateTab ([aa25528](https://github.com/DTStack/molecule/commit/aa25528d059410b9888c00ce54a54c5f3c523646))
-   editor.executeEdits that pass in the desired end selection for the editor on which you invoke ([#137](https://github.com/DTStack/molecule/issues/137)) ([a2d3d27](https://github.com/DTStack/molecule/commit/a2d3d2737f98d97c8f21ebbf50b38460e66358ea))
-   fix ci problems ([d413fb5](https://github.com/DTStack/molecule/commit/d413fb5dd2287b8d2fab751b6e57d5e7d66ecaea))
-   fix ci type validate error ([ce77de5](https://github.com/DTStack/molecule/commit/ce77de5a44cf580abeb0b07d09fc6b46af7487a4))
-   fix cycle file, extract connect to controller ([#126](https://github.com/DTStack/molecule/issues/126)) ([69bbb75](https://github.com/DTStack/molecule/commit/69bbb753afc3210306b849c68eb458af78ac5d1c))
-   fix editor tabs cannot scroll ([#219](https://github.com/DTStack/molecule/issues/219)) ([3b8db4c](https://github.com/DTStack/molecule/commit/3b8db4c908d1efe027acbe8fac5214f39eae85eb))
-   fix function params ([cd6fa5b](https://github.com/DTStack/molecule/commit/cd6fa5ba60cb6a83c9e54e96fe4d68b5ab224ab4))
-   fix removeFolder exec error and delete modal style ([#81](https://github.com/DTStack/molecule/issues/81)) ([c664353](https://github.com/DTStack/molecule/commit/c6643536a276d09f3300048fab6b456595d0a296))
-   format code prettier ([ec63d9b](https://github.com/DTStack/molecule/commit/ec63d9bcf1aa360a9132055e03061d59535b7e1a))
-   improve active style and prevent the show of native contextMenu ([#225](https://github.com/DTStack/molecule/issues/225)) ([70f3804](https://github.com/DTStack/molecule/commit/70f3804f01435c0d40a7ff79b40ea94aaf4d5311))
-   improve activity context menu behavior ([#206](https://github.com/DTStack/molecule/issues/206)) ([20c191d](https://github.com/DTStack/molecule/commit/20c191d2480d969060c0a86cfe46999e41ceaeb2))
-   improve dropdown get relative position ([#222](https://github.com/DTStack/molecule/issues/222)) ([e46c5de](https://github.com/DTStack/molecule/commit/e46c5debd8fd82f2b74ad0ce0d8f4c78c52e830b))
-   improve editor content change ([a9d2bcf](https://github.com/DTStack/molecule/commit/a9d2bcfef6ed9a0321722c99761a7d865c9f6aca))
-   improve editor tabs change ([c1c34e2](https://github.com/DTStack/molecule/commit/c1c34e2c69a1510dc9b62f5219639da407ee4861))
-   improve global contextMenu in activity bar ([#200](https://github.com/DTStack/molecule/issues/200)) ([7ad58ab](https://github.com/DTStack/molecule/commit/7ad58abbe460e8c17404f2c81a64a4eec1d431aa))
-   improve workbench height & box-sizing ([#223](https://github.com/DTStack/molecule/issues/223)) ([9f25dd5](https://github.com/DTStack/molecule/commit/9f25dd57de2ccdbaf40042a35cf5236f8747d54e))
-   location render incorrect after editing ([d0877f0](https://github.com/DTStack/molecule/commit/d0877f004f1bbac7f424745241f6a39f3a6025f7))
-   manual pass Controller to View ([03791e7](https://github.com/DTStack/molecule/commit/03791e752585459c9add25cb63349c59796b459f))
-   premitter code format ([0259a15](https://github.com/DTStack/molecule/commit/0259a15f4405635dcde3534c2df6c5ad19de939c))
-   prettier code format ([ea609ea](https://github.com/DTStack/molecule/commit/ea609eae6bd26406bee71ab5684fa3a29c8433dd))
-   prettier format code ([4b14844](https://github.com/DTStack/molecule/commit/4b14844b5fcf4b441260ca807f5d3b3d1073ef39))
-   remove useless package-lock.json ([2f391f1](https://github.com/DTStack/molecule/commit/2f391f15c53013771bb569842ea6c6c3fe778d2e))
-   the editor content incorrect when changing tabs ([#210](https://github.com/DTStack/molecule/issues/210)) ([7d59c07](https://github.com/DTStack/molecule/commit/7d59c07e3d8eb8b2e56bccfc41c9222647271d82))
-   the value still in when file reopen ([#228](https://github.com/DTStack/molecule/issues/228)) ([2b9002b](https://github.com/DTStack/molecule/commit/2b9002b72cf608b56b52d1d84ea2ce31123b709b))
-   **actionbar:** merge onClick event ([f5c24b3](https://github.com/DTStack/molecule/commit/f5c24b3a1e6ff0981616dee0118de9ea4152db60))
-   **build:** replace the path named mo with relative path ([8146eee](https://github.com/DTStack/molecule/commit/8146eeede60a8436aafb679cd979aea7ec9c909b))
-   **ci:** fix ci error ([6c80a4f](https://github.com/DTStack/molecule/commit/6c80a4f867c2568ddd4617da1871d93dd6e5896c))
-   **collapse:** fix calc content height failed because of height: 100% ([#194](https://github.com/DTStack/molecule/issues/194)) ([f6a6ba6](https://github.com/DTStack/molecule/commit/f6a6ba649969b68a0adafc7c37049801f8e5f362))
-   **collapse:** improve the timing to detect panel content whether empty ([#184](https://github.com/DTStack/molecule/issues/184)) ([eb70c6d](https://github.com/DTStack/molecule/commit/eb70c6da486802c00b545b090b1b409753ce9b5e))
-   **collapse:** open editor not expand expected ([#196](https://github.com/DTStack/molecule/issues/196)) ([2af530f](https://github.com/DTStack/molecule/commit/2af530f27abac057581c3cf46a4b10e8c0a433a5))
-   **dialog:** remove the style.scss import ([0a420fb](https://github.com/DTStack/molecule/commit/0a420fbb19270ca9a9539d383a86f174bbb05013))
-   **editor:** fix report error message when close tab in editor ([#207](https://github.com/DTStack/molecule/issues/207)) ([32daee2](https://github.com/DTStack/molecule/commit/32daee232258ae1f1aa6530c1acb529c3dc222b7))
-   **folderTree:** improve add root folder & delete dialog ([#192](https://github.com/DTStack/molecule/issues/192)) ([c49e534](https://github.com/DTStack/molecule/commit/c49e534139ffa4c3f48607e15a020709b9a47bf4))
-   **global:** set the color of Tag a as inherit ([#216](https://github.com/DTStack/molecule/issues/216)) ([54cc07c](https://github.com/DTStack/molecule/commit/54cc07c80ebaa83819c4d932e94fa5d66c4e7fc3)), closes [#187](https://github.com/DTStack/molecule/issues/187)
-   **menu:** improve menu interactive behavior ([#199](https://github.com/DTStack/molecule/issues/199)) ([a8ba3e3](https://github.com/DTStack/molecule/commit/a8ba3e3049e91719bc8fe63d128f153fb54a9fcf))
-   mock editor.create method ([abe61d6](https://github.com/DTStack/molecule/commit/abe61d6427ff9bed7f902e009320ba2f529b94f7))
-   remove folderEvent from editor controller ([4015f58](https://github.com/DTStack/molecule/commit/4015f5811a27a816c6a48845afc447eeff719d4e))
-   remove the updateTab action ([a95e4db](https://github.com/DTStack/molecule/commit/a95e4db789b9962b2a12bddfac597e1a2931d83c))
-   remove the useless injectable decorator ([ec61d0f](https://github.com/DTStack/molecule/commit/ec61d0fbe9f6a117368c4aabecf1cca8a6072598))
-   set the outline as 0 for the root element ([#177](https://github.com/DTStack/molecule/issues/177)) ([d2e2c6d](https://github.com/DTStack/molecule/commit/d2e2c6d48effe56240b4c22136b8805a216aeca7))
-   update the color theme ([58df79b](https://github.com/DTStack/molecule/commit/58df79b8c519b9ab4e415f4cc6acb0ecf95e64d8))
-   **eventbus:** expand the arguments of emit ([94020b8](https://github.com/DTStack/molecule/commit/94020b88ecb8ce991052f6e72da4bef6785f9c03))
-   **logger:** expand the arguments of function ([6732faa](https://github.com/DTStack/molecule/commit/6732faaa3fe44635ec1f641ce7db50edadc0ddc8))
-   **menu:** provides onClick API ([f325ac4](https://github.com/DTStack/molecule/commit/f325ac4561a1cc53daf58a85ee2bc9f2ba05c2ee)), closes [#87](https://github.com/DTStack/molecule/issues/87)
-   **monaco:** import default language contributions ([6d0fdbc](https://github.com/DTStack/molecule/commit/6d0fdbc1a6382e926053bea1f48be94f388b93ac))
-   **output:** disable the editor minimap ([5330ece](https://github.com/DTStack/molecule/commit/5330eced3f5ef937726ca8b664d7da9948607642)), closes [#62](https://github.com/DTStack/molecule/issues/62)
-   **output:** disable the minimap and fix output can't refresh problem ([#124](https://github.com/DTStack/molecule/issues/124)) ([5a9dba2](https://github.com/DTStack/molecule/commit/5a9dba2d4e10b9979bde901edf5a8e225a8ec703)), closes [#123](https://github.com/DTStack/molecule/issues/123)
-   **slipt-pane:** import Pane module directly ([e6b7b70](https://github.com/DTStack/molecule/commit/e6b7b70c85f4d6224ff5ad8cc6d9e68c8faacb35))
-   **stylelint:** ignore esm and umd folder ([6a175af](https://github.com/DTStack/molecule/commit/6a175af735702e3d87f0b8c44b7b257af3386fac))
-   **theme:** improve the colorTheme detail ([cee788d](https://github.com/DTStack/molecule/commit/cee788d1905e0294afd450b56ef60877300d0e10)), closes [#20](https://github.com/DTStack/molecule/issues/20)
-   **toolbar:** remove the toolbar style.scss import ([58c8884](https://github.com/DTStack/molecule/commit/58c88843a3c007a3a648e000b76cd93ff9c7f9b6)), closes [#131](https://github.com/DTStack/molecule/issues/131)
-   activitybar items overflow ([21f8f7a](https://github.com/DTStack/molecule/commit/21f8f7a6e25bf82de9513ef0fd0cd7e2d3fc0590))
-   add key prop for each subMenu ([f36f09e](https://github.com/DTStack/molecule/commit/f36f09ecca13686bd643cae316bdddc270ae0ee7))
-   check-types fix ([09a57d6](https://github.com/DTStack/molecule/commit/09a57d657feb1d125120dfe8fc2e58209cb9c747))
-   code format ([adea9b0](https://github.com/DTStack/molecule/commit/adea9b0df72679c48bd1c49431de8f08a537f349))
-   contextmenu trigger fix ([8432c46](https://github.com/DTStack/molecule/commit/8432c46e12b433a9183aaed7cea772be04db1d78))
-   del mock data ([c27d346](https://github.com/DTStack/molecule/commit/c27d3464f9ed8d51fa8ca978f8a735df2f156045))
-   del npmrc file ([454f8c6](https://github.com/DTStack/molecule/commit/454f8c638d8e25e367233f9d25ac128171401bbb))
-   delcare ActivityBarEvent in model ([dded331](https://github.com/DTStack/molecule/commit/dded33133818800cd8606fc365349180636b61b8))
-   derive the arguments of callback function ([bccae6e](https://github.com/DTStack/molecule/commit/bccae6e46ced96f566e48a0c5ae909b4d2d61cb0))
-   derive the arguments of callback function ([cdf8388](https://github.com/DTStack/molecule/commit/cdf83889bb4d386e9f510568b763ce9f11c718fb))
-   disable no-invalid-this eslint rule ([a841f8b](https://github.com/DTStack/molecule/commit/a841f8b7a4896d09ce9cc0bf75148c0b41a1b60d))
-   disable no-invalid-this eslint rule ([86f536e](https://github.com/DTStack/molecule/commit/86f536e8fab836feff9f1f03a39d59320710b305))
-   export \* from menuItem ([2b76400](https://github.com/DTStack/molecule/commit/2b764004f4cb9d5ad1b0b375800a7a5f0029aea1))
-   fix content ([b79bd40](https://github.com/DTStack/molecule/commit/b79bd402646bfdf84de2641e81d46e94a82fc2f9))
-   fix fileType ([5d76b32](https://github.com/DTStack/molecule/commit/5d76b32d9934082da597b704b12daf74cf59244a))
-   fix focus ([3816eb5](https://github.com/DTStack/molecule/commit/3816eb5386682120246b18169c9bb27a49ec1f9d))
-   fix lint ([b67a74a](https://github.com/DTStack/molecule/commit/b67a74aa4c2f0cc43d867dc0868e22e7c4d9f4f2))
-   fix yarnrc and some todo ([8658641](https://github.com/DTStack/molecule/commit/86586410eac1beff091c56113cf36407758f3667))
-   merge tabs and collapse ([c3e1451](https://github.com/DTStack/molecule/commit/c3e1451117ac896e3fd559725cda4efb9f98c334))
-   prettier format fix ([1a67fff](https://github.com/DTStack/molecule/commit/1a67fff547816c846f0aa8ce8647c90ad2a74f4c))
-   rename data backfill ([0b0742d](https://github.com/DTStack/molecule/commit/0b0742d73d5656b4d661de77e8a0b2ba4e5f5a57))
-   ts error ([456168c](https://github.com/DTStack/molecule/commit/456168c0c6d657498c581b2089c7c7507bf6bf59))
-   ts types error fix ([e2fb056](https://github.com/DTStack/molecule/commit/e2fb056b8f378437b37ce5f44711c66f5ceb8ec2))
-   typing error ([ebca2f0](https://github.com/DTStack/molecule/commit/ebca2f05808f1b967069267e99b19d419f6eacc1))
-   **conflict:** merge Button and List ([7c6df2a](https://github.com/DTStack/molecule/commit/7c6df2a40660d89b4204d55228b737c3f6377180))
-   **event emitter:** extend the event origianl function arguments ([30c6f51](https://github.com/DTStack/molecule/commit/30c6f5176be2d4edd46af8fa32ad8a8870100af8))
-   **logger:** expand the arguments of function ([63642ea](https://github.com/DTStack/molecule/commit/63642ea74cb2757337234311a9674ff56ea0144e))
-   **tabs:** add dragAndDrop HOC ([e94acc1](https://github.com/DTStack/molecule/commit/e94acc17a793a1ce4d3dbc26b6f16b6a36997051))
-   fix validate ([6b7887e](https://github.com/DTStack/molecule/commit/6b7887e87d705c82d34420542e43df6ac10c63c7))
-   prettier format ([ed3065d](https://github.com/DTStack/molecule/commit/ed3065de2fba0e295cfec067e3cd355a63fdff44))
-   **stylelint:** prettify and ignore markdownlint extension ([23a9966](https://github.com/DTStack/molecule/commit/23a9966479c6028517056d53394b02e6ba30a8e8))
-   **sync:** fix conflict ([ad7bd67](https://github.com/DTStack/molecule/commit/ad7bd670b8752dbc139790ea73038030ff7d558f))
-   **ts:** typing errors ([2b1d115](https://github.com/DTStack/molecule/commit/2b1d115d05c26e7339e012198d4d0ee42c74a432))
-   fix replace classNames use local method ([2110081](https://github.com/DTStack/molecule/commit/211008192e4513623172aff67c5d6b7fb76f7f8f))
-   merge tabs and collapse ([b5eb235](https://github.com/DTStack/molecule/commit/b5eb2352fa4dd793b66b48540c4dcb5789e3bb0b))
-   merge tabs and collapse ([e479848](https://github.com/DTStack/molecule/commit/e479848770241ea6b78a5ad4bd197029787d1720))
-   rebase dev ([ca91fce](https://github.com/DTStack/molecule/commit/ca91fcedf78ff5732778f9dddf50aa0b37bd5de7))
-   rebase dev ([f6099e9](https://github.com/DTStack/molecule/commit/f6099e91891ba8f6b68e35b30881a9fe11fc8ca7))
-   remove illegal charactor for json file ([e8bdb74](https://github.com/DTStack/molecule/commit/e8bdb747e39a0b879c1cc4128cd5f1144e1c8c00))
-   remove the Symbol parameter type ([db86ef2](https://github.com/DTStack/molecule/commit/db86ef23cc06b1d794100ecd098c125523ca8923))
-   remove useless option and fix the Select value logic ([f3154f1](https://github.com/DTStack/molecule/commit/f3154f1c9227af32cce6de29805cb83a815de36a))
-   stylelint ([6d2dbeb](https://github.com/DTStack/molecule/commit/6d2dbebdebf5f0862a502d271ab7835b5993ee7f))
-   sync code ([a13551e](https://github.com/DTStack/molecule/commit/a13551e7848659c980cd6e6b3fd9749cb235630a))
-   typing error ([a144947](https://github.com/DTStack/molecule/commit/a144947dbdd49f9e831211da5e0bce4fd9a9afdd))
-   typing error ([8891b52](https://github.com/DTStack/molecule/commit/8891b525f16b736de81cf0d92544f6ff225ba86d))
-   **theme:** mixin-pattern error ([1f82d95](https://github.com/DTStack/molecule/commit/1f82d95521689651124ffb7085253ec7117b3058))
-   **workbench:** cache the workbench SplitPane position ([#76](https://github.com/DTStack/molecule/issues/76)) ([2af86cd](https://github.com/DTStack/molecule/commit/2af86cdecef955b3713fad43f373852698d8e6b9)), closes [#60](https://github.com/DTStack/molecule/issues/60)
-   unexpect none as border-top value ([1eff91a](https://github.com/DTStack/molecule/commit/****************************************))

-   **api:** refactor the molecule API and interfaces ([fa01143](https://github.com/DTStack/molecule/commit/fa0114304c9d511775131e0d1b0d36f8900a7f36)), closes [#143](https://github.com/DTStack/molecule/issues/143) [#153](https://github.com/DTStack/molecule/issues/153)

## [0.9.0-alpha.2.1](https://github.com/DTStack/molecule/compare/v0.9.0-alpha.2...v0.9.0-alpha.2.1) (2021-05-26)

### ⚠ BREAKING CHANGES

-   **api:** different invoke methods based on molecule API

### Features

-   access the color themes globally ([1a627d5](https://github.com/DTStack/molecule/commit/1a627d5c8ae7e92f127be760254992b419f9d945))
-   access the CommandPalette globally ([f37989b](https://github.com/DTStack/molecule/commit/f37989bef65bfa325d1b23102622f87e8975acd4))
-   add checked color for activityBar item ([e09f07d](https://github.com/DTStack/molecule/commit/e09f07df8bd52a6c5f24ef4bd9ab898a80905728))
-   add CommandQuickAccessProvider and CommandQuickAccessViewAction ([5a42e22](https://github.com/DTStack/molecule/commit/5a42e223af8c7db377d86b2093bd44bba178c293))
-   add the quickAccess for the Settings ([458a4ca](https://github.com/DTStack/molecule/commit/458a4ca067acaee6d6ec54f806390cea2e0e6df2))
-   alter the Workbench tabIndex default to 0 ([f1c278b](https://github.com/DTStack/molecule/commit/f1c278bdec5cb8088cd18322a8ad62ba6c15cbc6))
-   encapsulate Action2 for workbench ([39d9e07](https://github.com/DTStack/molecule/commit/39d9e07f593501dc9a39ce9c4f9d0b93aeb62e15))
-   encapsulate common workspace Actions API based on moanco api ([1d49058](https://github.com/DTStack/molecule/commit/1d4905853d9e788348c3c527192e434edeb6805e))
-   encapsulate the quickAccessProvider ([223ea3c](https://github.com/DTStack/molecule/commit/223ea3cf163452fc1100ef0b6e48cff9f3946fdc))
-   remove the default active animation ([b3ee198](https://github.com/DTStack/molecule/commit/b3ee1980e4746ac8530cc1816da7a6ddc2f21df9))
-   support show or hide statusBar when rightClik statusBar panel ([#154](https://github.com/DTStack/molecule/issues/154)) ([b26c532](https://github.com/DTStack/molecule/commit/b26c532a9529da4802e91b57b56df9c0c154f441))
-   update builtin color theme ([d02f552](https://github.com/DTStack/molecule/commit/d02f5527e81c5c2d605b946836bd8645caba889a))
-   **notification:** modify styles for notification panel ([8d20149](https://github.com/DTStack/molecule/commit/8d20149955142c4717962463ad02bf862f1656e2))
-   **panel service:** modify panel service test code ([c808b12](https://github.com/DTStack/molecule/commit/c808b12cccb319926790d531b4c82105a7554ea8))
-   **problems and notification:** modify problems and notification styles and rename panel ([7c1a08d](https://github.com/DTStack/molecule/commit/7c1a08d31b30f53e5411aed1d6fbc890c39353f8))
-   **problems and notification:** update problems and notification code ([aa6b32d](https://github.com/DTStack/molecule/commit/aa6b32d6018e591a49062c3b8ced25506045aaa3)), closes [#103](https://github.com/DTStack/molecule/issues/103)
-   **tsconfig:** modify tsconfig for yarn build esm ([6c9e308](https://github.com/DTStack/molecule/commit/6c9e3086ead35c749373c5873a57cd94fae3bf0a))

### Bug Fixes

-   add animation for expand the collapse ([17995c9](https://github.com/DTStack/molecule/commit/17995c9befa8a97a83c0a1461cb5d4f80fe72598))
-   add the keyword type when exporting the interface or type object ([b3966f9](https://github.com/DTStack/molecule/commit/b3966f99e3c76e91740c38750e536d4bea4b8fe3))
-   adjust the margin of Sidebar search control ([ad5319f](https://github.com/DTStack/molecule/commit/ad5319ffaf93a03e8045ad22dbe0931db61452bd))
-   disable update the tab when onUpdateTab ([aa25528](https://github.com/DTStack/molecule/commit/aa25528d059410b9888c00ce54a54c5f3c523646))
-   remove the updateTab action ([a95e4db](https://github.com/DTStack/molecule/commit/a95e4db789b9962b2a12bddfac597e1a2931d83c))
-   remove the useless injectable decorator ([ec61d0f](https://github.com/DTStack/molecule/commit/ec61d0fbe9f6a117368c4aabecf1cca8a6072598))
-   update the color theme ([58df79b](https://github.com/DTStack/molecule/commit/58df79b8c519b9ab4e415f4cc6acb0ecf95e64d8))
-   **monaco:** import default language contributions ([6d0fdbc](https://github.com/DTStack/molecule/commit/6d0fdbc1a6382e926053bea1f48be94f388b93ac))

-   **api:** refactor the molecule API and interfaces ([fa01143](https://github.com/DTStack/molecule/commit/fa0114304c9d511775131e0d1b0d36f8900a7f36)), closes [#143](https://github.com/DTStack/molecule/issues/143) [#153](https://github.com/DTStack/molecule/issues/153)

## 0.9.0-alpha.2 (2021-05-06)

### Features

-   **editor:** custom the TabPane renderer ([b65d221](https://github.com/DTStack/molecule/commit/b65d221bd726853b17297096eb293e496482dbd2))
-   **monaco:** add json language ([dd4846c](https://github.com/DTStack/molecule/commit/dd4846c75a51b6f18f1e2c9d0c55ea72b91fc3c8))
-   **settings:** add basic Settings feature ([8bceabd](https://github.com/DTStack/molecule/commit/8bceabdfb1734f7d35c797c4362e5ec18a867707)), closes [#104](https://github.com/DTStack/molecule/issues/104)
-   **utils:** add flatObject and normalizeFlattedObject functions ([cb93d7c](https://github.com/DTStack/molecule/commit/cb93d7c9f67100f8045138da4944755ae1931a3d))
-   add addConextMenu removeContextMenu activityServices ([10411e3](https://github.com/DTStack/molecule/commit/10411e338e664bacde0f559b4e8dc5ad3623526c))
-   add colorRegistery ([ff55f06](https://github.com/DTStack/molecule/commit/ff55f06f3e7886588cd1c9efd93ecbac8ef53097))
-   add colorRegistery ([84f5780](https://github.com/DTStack/molecule/commit/84f57804064d885f538a8d950bb3be5aa98b62b1))
-   add onTabChange and onToolbarClick interfaces in panelService ([6cab8f0](https://github.com/DTStack/molecule/commit/6cab8f05f68e6851167f5e38e775cead16c54bbd)), closes [#45](https://github.com/DTStack/molecule/issues/45)
-   add testing sidebar panel ([d9b9fdc](https://github.com/DTStack/molecule/commit/d9b9fdc0f526a1e8af6820746aada7a8415da32f))
-   code format premitter ([519a651](https://github.com/DTStack/molecule/commit/519a65162b2801857dc6eb6cfd55a3548d844095))
-   connect state and view by connect method ([fc9188f](https://github.com/DTStack/molecule/commit/fc9188fb1c61d73d64a313dd4c3687604aa6fa09))
-   define contextMenu command id ([b08f56f](https://github.com/DTStack/molecule/commit/b08f56f280d90d846aa0b62eb4385a9ede9f5235))
-   display the editor line and column info on statusBar ([f502adb](https://github.com/DTStack/molecule/commit/f502adb35b87af0dc33adde8fca9b256bf11aa72))
-   editorService support subscribe onMoveTab, onSelectTab, onCloseAll, onCloseTab, onCloseOth etc ([#140](https://github.com/DTStack/molecule/issues/140)) ([48c8984](https://github.com/DTStack/molecule/commit/48c898469e84538b93730389a93fb7103cb37f53))
-   explorer headerToolBar feats add ([c3ac8af](https://github.com/DTStack/molecule/commit/c3ac8af266ec9eeed080a69a609be2427d88e6e3))
-   explorer service add ([9963180](https://github.com/DTStack/molecule/commit/9963180e4007f5182ebf4f305e505ef79e54d864))
-   export the shadowClassName of contextView ([7875f19](https://github.com/DTStack/molecule/commit/7875f19107ba0c63fc81fd62c86fbcefe2cca82e))
-   extract editor modified logic to extensions ([#138](https://github.com/DTStack/molecule/issues/138)) ([70aafcf](https://github.com/DTStack/molecule/commit/70aafcf30ccb9af536babdb5fbb7c5961050de68))
-   extract ITabProps interface to tabComponent ([3645f46](https://github.com/DTStack/molecule/commit/3645f466bb4cd05647140e1eb16c780bb0f7e3d3))
-   extract logic to extensions ([cd47341](https://github.com/DTStack/molecule/commit/cd473417a31870132bee8add434ebb2e0631b545))
-   extract overside tabDataInterface ([c7a6857](https://github.com/DTStack/molecule/commit/c7a6857be5f5a02812b1913b365dbfb645a1bcc1))
-   extract TreeViewUtil to help file ([883de0f](https://github.com/DTStack/molecule/commit/883de0fba6241c1378a399608cd251cc710149ab))
-   import mo.scss manually ([1d4fb98](https://github.com/DTStack/molecule/commit/1d4fb9878538f7c30cc263c97fd31b7f887b98dd))
-   listen sample folder contextmenu event ([46343fd](https://github.com/DTStack/molecule/commit/46343fd9745ef862266f6b039858e9e21816f52b))
-   optimize dataType interface name ([7023127](https://github.com/DTStack/molecule/commit/7023127dcc040fc8a511447107f961ceeb6a681e))
-   optimize some named ([315d520](https://github.com/DTStack/molecule/commit/315d520718ad84d17e0bb0d40c2825cce0c0dda8))
-   optimzie activityBar onContextMenuClick method ([60056ad](https://github.com/DTStack/molecule/commit/60056ade362debcfa3d4de679a496a1a64bdd3ac))
-   optmize stories ([c2bf6b0](https://github.com/DTStack/molecule/commit/c2bf6b0efedee73e5c5dff45efe8d069d6287cc1))
-   prettier validate format ([3c6ffe4](https://github.com/DTStack/molecule/commit/3c6ffe4ab1392d5424510c5ba756430f4da8d6e2))
-   resolve [#37](https://github.com/DTStack/molecule/issues/37) ([3597665](https://github.com/DTStack/molecule/commit/35976653f588aee432e410af30c1a0bbb6c0b1e7))
-   resovle [#37](https://github.com/DTStack/molecule/issues/37) ([dd2577d](https://github.com/DTStack/molecule/commit/dd2577d4ec2f5ffbab491c2d255ea29d6ae9ea27))
-   support show or hide menuBar search and explorer ([0618d12](https://github.com/DTStack/molecule/commit/0618d12f6cd2aa1fd89efbd7fbdf9792ae05337b))
-   sync main code ([381f11a](https://github.com/DTStack/molecule/commit/381f11a066360ec6090cd6da45284df7c2dbb3fe))
-   **components:** add Breadcrumb ([70d6dd5](https://github.com/DTStack/molecule/commit/70d6dd58f763376669db663143a6e6780f878d59))
-   **editor:** add split window and some features for editor ([3bf957b](https://github.com/DTStack/molecule/commit/3bf957b079f228476d0c4560d4bb55af2cd8539e))
-   **icon:** add onClick prop in Icon ([b9fed09](https://github.com/DTStack/molecule/commit/b9fed0966566fc0d6d59d5f1a591b77fafffc811))
-   **notification:** add simple Notification module ([a99d855](https://github.com/DTStack/molecule/commit/a99d855ff5e05d14ba60bd50d0bdbd4c88a64fdc)), closes [#18](https://github.com/DTStack/molecule/issues/18)
-   **panel:** add intitial service, model and controller ([2c4eac3](https://github.com/DTStack/molecule/commit/2c4eac318c0dd87ba81b5de22a38107c7a0d0500))
-   **panel:** support add, remove and update Panel features, builtin OUTPUT and PROBLEMS panel ([909bcc8](https://github.com/DTStack/molecule/commit/909bcc840b2a8ee2883ea0e3ed5e2dd41e126703)), closes [#19](https://github.com/DTStack/molecule/issues/19) [#22](https://github.com/DTStack/molecule/issues/22)
-   **react:** add abstract class Controller ([a55dc0f](https://github.com/DTStack/molecule/commit/a55dc0f4a2e0106dbfa681ba9340afa277a1601a))
-   **react:** add Connector for service, view and controller ([88dcc80](https://github.com/DTStack/molecule/commit/88dcc80304b896e197eac00f2dc7b49a00d41f4f))
-   **settings:** add intitial service and model ([57597f1](https://github.com/DTStack/molecule/commit/57597f1572d990c5354b09e2036c04e508543bd1))
-   **status bar:** tuning the status bar component ([5db28ec](https://github.com/DTStack/molecule/commit/5db28ec3ab7d96ed9bf9be42b3356552329c5d17))
-   **status/bar:** modify status bar item ([cc14adf](https://github.com/DTStack/molecule/commit/cc14adfb656fc78a12cd592f1ce651784da89140))
-   **statusbar:** update status bar controller ([77b43d7](https://github.com/DTStack/molecule/commit/77b43d7de25a56192eeaecc2d0a94e34cab17a19)), closes [#17](https://github.com/DTStack/molecule/issues/17)
-   extract randomId ([459d867](https://github.com/DTStack/molecule/commit/459d867c4be348b048534c3c7216f8589cc9e8ab))
-   folder service test ([4d13957](https://github.com/DTStack/molecule/commit/4d139578c0a0ad5b2aa6a32142b7c81e12447755))
-   optimize fileType definition ([768f176](https://github.com/DTStack/molecule/commit/768f17633c9988fb44190abcd21101981309d40d))
-   optimize folderPanel contextMenu logic and extract style to mo ([d1191a5](https://github.com/DTStack/molecule/commit/d1191a57ec155b6cba33bdbd7de926878352c6a6))
-   prettier format code ([768885c](https://github.com/DTStack/molecule/commit/768885c385d06b6c149c64c7e41ccfd62dfaaf18))
-   **panel:** support show or hide, and maximize or restore the Panel ([a3c8d85](https://github.com/DTStack/molecule/commit/a3c8d855680b289f01a45b2e5228cc19335ca002)), closes [#19](https://github.com/DTStack/molecule/issues/19)
-   update the menu item height to 1.8em ([ac345cc](https://github.com/DTStack/molecule/commit/ac345cce965ffc8b6581615a1e41c24e4cf1e1f9))
-   **theme:** add default palenight theme ([d5382f8](https://github.com/DTStack/molecule/commit/d5382f8afce8b4dc346ec8c0d75b310ce576cdce))
-   **theme:** add default palenight theme ([6df509a](https://github.com/DTStack/molecule/commit/6df509afe652f12c552bddd141de91f7727133ff))
-   **theme:** compatible with vscode theme ([768abf2](https://github.com/DTStack/molecule/commit/768abf22c7de618abdc630a66bf54fd9bdc8d75a))
-   **theme:** compatible with vscode theme ([4839c84](https://github.com/DTStack/molecule/commit/4839c842b50b35cabb4a790ce1dccc2f481ecf09))
-   add basic Checkbox component ([550d04a](https://github.com/DTStack/molecule/commit/550d04ac3aadc0ef994649cf07e19b714e3e7e0f))
-   add basic Input and TextArea component ([e00d81e](https://github.com/DTStack/molecule/commit/e00d81e78308604c66377d2365e8430939d9af65))
-   add basic Select component ([027535d](https://github.com/DTStack/molecule/commit/027535dc69ac2f648a682ce9e8e851a5a9e50537))
-   add basic statusBar ([4c9b91c](https://github.com/DTStack/molecule/commit/4c9b91c4a13113ce17c120b3271e351463d8c782))
-   add draft dialog component ([02aa023](https://github.com/DTStack/molecule/commit/02aa0234baa06ea19b2eac78fe7d58982f15619b))
-   add draft inputbox component ([6fd365e](https://github.com/DTStack/molecule/commit/6fd365ebe754b84e90e287f48c0f4fb0535a1506))
-   add getAttr function ([10199b8](https://github.com/DTStack/molecule/commit/10199b8c24ac5f76866bd1ea0915b76fe122a51b))
-   add keyCodes.ts ([5b968d1](https://github.com/DTStack/molecule/commit/5b968d1b867ac0e50a5cb072cf76d0a8d0423709))
-   add serviceTab logic ([bbc1973](https://github.com/DTStack/molecule/commit/bbc197330547ec291bd971b901d6fd36c0bbbb6b))
-   conetxtMenu add ([d3f334a](https://github.com/DTStack/molecule/commit/d3f334ad66355a4178733c26fac36a39e36fb4e8))
-   delete debugger ([2493cd7](https://github.com/DTStack/molecule/commit/2493cd772b6c92429519d948f5a8184faf7aec8a))
-   delete service add ([293d0ee](https://github.com/DTStack/molecule/commit/293d0ee17767f6bc6818f0537ea62ac6d9d1415d))
-   delete unless code ([be4091d](https://github.com/DTStack/molecule/commit/be4091df8c179028b79cdecb8c0d80a7dce15df4))
-   eliminate omit.js NPM package ([baac047](https://github.com/DTStack/molecule/commit/baac047e247b556d141f8d32a03154d07744b907))
-   enable afterClose callback ([939f16e](https://github.com/DTStack/molecule/commit/939f16e70058c492d736a58a7b8decb04581260d))
-   extend the ClassNames interface ([9fdc1d9](https://github.com/DTStack/molecule/commit/9fdc1d93cdceade55723d64822f0746caa06f1cb))
-   extract colorValue to theme ([dee0a71](https://github.com/DTStack/molecule/commit/dee0a715f02a26a446bcc0af589aa3c3f1ed7956))
-   extract css to theme ([966c0ae](https://github.com/DTStack/molecule/commit/966c0aea8257d3d1c3020e11e74006574a1960f0))
-   extract data to service ([c74adaf](https://github.com/DTStack/molecule/commit/c74adafeef730ad1196420eebc57b74d270389da))
-   extract overside logic ([1f089fb](https://github.com/DTStack/molecule/commit/1f089fb527f4fa28defd2750193abc87b001decd))
-   extract the ClassName out of the function; Prevent duplicate execution ([056ef35](https://github.com/DTStack/molecule/commit/056ef3562db74e806b3b6c5640362391914c24bc))
-   finish dialog interface ([8642a90](https://github.com/DTStack/molecule/commit/8642a90913f394f9596f5742670c7e73aa0be3f9))
-   finish input interface and UI ([a680aa4](https://github.com/DTStack/molecule/commit/a680aa41d5a825a2fb554863184243fe5704463c))
-   finish TextArea interface and UI and Stories ([052b16f](https://github.com/DTStack/molecule/commit/052b16f530095e66afbb527fbaeab77546e2957c))
-   intergate typings ([a7be36f](https://github.com/DTStack/molecule/commit/a7be36f3d09cae9c4adf2034a7ed7f9e0407fc9b))
-   lock rc package version ([c8a7999](https://github.com/DTStack/molecule/commit/c8a79992408b72053e5d19f46b71a5bb1e71fd7b))
-   open file ([ffa5708](https://github.com/DTStack/molecule/commit/ffa57087049831af9fb069e9d423674d35865a51))
-   optimize actionButton interface ([aca2fbb](https://github.com/DTStack/molecule/commit/aca2fbb1e74eae82996054981437010f46b197f1))
-   optimize code ([f97c818](https://github.com/DTStack/molecule/commit/f97c818456840c580a0157960ad68de8ce7e7eba))
-   optimize code ([2ddcbb8](https://github.com/DTStack/molecule/commit/2ddcbb83d55e79ed82d6f0dd5671bf11eed571a9))
-   optimize default action ([36919df](https://github.com/DTStack/molecule/commit/36919dfaf9874c267edf72aee211b80b2862dcb6))
-   optimize moduleName ([eb68f89](https://github.com/DTStack/molecule/commit/eb68f895033e2152e592bf81329cb5c436d9d4dc))
-   optmize type interface ([f299d4e](https://github.com/DTStack/molecule/commit/f299d4e0375e04c4613a78a0ed5eab8bf66d8b50))
-   process empty logic ([ce3afd4](https://github.com/DTStack/molecule/commit/ce3afd4e460ae54a305812c8d64ce059a70275be))
-   refactor closeTab logic ([70f85e4](https://github.com/DTStack/molecule/commit/70f85e49b53c150e4c69428570ba5c1d9643c33e))
-   refactor scss ([af491c4](https://github.com/DTStack/molecule/commit/af491c41b3af227b9cceec4d89a0a32a420b1a6d))
-   refactor service interface ([78f3eba](https://github.com/DTStack/molecule/commit/78f3eba8f85556f14a4fab79f4f6fd2a871b8d81))
-   relation to issue [#10](https://github.com/DTStack/molecule/issues/10) ([6e77c1f](https://github.com/DTStack/molecule/commit/6e77c1f04f78b667d31e19bbfd34250f49df2673))
-   remove noUsed functionDefine ([4591cf5](https://github.com/DTStack/molecule/commit/4591cf52cb99ff87144b961062758def52c6e12b))
-   remove unless code ([ec9a643](https://github.com/DTStack/molecule/commit/ec9a6434cc16863305e35dd16738fe1151b5bd33))
-   remove unless code ([af80ee3](https://github.com/DTStack/molecule/commit/af80ee3af4eb7cc34d9acc1d8b2c70add8a06fdf))
-   resolve closeTab backfill ([32acfd5](https://github.com/DTStack/molecule/commit/32acfd581b0f0f76c3af97a8ecbb0f2741155f25))
-   resolve conflicts ([9c38fee](https://github.com/DTStack/molecule/commit/9c38fee5bc83d2636cbc9f152973752c1967c954))
-   resolve conflicts ([2347396](https://github.com/DTStack/molecule/commit/2347396ba4de8f32764128a6535d5e1722f6097b))
-   resolve emitEvent params missing ([fa829a7](https://github.com/DTStack/molecule/commit/fa829a78506a0a595210fa01267fbfa20cd24eaf))
-   some service add ([93bd10e](https://github.com/DTStack/molecule/commit/93bd10efbb326b2d112ae5c473655a07e5c051b8))
-   stories optimzie ([c017b7c](https://github.com/DTStack/molecule/commit/c017b7cd3de047be398feb7cb09e0f9bf501d76a))
-   sync code ([2406e72](https://github.com/DTStack/molecule/commit/2406e72d8f3afc695419b12f256adb74046aef95))
-   sync code ([34263ca](https://github.com/DTStack/molecule/commit/34263ca6f9533a2d76d3c292afab4a553f37f1f3))
-   sync Code ([e46213f](https://github.com/DTStack/molecule/commit/e46213f5c55ec815ebc9dbfccb5a3733eda0fcfe))
-   sync dev code ([e81554d](https://github.com/DTStack/molecule/commit/e81554dddb0b2b7557f5cad7612d554a04df15aa))
-   test editor logic ([6141663](https://github.com/DTStack/molecule/commit/61416631223c7b4283812e169cdb00707bbe9ee5))
-   use resetProps ([d6200a9](https://github.com/DTStack/molecule/commit/d6200a976e5c153a28d4ab6730807707c263b699))
-   **classname:** add BEM wrapper functions ([cd12f91](https://github.com/DTStack/molecule/commit/cd12f91e577f207212d6a1d010d521e8b2316770))
-   **contextview:** add more contextview demo ([868e190](https://github.com/DTStack/molecule/commit/868e190867f8e40c5ce206d6b9074b9ae268c047))
-   **contextview:** add onHide, dispose function, and shadowOutline option ([9f527f5](https://github.com/DTStack/molecule/commit/9f527f550b19c6958af9df98d6977673391ca7f8))
-   **eventemitter:** add unsubscribe function ([697c431](https://github.com/DTStack/molecule/commit/697c431ee9e3bede35d965dc7bbd2efeee6e0b32))
-   add Button component ([daa88ee](https://github.com/DTStack/molecule/commit/daa88ee1f47e9aba2262c197097afbabfe95c111))
-   add Button component ([9cb82b7](https://github.com/DTStack/molecule/commit/9cb82b7d1d82859bb926a4d61fa3d8bc233d8542))
-   add draft scrollabe ([d77b6b0](https://github.com/DTStack/molecule/commit/d77b6b0afb878d64357df1be059b171a45448852))
-   add draft tabs stories ([78c25f9](https://github.com/DTStack/molecule/commit/78c25f9ea38495fa779468282d6ec909ace9b16a))
-   add dropDown menu to menuBar ([607a46f](https://github.com/DTStack/molecule/commit/607a46f4b6b5ab30fa066ff405f07aa0f4eed648))
-   add em2Px function ([c9e291e](https://github.com/DTStack/molecule/commit/c9e291e12b47650dd7ac082c016f1a4a1e995054))
-   add findParentByClassName, getEventPosition, triggerEvent, TriggerEvent ([7121416](https://github.com/DTStack/molecule/commit/71214169c7936e07b68f2ed9ad51b5754bca8084))
-   add Icon component ([d342606](https://github.com/DTStack/molecule/commit/d3426067ee81a94bb762994dfdc9bab335057ad9))
-   add Icon component ([bff6978](https://github.com/DTStack/molecule/commit/bff697885ead0a10bb8ba6b3f5b32f95e1574f24))
-   add Scrollable component ([8691459](https://github.com/DTStack/molecule/commit/869145917e3de5b098dcf6cd60c4c528d9cd52ba))
-   add second version of Tab component ([6aabcf6](https://github.com/DTStack/molecule/commit/6aabcf685d4fe8831b19c86e36aaad0770e126cb))
-   adjust filePath ([a839ef7](https://github.com/DTStack/molecule/commit/a839ef7e8bb99e8e9faea885192a792f7dc729af))
-   delete scrollble component ([656ec89](https://github.com/DTStack/molecule/commit/656ec89efa7b34d9c1645be4e12c538486d4a28b))
-   delete unless file ([4b3573e](https://github.com/DTStack/molecule/commit/4b3573e8d85f64c5a05a4c3860238e471ee087c8))
-   extract color styles to theme.scss ([6dd927f](https://github.com/DTStack/molecule/commit/6dd927fa34ca3ec943d6b0f8cf65f96f6a5db7ac))
-   getPositionByPlacement ([18c4b75](https://github.com/DTStack/molecule/commit/18c4b75c312584b4c10ced8c83325487e58a211c))
-   optimize code ([aa772a4](https://github.com/DTStack/molecule/commit/aa772a415079e2320a7a00026e025a0aba2dc9e5))
-   remove dists ([6aea7ef](https://github.com/DTStack/molecule/commit/6aea7ef687a810ab3cf3407d55ef8870caedf42f))
-   remove fileIcon component and debugger webpack plugin ([52c089f](https://github.com/DTStack/molecule/commit/52c089ff92c707800d2debb11e0220e22c4c3fe4))
-   remove unused NPM packages ([ecd97ec](https://github.com/DTStack/molecule/commit/ecd97ecd3b4c46e2f3c8ef54fff83cde75a91376))
-   resolve conflicts ([a99d131](https://github.com/DTStack/molecule/commit/a99d131923e210993b2ddef9c99b7f4a89304473))
-   sync sourceCode ([304e31a](https://github.com/DTStack/molecule/commit/304e31a582199f33c3d0439766054f9f5d5afcd2))
-   update actionBar icon ([1c8def7](https://github.com/DTStack/molecule/commit/1c8def7cfa5ce8cb0a7efa90e55608970ba2dfd2))
-   **contextview:** attach mac css class and default append view to workbench ([40f70ea](https://github.com/DTStack/molecule/commit/40f70ea0b29a87bd661519d4aeb86ad3b2b21a2a))
-   **contextview:** attach mac css class and default append view to workbench ([4bd9b7e](https://github.com/DTStack/molecule/commit/4bd9b7ea32004dc4fac49e4693ee2e150e8d8cc7))
-   **dropdown:** add basic component and stories ([f00df97](https://github.com/DTStack/molecule/commit/f00df97f45d4f4bab3a94a860aa4d3d8a829ad2b))
-   **dropdown:** add basic component and stories ([5391d05](https://github.com/DTStack/molecule/commit/5391d05a94e45986fdf69b34203fd98b82120bfb))
-   **dropdown:** add basic component and stories ([77941be](https://github.com/DTStack/molecule/commit/77941be836efc75350bcb42a87b8c51c9496798e))
-   **dropdown:** support the overlay display by different placement ([47dadcd](https://github.com/DTStack/molecule/commit/47dadcdc19fa53afcc0d5c790f6e7d885ba0eaf7))
-   **gitlab:** add mr, issue template ([447b3a1](https://github.com/DTStack/molecule/commit/447b3a1403d0975802b5ec4da53d256187097b1c))
-   **global:** add favicon ([675f803](https://github.com/DTStack/molecule/commit/675f803f5ae787cd8f23eb6b25195d10b72587f9))
-   **global:** add favicon ([9db841a](https://github.com/DTStack/molecule/commit/9db841a99fb4b09d20e79d3cd6f27dbbac041e12))
-   **icon:** add icon component based on codicons ([2f6a071](https://github.com/DTStack/molecule/commit/2f6a071c46408e622e7b5bd00df24deb34438f9d))
-   **icon:** add icon component based on codicons ([664247c](https://github.com/DTStack/molecule/commit/664247c748a098a1cd01cd9cb9eee4b344df4486))
-   **id.ts:** add const ID_APP ([4f2b567](https://github.com/DTStack/molecule/commit/4f2b567a1d53f52830187442e3e1434dc627989b))
-   **id.ts:** add const ID_APP ([c80b2b7](https://github.com/DTStack/molecule/commit/c80b2b746458fdd731763be4bc2c16e4eff8fbae))
-   **list:** add basic List component ([8c3a691](https://github.com/DTStack/molecule/commit/8c3a691bf861ac5c5095d4de105bea1297c6a913))
-   **list:** add basic List component ([6cd3531](https://github.com/DTStack/molecule/commit/6cd35316f0ff72b2b6c578d940a40dc6bcf9b35e))
-   **list:** initial list component ([0c52a1c](https://github.com/DTStack/molecule/commit/0c52a1cdc19c1f3ef2ff17cd2597e6417bfbf2ce))
-   **list:** initial list component ([77d2c43](https://github.com/DTStack/molecule/commit/77d2c432371613ec4001d8ce2424c3669569217c))
-   **react:** add cloneReactChildren function ([3c38959](https://github.com/DTStack/molecule/commit/3c38959b9d9081e6076debe73af86ac55d562af2))
-   add contextView and actionBar examples ([8c5bece](https://github.com/DTStack/molecule/commit/8c5becec4bd5dc0d1e957ff2d467efea8b2574cb))
-   add em2Px function ([58c8e3f](https://github.com/DTStack/molecule/commit/58c8e3fc2be4ed8ecf4ee940b5234dff2e239505))
-   add findParentByClassName, getEventPosition, triggerEvent, TriggerEvent ([c42afcf](https://github.com/DTStack/molecule/commit/c42afcfe0ec82fdcb26a5f18ea5707a3ecb04a73))
-   compoent ui opti ([e120d46](https://github.com/DTStack/molecule/commit/e120d468b4f970fb4fefb00f85a5288dcb683b3d))
-   sync code ([7f5dd53](https://github.com/DTStack/molecule/commit/7f5dd5373fc93c041f9dcb6ff171e55379c64803))
-   test fileIcon render ([4bd0725](https://github.com/DTStack/molecule/commit/4bd072516ab199b781279728888e7963406670cd))
-   test scriptFile icons ([4232028](https://github.com/DTStack/molecule/commit/4232028d91d212838a213b774323946d4d1e72d5))
-   **menu:** add menu component ([afab6dc](https://github.com/DTStack/molecule/commit/afab6dcbb5c2402c60e970fa67ab6a2893b9367c))
-   **menu:** add menu component ([1fe6831](https://github.com/DTStack/molecule/commit/1fe68310a6c3201e085052819d475ee51a8b6636))
-   **react:** add cloneReactChildren function ([699f96a](https://github.com/DTStack/molecule/commit/699f96ad4bd36b06948cfed9b0d4e1c93c4072a7))
-   **typings:** declare HTMLElementProps ([a32c78c](https://github.com/DTStack/molecule/commit/a32c78c7eacd780de59c4133cdd398a1ad37c659))
-   **typings:** declare HTMLElementProps ([3aed642](https://github.com/DTStack/molecule/commit/3aed6420a2dbf8fd14ba7c61962b25cede9a2689))
-   **utils:** add mergeFunctions ([4c09efa](https://github.com/DTStack/molecule/commit/4c09efa4c7a679d11da434a15d24a4dcb9e83806))
-   activityBar selected ([5de680f](https://github.com/DTStack/molecule/commit/5de680f416de0fe19a523d7d4d3dbce1ba6e411b))
-   add actionBar and samples ([83e5947](https://github.com/DTStack/molecule/commit/83e59472543412034d66969755474d26c23ddb63))
-   add classNames common function ([56886e8](https://github.com/DTStack/molecule/commit/56886e8463d72311389bc48154b677e574a0a601))
-   add common animation.scss ([278e71a](https://github.com/DTStack/molecule/commit/278e71a5b7459a8390933d1c62027231d07ed956))
-   add contextMenu component ([606f73c](https://github.com/DTStack/molecule/commit/606f73cb32a2291f76b802b2f8b2e752e368f442))
-   add contextMenu for activityBar ([6648524](https://github.com/DTStack/molecule/commit/6648524d7e6998a488d01b3b5beb1e90319cda95))
-   add dom helper module ([bb728c4](https://github.com/DTStack/molecule/commit/bb728c495765732ffcebc67f06dd17526169264c))
-   add draft contextMenu component ([7ad4483](https://github.com/DTStack/molecule/commit/7ad448396bc375baf26ebb2ebec85483fc3f5c5a))
-   add draft contextView component ([4ae6678](https://github.com/DTStack/molecule/commit/4ae6678925f9dd1a4038ee1ef73092bb2efa266c))
-   add first version of tabComponent ([f05c506](https://github.com/DTStack/molecule/commit/f05c506ad52cdfe5eb9a511a3918debbc2467c2c))
-   add mapping state service ([65d20b2](https://github.com/DTStack/molecule/commit/65d20b25079e63db251dc902a5c799b927955eee))
-   add Menu component ([df20a80](https://github.com/DTStack/molecule/commit/df20a80e2f4bf2da6bc0c89020a4b68221f7f616))
-   **component:** collapse init ([9b10669](https://github.com/DTStack/molecule/commit/9b106699fbc6fdc613566b9f5cc1694d931a8321))
-   add editorGroup model ([06a4a22](https://github.com/DTStack/molecule/commit/06a4a223c73febcba40dc86112cfddc1004c2537))
-   add global items in actiitybar ([cd31e15](https://github.com/DTStack/molecule/commit/cd31e151b5b27d511d9ad2e0c2da890b0c49eb30))
-   add logger ([c31031b](https://github.com/DTStack/molecule/commit/c31031b12c513d0646284babd39e6b43474cee60))
-   add menuBarProvider ([77f5fcd](https://github.com/DTStack/molecule/commit/77f5fcd2d75be757137643bd8795ba787c9d6ad2))
-   add menuBarService ([600ba8e](https://github.com/DTStack/molecule/commit/600ba8edca9d2f3349b80ee3c30e9a7c119ce484))
-   add model layer and extract view interfaces to model file ([85eed7e](https://github.com/DTStack/molecule/commit/85eed7edc35ed5e9aae0b7ae76d65f2ae4c947e8))
-   add observable decorator ([e54aa51](https://github.com/DTStack/molecule/commit/e54aa510d69fd1a36bc7cdd7b7183abd05628ab1))
-   add onClose event ([7574845](https://github.com/DTStack/molecule/commit/7574845f9bf394ef1789aa2b904e7bd840c2d6eb))
-   add propsTable component ([8a60f66](https://github.com/DTStack/molecule/commit/8a60f66d376eb453fdbade7f4bbd68647cf47914))
-   add servcie declare ([17a237c](https://github.com/DTStack/molecule/commit/17a237c488062996541e39915f1ee8195290d250))
-   add sidebar, theme layout and others core modules ([2196711](https://github.com/DTStack/molecule/commit/2196711d0a8f8cdd5ed5d0c2b973e7da5a3f3c0a))
-   add singleton decorator ([b676e55](https://github.com/DTStack/molecule/commit/b676e55a802aa6a9af4c560ae8ae5a4bc9932937))
-   add the main entry file for molecule ([63064c9](https://github.com/DTStack/molecule/commit/63064c96bef2e5b533e4dc69eb38feea2a8f89fb))
-   add the ui extension ([0e23b74](https://github.com/DTStack/molecule/commit/0e23b748acc9a194af84bcc6fea0f783e2464ad3))
-   add ToolBar component ([0c35c3b](https://github.com/DTStack/molecule/commit/0c35c3be31b893f06f065899b266b647f67faf2b))
-   add welcome page ([d49731e](https://github.com/DTStack/molecule/commit/d49731e8114e82f8d2f1bfcc40813d3713b084f8))
-   common stories style ([0171801](https://github.com/DTStack/molecule/commit/0171801d84ad69768e044b90e613d2bed272400c))
-   construct the workbench struct ([2bbf4a5](https://github.com/DTStack/molecule/commit/2bbf4a5bd9ee6dce96a130beea598bc6ea639dd0))
-   declare molecule service ([9cbec33](https://github.com/DTStack/molecule/commit/9cbec339213d860e4722160e7965c9148b6c395e))
-   declare service interface ([6ccd70c](https://github.com/DTStack/molecule/commit/6ccd70c1a51f731f4ac406f850db342d7012c8a7))
-   declare ui interface ([513ce9c](https://github.com/DTStack/molecule/commit/513ce9c818dba8b970f606330e467bf00bb84bbe))
-   encapsulate monaco-editor as react component ([2452d49](https://github.com/DTStack/molecule/commit/2452d49417faa71147362cc31fc64f8c98618329))
-   extract common ids ([fdfa1df](https://github.com/DTStack/molecule/commit/fdfa1df01ec81965d61b163670df45b6d4beb0d1))
-   extract GlobalEvent abstract class ([20c5dbe](https://github.com/DTStack/molecule/commit/20c5dbe4fb37ce901c845fa9e37df2da7c6a4752))
-   extract react service, and re render by observable model ([4b9582a](https://github.com/DTStack/molecule/commit/4b9582a2398832968f4e7e5d6057a77cac2e9c63))
-   global import animation.css ([33103ac](https://github.com/DTStack/molecule/commit/33103ac27df1f6796738b304115c061f00ffe871))
-   init common definitions for molecule ([3a4316f](https://github.com/DTStack/molecule/commit/3a4316feea40de36f1502dc50020a8cc36200b01))
-   init the activity bar of workbench ([558843e](https://github.com/DTStack/molecule/commit/558843e0bacd1eeb4dbcc30e684b7c34d57c1cb6))
-   init the editor of workbench ([6f3dbf3](https://github.com/DTStack/molecule/commit/6f3dbf34f4425328b76864af1967ba376fb2e38d))
-   init the menu bar of workbench ([db03bc5](https://github.com/DTStack/molecule/commit/db03bc52ddb8b51e819755525c0ab01202781835))
-   init the panel of workbench ([97eccfe](https://github.com/DTStack/molecule/commit/97eccfee2d2a6211997b894ec2d4c401f9f264ce))
-   init the settings of workbench ([2ff9c9e](https://github.com/DTStack/molecule/commit/2ff9c9e8d3581496620adb4d720e3de01625a005))
-   init the sidebar of workbench ([403ef36](https://github.com/DTStack/molecule/commit/403ef36bccada54835d913d7d6d1f84c9a4b5897))
-   init the status bar of workbench ([abcae7e](https://github.com/DTStack/molecule/commit/abcae7e1ca7b2f623d810f2968150320acc541d4))
-   init workbench ([7446677](https://github.com/DTStack/molecule/commit/74466774f9d6faa8e0daf7f88c4e6052ec2e9525))
-   logger moleculeCtx, its convenient for development ([25db6cb](https://github.com/DTStack/molecule/commit/25db6cbfe18a8518ab18db36a2e127f2928ad105))
-   only push item ([1363592](https://github.com/DTStack/molecule/commit/13635926c3bb33568cb3fa2c0c1f6703aef986bf))
-   optimize code ([bd657b9](https://github.com/DTStack/molecule/commit/bd657b9eb68ec9080b970bf59b5690627155d635))
-   provide abstract class baseService ([5590dca](https://github.com/DTStack/molecule/commit/5590dca71ec48d89a974218091a6cca9f205dc08))
-   provide the eventService class for event management ([d5708b5](https://github.com/DTStack/molecule/commit/d5708b5fc037d4ad547d05f896fc428b2d9e5099))
-   **utils:** add cloneInstance function ([03f66a5](https://github.com/DTStack/molecule/commit/03f66a594871324634121b433f6b916d1cf502eb))
-   some default extensions ([bbc891e](https://github.com/DTStack/molecule/commit/bbc891e7fa3b8e740ca76b77e5c545261c7caa1c))
-   styled editor breadcrumbs ([ca96140](https://github.com/DTStack/molecule/commit/ca96140337e948a95ca6f20ef6fcafa71e6d3e08))
-   support multiple subscribe for the same event ([84ce57a](https://github.com/DTStack/molecule/commit/84ce57a27b6b5bc20831b43fd6eff4f887d7d2fe))
-   the parameter of prefixClaName supports Symbol ([8b258de](https://github.com/DTStack/molecule/commit/8b258deab138ec680b646706a6347af488769aed))
-   tree ui add ([b7d37a9](https://github.com/DTStack/molecule/commit/b7d37a90cb1ce9ab3aa5441dae9fe5074a663c9b))

### Bug Fixes

-   editor.executeEdits that pass in the desired end selection for the editor on which you invoke ([#137](https://github.com/DTStack/molecule/issues/137)) ([a2d3d27](https://github.com/DTStack/molecule/commit/a2d3d2737f98d97c8f21ebbf50b38460e66358ea))
-   remove folderEvent from editor controller ([4015f58](https://github.com/DTStack/molecule/commit/4015f5811a27a816c6a48845afc447eeff719d4e))
-   **actionbar:** merge onClick event ([f5c24b3](https://github.com/DTStack/molecule/commit/f5c24b3a1e6ff0981616dee0118de9ea4152db60))
-   **dialog:** remove the style.scss import ([0a420fb](https://github.com/DTStack/molecule/commit/0a420fbb19270ca9a9539d383a86f174bbb05013))
-   **menu:** provides onClick API ([f325ac4](https://github.com/DTStack/molecule/commit/f325ac4561a1cc53daf58a85ee2bc9f2ba05c2ee)), closes [#87](https://github.com/DTStack/molecule/issues/87)
-   **output:** disable the minimap and fix output can't refresh problem ([#124](https://github.com/DTStack/molecule/issues/124)) ([5a9dba2](https://github.com/DTStack/molecule/commit/5a9dba2d4e10b9979bde901edf5a8e225a8ec703)), closes [#123](https://github.com/DTStack/molecule/issues/123)
-   **stylelint:** ignore esm and umd folder ([6a175af](https://github.com/DTStack/molecule/commit/6a175af735702e3d87f0b8c44b7b257af3386fac))
-   **theme:** improve the colorTheme detail ([cee788d](https://github.com/DTStack/molecule/commit/cee788d1905e0294afd450b56ef60877300d0e10)), closes [#20](https://github.com/DTStack/molecule/issues/20)
-   **toolbar:** remove the toolbar style.scss import ([58c8884](https://github.com/DTStack/molecule/commit/58c88843a3c007a3a648e000b76cd93ff9c7f9b6)), closes [#131](https://github.com/DTStack/molecule/issues/131)
-   add lib gitignore ([6d97e5a](https://github.com/DTStack/molecule/commit/6d97e5a653c1a77350d95bcae8bf94a7e0c04578))
-   alter the type of Connector props and status to any ([53fc63e](https://github.com/DTStack/molecule/commit/53fc63e5987d964a2d85feb5df4a6da4c0a0c0f0))
-   change file name ([9cb0760](https://github.com/DTStack/molecule/commit/9cb0760d9469e6226691f4023ab099311cf519f9))
-   check-types fix ([09a57d6](https://github.com/DTStack/molecule/commit/09a57d657feb1d125120dfe8fc2e58209cb9c747))
-   code format premitter ([de3d81a](https://github.com/DTStack/molecule/commit/de3d81a87d42d11915bd668261a5abaca9710674))
-   currentTab of the currentGroup is selected by default when add EditorGroup ([1353943](https://github.com/DTStack/molecule/commit/135394389ec19b05d52d8cdc8484afe6db11abb4))
-   delete some test code ([05098f3](https://github.com/DTStack/molecule/commit/05098f34827c2ee3aede0bb09dff118c37bc090a))
-   fix ci problems ([d413fb5](https://github.com/DTStack/molecule/commit/d413fb5dd2287b8d2fab751b6e57d5e7d66ecaea))
-   fix ci type validate error ([ce77de5](https://github.com/DTStack/molecule/commit/ce77de5a44cf580abeb0b07d09fc6b46af7487a4))
-   fix cycle file, extract connect to controller ([#126](https://github.com/DTStack/molecule/issues/126)) ([69bbb75](https://github.com/DTStack/molecule/commit/69bbb753afc3210306b849c68eb458af78ac5d1c))
-   fix fileType ([5d76b32](https://github.com/DTStack/molecule/commit/5d76b32d9934082da597b704b12daf74cf59244a))
-   fix function params ([cd6fa5b](https://github.com/DTStack/molecule/commit/cd6fa5ba60cb6a83c9e54e96fe4d68b5ab224ab4))
-   fix removeFolder exec error and delete modal style ([#81](https://github.com/DTStack/molecule/issues/81)) ([c664353](https://github.com/DTStack/molecule/commit/c6643536a276d09f3300048fab6b456595d0a296))
-   format code prettier ([ec63d9b](https://github.com/DTStack/molecule/commit/ec63d9bcf1aa360a9132055e03061d59535b7e1a))
-   manual pass Controller to View ([03791e7](https://github.com/DTStack/molecule/commit/03791e752585459c9add25cb63349c59796b459f))
-   mock editor.create method ([abe61d6](https://github.com/DTStack/molecule/commit/abe61d6427ff9bed7f902e009320ba2f529b94f7))
-   premitter code format ([0259a15](https://github.com/DTStack/molecule/commit/0259a15f4405635dcde3534c2df6c5ad19de939c))
-   prettier code format ([ea609ea](https://github.com/DTStack/molecule/commit/ea609eae6bd26406bee71ab5684fa3a29c8433dd))
-   prettier format ([ed3065d](https://github.com/DTStack/molecule/commit/ed3065de2fba0e295cfec067e3cd355a63fdff44))
-   prettier format code ([4b14844](https://github.com/DTStack/molecule/commit/4b14844b5fcf4b441260ca807f5d3b3d1073ef39))
-   prettier format fix ([1a67fff](https://github.com/DTStack/molecule/commit/1a67fff547816c846f0aa8ce8647c90ad2a74f4c))
-   remove useless package-lock.json ([2f391f1](https://github.com/DTStack/molecule/commit/2f391f15c53013771bb569842ea6c6c3fe778d2e))
-   rename data backfill ([0b0742d](https://github.com/DTStack/molecule/commit/0b0742d73d5656b4d661de77e8a0b2ba4e5f5a57))
-   ts types error fix ([e2fb056](https://github.com/DTStack/molecule/commit/e2fb056b8f378437b37ce5f44711c66f5ceb8ec2))
-   **ci:** fix ci error ([6c80a4f](https://github.com/DTStack/molecule/commit/6c80a4f867c2568ddd4617da1871d93dd6e5896c))
-   **logger:** expand the arguments of function ([63642ea](https://github.com/DTStack/molecule/commit/63642ea74cb2757337234311a9674ff56ea0144e))
-   activitybar items overflow ([21f8f7a](https://github.com/DTStack/molecule/commit/21f8f7a6e25bf82de9513ef0fd0cd7e2d3fc0590))
-   add default generic type ([4fc75cf](https://github.com/DTStack/molecule/commit/4fc75cfc9f8e14c37a8cc03f78d128c8944b4322))
-   code format ([adea9b0](https://github.com/DTStack/molecule/commit/adea9b0df72679c48bd1c49431de8f08a537f349))
-   contextmenu trigger fix ([8432c46](https://github.com/DTStack/molecule/commit/8432c46e12b433a9183aaed7cea772be04db1d78))
-   del mock data ([c27d346](https://github.com/DTStack/molecule/commit/c27d3464f9ed8d51fa8ca978f8a735df2f156045))
-   del npmrc file ([454f8c6](https://github.com/DTStack/molecule/commit/454f8c638d8e25e367233f9d25ac128171401bbb))
-   delcare ActivityBarEvent in model ([dded331](https://github.com/DTStack/molecule/commit/dded33133818800cd8606fc365349180636b61b8))
-   derive the arguments of callback function ([bccae6e](https://github.com/DTStack/molecule/commit/bccae6e46ced96f566e48a0c5ae909b4d2d61cb0))
-   derive the arguments of callback function ([cdf8388](https://github.com/DTStack/molecule/commit/cdf83889bb4d386e9f510568b763ce9f11c718fb))
-   disable no-invalid-this eslint rule ([a841f8b](https://github.com/DTStack/molecule/commit/a841f8b7a4896d09ce9cc0bf75148c0b41a1b60d))
-   disable no-invalid-this eslint rule ([86f536e](https://github.com/DTStack/molecule/commit/86f536e8fab836feff9f1f03a39d59320710b305))
-   export \* from menuItem ([2b76400](https://github.com/DTStack/molecule/commit/2b764004f4cb9d5ad1b0b375800a7a5f0029aea1))
-   fix focus ([3816eb5](https://github.com/DTStack/molecule/commit/3816eb5386682120246b18169c9bb27a49ec1f9d))
-   ts error ([456168c](https://github.com/DTStack/molecule/commit/456168c0c6d657498c581b2089c7c7507bf6bf59))
-   **event emitter:** extend the event origianl function arguments ([30c6f51](https://github.com/DTStack/molecule/commit/30c6f5176be2d4edd46af8fa32ad8a8870100af8))
-   **eventbus:** expand the arguments of emit ([94020b8](https://github.com/DTStack/molecule/commit/94020b88ecb8ce991052f6e72da4bef6785f9c03))
-   **logger:** expand the arguments of function ([6732faa](https://github.com/DTStack/molecule/commit/6732faaa3fe44635ec1f641ce7db50edadc0ddc8))
-   **output:** disable the editor minimap ([5330ece](https://github.com/DTStack/molecule/commit/5330eced3f5ef937726ca8b664d7da9948607642)), closes [#62](https://github.com/DTStack/molecule/issues/62)
-   **slipt-pane:** import Pane module directly ([e6b7b70](https://github.com/DTStack/molecule/commit/e6b7b70c85f4d6224ff5ad8cc6d9e68c8faacb35))
-   **tabs:** add dragAndDrop HOC ([e94acc1](https://github.com/DTStack/molecule/commit/e94acc17a793a1ce4d3dbc26b6f16b6a36997051))
-   **ts:** typing errors ([2b1d115](https://github.com/DTStack/molecule/commit/2b1d115d05c26e7339e012198d4d0ee42c74a432))
-   **workbench:** cache the workbench SplitPane position ([#76](https://github.com/DTStack/molecule/issues/76)) ([2af86cd](https://github.com/DTStack/molecule/commit/2af86cdecef955b3713fad43f373852698d8e6b9)), closes [#60](https://github.com/DTStack/molecule/issues/60)
-   add key prop for each subMenu ([f36f09e](https://github.com/DTStack/molecule/commit/f36f09ecca13686bd643cae316bdddc270ae0ee7))
-   fix content ([b79bd40](https://github.com/DTStack/molecule/commit/b79bd402646bfdf84de2641e81d46e94a82fc2f9))
-   fix lint ([b67a74a](https://github.com/DTStack/molecule/commit/b67a74aa4c2f0cc43d867dc0868e22e7c4d9f4f2))
-   fix replace classNames use local method ([2110081](https://github.com/DTStack/molecule/commit/211008192e4513623172aff67c5d6b7fb76f7f8f))
-   fix validate ([6b7887e](https://github.com/DTStack/molecule/commit/6b7887e87d705c82d34420542e43df6ac10c63c7))
-   fix yarnrc and some todo ([8658641](https://github.com/DTStack/molecule/commit/86586410eac1beff091c56113cf36407758f3667))
-   merge tabs and collapse ([b5eb235](https://github.com/DTStack/molecule/commit/b5eb2352fa4dd793b66b48540c4dcb5789e3bb0b))
-   merge tabs and collapse ([e479848](https://github.com/DTStack/molecule/commit/e479848770241ea6b78a5ad4bd197029787d1720))
-   merge tabs and collapse ([c3e1451](https://github.com/DTStack/molecule/commit/c3e1451117ac896e3fd559725cda4efb9f98c334))
-   rebase dev ([ca91fce](https://github.com/DTStack/molecule/commit/ca91fcedf78ff5732778f9dddf50aa0b37bd5de7))
-   rebase dev ([f6099e9](https://github.com/DTStack/molecule/commit/f6099e91891ba8f6b68e35b30881a9fe11fc8ca7))
-   remove illegal charactor for json file ([e8bdb74](https://github.com/DTStack/molecule/commit/e8bdb747e39a0b879c1cc4128cd5f1144e1c8c00))
-   remove the Symbol parameter type ([db86ef2](https://github.com/DTStack/molecule/commit/db86ef23cc06b1d794100ecd098c125523ca8923))
-   remove useless option and fix the Select value logic ([f3154f1](https://github.com/DTStack/molecule/commit/f3154f1c9227af32cce6de29805cb83a815de36a))
-   stylelint ([6d2dbeb](https://github.com/DTStack/molecule/commit/6d2dbebdebf5f0862a502d271ab7835b5993ee7f))
-   typing error ([ebca2f0](https://github.com/DTStack/molecule/commit/ebca2f05808f1b967069267e99b19d419f6eacc1))
-   typing error ([a144947](https://github.com/DTStack/molecule/commit/a144947dbdd49f9e831211da5e0bce4fd9a9afdd))
-   **conflict:** merge Button and List ([7c6df2a](https://github.com/DTStack/molecule/commit/7c6df2a40660d89b4204d55228b737c3f6377180))
-   **stylelint:** prettify and ignore markdownlint extension ([23a9966](https://github.com/DTStack/molecule/commit/23a9966479c6028517056d53394b02e6ba30a8e8))
-   sync code ([a13551e](https://github.com/DTStack/molecule/commit/a13551e7848659c980cd6e6b3fd9749cb235630a))
-   typing error ([8891b52](https://github.com/DTStack/molecule/commit/8891b525f16b736de81cf0d92544f6ff225ba86d))
-   **theme:** mixin-pattern error ([1f82d95](https://github.com/DTStack/molecule/commit/1f82d95521689651124ffb7085253ec7117b3058))
-   unexpect none as border-top value ([1eff91a](https://github.com/DTStack/molecule/commit/****************************************))
-   **sync:** fix conflict ([ad7bd67](https://github.com/DTStack/molecule/commit/ad7bd670b8752dbc139790ea73038030ff7d558f))
