@import '../../classNames/style.scss';

$tab: prefix('tab');
$extra: BEMElement($tab, 'extra');
$name: BEMElement($tab, 'name');
$active: BEMModifier($tab, 'active');
$dragging: BEMModifier($tab, 'dragging');
$extraActive: BEMModifier($extra, 'active');

.#{$tab} {
    background: var(--tab-inactiveBackground);
    border-right: 1px solid var(--tab-border);
    box-sizing: border-box;
    color: var(--tab-inactiveForeground);
    cursor: pointer;
    padding: 0 10px;
    position: relative;
    user-select: none;
    font-size: 12px;
    height: 100%;
    min-width: fit-content;

    &.#{$dragging} {
        background-color: var(--editorGroup-dropBackground) !important;
        outline: none !important;

        > * {
            pointer-events: none;
        }
    }

    &.#{$active} {
        background: var(--tab-activeBackground);
        border-bottom-color: var(--tab-activeBorder);
        color: var(--tab-activeForeground);

        .#{$extra} {
            opacity: 1;
        }
    }

    &:hover {
        .#{$extra} {
            opacity: 1;
        }
    }

    .#{$name} {
        max-width: 300px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.#{$extra} {
    opacity: 0;

    &.#{$extraActive} {
        opacity: 1;
    }
}

:export {
    tab: $tab;
    extra: $extra;
    name: $name;
    active: $active;
    dragging: $dragging;
    extraActive: $extraActive;
}
