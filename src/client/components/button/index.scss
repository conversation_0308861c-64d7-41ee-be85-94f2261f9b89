@import '../../classNames/style.scss';

$container: prefix('btn');
$normal: BEMModifier($container, 'normal');
$large: BEMModifier($container, 'large');
$disabled: BEMModifier($container, 'disabled');
$block: BEMModifier($container, 'block');

.#{$container} {
    background-color: var(--button-background);
    border: 1px solid var(--contrastBorder);
    box-sizing: border-box;
    color: var(--button-foreground);
    line-height: 18px;
    outline-offset: 1px;
    padding: 5px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
    border-radius: 2px;

    &:hover {
        background-color: var(--button-hoverBackground);
        opacity: 0.9;
    }

    &.#{$block} {
        display: block;
        width: 100%;
    }

    &.#{$large} {
        height: 34px;
        line-height: 34px;
        font-size: 18px;
        padding: 0px 14px;
    }

    &.#{$disabled} {
        cursor: not-allowed;
        opacity: 0.4;
        pointer-events: none;
    }
}

:export {
    container: $container;
    normal: $normal;
    large: $large;
    disabled: $disabled;
    block: $block;
}