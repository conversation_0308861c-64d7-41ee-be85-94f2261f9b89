@import '../../classNames/style.scss';

$scrollBar: prefix('scrollBar');
$container: BEMElement($scrollBar, 'container');
$viewport: BEMElement($scrollBar, 'viewport');
$shadow: BEMElement($scrollBar, 'shadow');
$track: BEMElement($scrollBar, 'track');
$thumb: BEMElement($scrollBar, 'thumb');

$horizontal: BEMModifier($container, 'horizontal');
$vertical: BEMModifier($container, 'vertical');

$trackHidden: BEMModifier($track, 'hidden');

.#{$container} {
    height: 100%;
    overflow: hidden;
    position: relative;
    width: 100%;
}

.#{$viewport} {
    overflow: auto;
    width: 100%;
    height: 100%;
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

.#{$track} {
    position: absolute;
    background-color: transparent;
    z-index: 99;
    user-select: none;
    opacity: 1;
    transition: opacity 0.1s linear;

    &.#{$vertical} {
        width: 10px;
        top: 0;
        bottom: 0;
        right: 0;
    }

    &.#{$horizontal} {
        height: 10px;
        left: 0;
        right: 0;
        bottom: 0;
    }

    &.#{$trackHidden} {
        pointer-events: none;
        opacity: 0;
        transition-delay: 0.8s;
    }
}

.#{$thumb} {
    background-color: var(--scrollbarSlider-background);
    height: var(--radix-thumb-vertical, 100%);
    position: absolute;
    width: var(--radix-thumb-horizontal, 100%);

    &:hover {
        background-color: var(--scrollbarSlider-hoverBackground);
    }
}

.#{$shadow} {
    position: absolute;
    &.vertical {
        width: 100%;
        top: 0;
        height: 3px;
        box-shadow: var(--widget-shadow) 0 6px 6px -6px inset;
    }

    &.horizontal {
        width: 3px;
        left: 0;
        height: 100%;
        box-shadow: var(--widget-shadow) 6px 0px 6px -6px inset;
    }
}

:export {
    container: $container;
    viewport: $viewport;
    shadow: $shadow;
    track: $track;
    thumb: $thumb;
    horizontal: $horizontal;
    vertical: $vertical;
    trackHidden: $trackHidden;
}
