@import '../../classNames/style.scss';

$menu: prefix('menu');
$container: BEMElement($menu, 'container');
$subMenu: BEMElement($menu, 'subMenu');
$item: BEMElement($menu, 'item');
$label: BEMElement($menu, 'label');
$divider: BEMElement($menu, 'divider');
$icon: BEMElement($menu, 'icon');
$keybinding: BEMElement($menu, 'keybinding');
$keybindingItem: BEMElement($menu, 'keybindingItem');
$indicator: BEMElement($menu, 'indicator');

$disabled: BEMModifier($item, 'disabled');

.#{$container} {
    background-color: var(--menu-background);
    outline: 1px solid var(--menu-border);
    box-shadow: 0 2px 8px var(--widget-shadow);
    border-radius: 5px;
    color: var(--menu-foreground);
    font-size: 13px;
    min-width: 160px;
    max-height: 841px;
    overflow: scroll;
    margin: 0;
    padding: 4px 0;
    outline-offset: -1px;

    &:focus {
        // override global *[tabIndex=0]'s outline style
        outline: 1px solid var(--menu-border) !important;
    }

    .#{$item} {
        list-style: none;
        height: 2em;
        display: flex;
        align-items: center;
        margin: 0 4px;
        border-radius: 4px;
        user-select: none;

        &:not(.#{$disabled}) {
            cursor: pointer;

            &:hover {
                background-color: var(--menu-selectionBackground);
                color: var(--menu-selectionForeground);
                outline: solid 1px var(--menu-selectionBorder);
                outline-offset: -1px;
            }
        }

        &.#{$disabled} {
            color: var(--disabledForeground);
        }
    }

    .#{$divider} {
        background-color: var(--menu-separatorBackground);
        height: 1px;
        width: 100%;
        margin: 4px 0;
        pointer-events: none;
    }

    .#{$label} {
        flex: 1;
        line-height: normal;
        white-space: nowrap;
    }

    .#{$icon} {
        font-size: 12px;
        line-height: 12px;
        width: 2em;
        text-align: center;
        flex: 0 1 2em;
    }

    .#{$keybinding} {
        flex: 2 1 auto;
        text-align: right;
        margin: 0 16px;
        display: flex;
        justify-content: end;
        gap: 2px;
    }

    .#{$indicator} {
        text-align: right;
        font-size: 12px;
        line-height: 12px;
    }

    > ul {
        margin: 0;
        padding: 0;
    }
}

.#{$subMenu} {
    > .rc-menu-submenu-title:focus {
        // override global *[tabIndex=0]'s outline style
        outline: none;
        outline-offset: 0;
    }

    &.rc-menu-submenu-active .#{$item}:not(.#{$disabled}) {
        background-color: var(--menu-selectionBackground);
        color: var(--menu-selectionForeground);
        outline: solid 1px var(--menu-selectionBorder);
        outline-offset: -1px;
    }
}

.rc-menu-submenu-popup {
    position: absolute;
    z-index: 9999;
}

.rc-menu-submenu-hidden {
    display: none;
}

:export {
    container: $container;
    subMenu: $subMenu;
    item: $item;
    divider: $divider;
    label: $label;
    icon: $icon;
    keybinding: $keybinding;
    keybindingItem: $keybindingItem;
    indicator: $indicator;
    disabled: $disabled;
}
