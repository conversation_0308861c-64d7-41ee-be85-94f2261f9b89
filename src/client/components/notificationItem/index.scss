@import '../../classNames/style.scss';

$notification: prefix('notification');
$item: BEMElement($notification, 'item');

.#{$item} {
    padding: 10px 5px;
    text-align: left;
    font-size: 12px;
    transition: background-color 0.3s;
    border-radius: 5px;
    line-height: 22px;
    width: 450px;
    box-sizing: border-box;
    background-color: var(--notifications-background);

    &:hover {
        background-color: var(--list-hoverBackground);
    }
}

:export {
    item: $item;
}
