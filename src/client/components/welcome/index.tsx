import variables from './index.scss';

function Logo({ className }: { className: string }) {
    return (
        <span style={{ fontSize: 0 }}>
            <svg width="1em" height="1em" viewBox="0 0 120 120" className={className}>
                <g fill="none" fillRule="evenodd" stroke="none" strokeWidth="1" opacity="0.48">
                    <g fill="#A2A2A2" transform="translate(-660 -152)">
                        <g transform="translate(660 152)">
                            <path d="M59.3 96.335c4.87 0 8.817 3.954 8.817 8.832S64.169 114 59.3 114c-4.87 0-8.818-3.955-8.818-8.833s3.948-8.832 8.818-8.832zm-22.444-6.826a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zm44.887 0a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zM59.3 72.245c4.87 0 8.817 3.955 8.817 8.833s-3.948 8.833-8.817 8.833c-4.87 0-8.818-3.955-8.818-8.833s3.948-8.833 8.818-8.833zm-42.483 0c4.87 0 8.817 3.955 8.817 8.833s-3.947 8.833-8.817 8.833S8 85.956 8 81.078s3.948-8.833 8.817-8.833zm85.366 0c4.87 0 8.817 3.955 8.817 8.833s-3.948 8.833-8.817 8.833c-4.87 0-8.817-3.955-8.817-8.833s3.947-8.833 8.817-8.833zm-65.327-6.022a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zm44.887 0a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zM59.3 48.156c4.87 0 8.817 3.955 8.817 8.833s-3.948 8.833-8.817 8.833c-4.87 0-8.818-3.955-8.818-8.833s3.948-8.833 8.818-8.833zm-42.483 0c4.87 0 8.817 3.955 8.817 8.833s-3.947 8.833-8.817 8.833S8 61.867 8 56.989s3.948-8.833 8.817-8.833zm85.366 0c4.87 0 8.817 3.955 8.817 8.833s-3.948 8.833-8.817 8.833c-4.87 0-8.817-3.955-8.817-8.833s3.947-8.833 8.817-8.833zM80.942 35.31c4.87 0 8.817 3.954 8.817 8.832s-3.948 8.833-8.817 8.833c-4.87 0-8.817-3.955-8.817-8.833s3.947-8.832 8.817-8.832zm-43.284 0c4.87 0 8.817 3.954 8.817 8.832s-3.948 8.833-8.817 8.833c-4.87 0-8.818-3.955-8.818-8.833s3.948-8.832 8.818-8.832zm64.525-11.242c4.87 0 8.817 3.954 8.817 8.833 0 4.878-3.948 8.832-8.817 8.832-4.87 0-8.817-3.954-8.817-8.832 0-4.879 3.947-8.833 8.817-8.833zm-85.366 0c4.87 0 8.817 3.954 8.817 8.833 0 4.878-3.947 8.832-8.817 8.832S8 37.778 8 32.9c0-4.879 3.948-8.833 8.817-8.833zm42.483 5.62a3.21 3.21 0 013.206 3.213 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.207-3.212 3.21 3.21 0 013.207-3.212zm-19.238-13.65a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zm38.475 0a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.206-3.212 3.21 3.21 0 013.206-3.212zM59.3 6a3.21 3.21 0 013.206 3.212 3.21 3.21 0 01-3.206 3.212 3.21 3.21 0 01-3.207-3.212A3.21 3.21 0 0159.3 6z"></path>
                        </g>
                    </g>
                </g>
            </svg>
        </span>
    );
}

export default function Welcome() {
    return (
        <div className={variables.container}>
            <Logo className={variables.logo} />
            <h1 className={variables.title}>Molecule</h1>
        </div>
    );
}
