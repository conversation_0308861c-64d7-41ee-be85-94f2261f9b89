@import '../../classNames/style.scss';

$container: prefix('welcome');
$logo: BEMElement($container, 'logo');
$title: BEMElement($container, 'title');

.#{$container} {
    background-color: var(--editor-background);
    display: flex;
    flex-flow: column wrap;
    align-items: center;
    justify-content: center;
    height: 100%;
    overflow: hidden;
    text-align: center;
    width: 100%;

    >* {
        width: 100%;
    }

    .#{$logo} {
        font-size: 120px;
        margin-top: 10vh;
    }

    .#{$title} {
        color: #a2a2a2;
        font-family: GEETYPE-TakaLineGB-Flash-Heavy, GEETYPE-TakaLineGB-Flash,
            sans-serif;
        font-weight: 800;
        height: 24px;
        letter-spacing: 2.667px;
        line-height: 24px;
        margin: 3vh auto 0;
        opacity: 0.48;
        user-select: none;
        width: 132px;
    }
}


:export {
    container: $container;
    logo: $logo;
    title: $title;
}