@import '../../classNames/style.scss';

$action: prefix('action');
$disabled: BEMModifier($action, 'disabled');

.#{$action} {
    box-sizing: border-box;
    cursor: pointer;
    padding: 3px;
    border-radius: 5px;
    margin: 0 1px;
    line-height: 0;

    &:not(.#{$disabled}):hover {
        background: var(--toolbar-hoverBackground);
        outline: 1px dashed var(--toolbar-hoverOutline);
        outline-offset: -1px;
    }


    &.#{$disabled} {
        cursor: not-allowed;
        opacity: 0.4;
    }
}

:export {
    action: $action;
    disabled: $disabled;
}