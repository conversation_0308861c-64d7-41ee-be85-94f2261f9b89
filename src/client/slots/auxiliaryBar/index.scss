@import '../../classNames/style.scss';

$auxiliaryBar: prefix('auxiliaryBar');
$container: #{$auxiliaryBar}__container;
$content: BEMElement($container, 'content');
$tabs: BEMElement($container, 'tabs');
$item: BEMElement($container, 'item');
$tab: BEMElement($container, 'tab');

$active: BEMModifier($item, 'active');


.#{$container} {
    height: 100%;
    width: 100%;
    background-color: var(--activityBar-background);
    border-left: 1px solid var(--activityBar-border);
    display: flex;

    .#{$content} {
        flex: 1;
        background-color: var(--sideBar-background);
    }
    .#{$tabs} {
        right: 0;
        margin: 0;
        padding: 0;
        width: 25px;
        background: var(--activityBar-background);
        border-left: 1px solid var(--activityBar-border);
    }

    .#{$tab} {
        border-bottom: 1px solid var(--sideBarSectionHeader-border);
        color: var(--tab-inactiveForeground);
        cursor: pointer;
        letter-spacing: 4px;
        list-style: none;
        padding: 4px;
        writing-mode: vertical-rl;
        font-size: 12px;
        user-select: none;

        &:hover,
        &.#{$active} {
            background-color: var(--menubar-selectionBackground);
        }
    }

    .#{$item} {
        color: var(--activityBar-inactiveForeground);
        cursor: pointer;
        display: flex;
        border-radius: 4px;

        .codicon {
            font-size: 18px;
        }

        &:hover,
        &.#{$active} {
            background-color: var(--menubar-selectionBackground);
        }

        &.#{$active} {
            color: var(--menu-selectionForeground);
            position: relative;

            &::after {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 2px;
                height: 100%;
                background-color: #3f87ff;
                content: "";
            }
        }
    }
}
:export {
    container: $container;
    content: $content;
    tabs: $tabs;
    item: $item;
    tab: $tab;
    active: $active;
}
