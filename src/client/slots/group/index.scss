@import '../../classNames/style.scss';

$editor: prefix('editor');
$group: BEMElement($editor, 'group');
$header: BEMElement($group, 'header');
$tab: BEMElement($header, 'tab');
$content: BEMElement($group, 'content');
$breadcrumb: BEMElement($group, 'breadcrumb');

// Used for autoScroll to active one
$active: BEMModifier($editor, 'active');

.#{$group} {
    height: 100%;
    position: relative;
    width: 100%;

    .#{$header} {
        background: var(--editorGroupHeader-tabsBackground);
        border-bottom: 1px solid var(--editorGroupHeader-tabsBorder);
        position: relative;
    }

    .#{$breadcrumb} {
        background-color: var(--editor-background);
        color: var(--editor-foreground);
        height: 22px;
        line-height: 22px;
        padding-left: 20px;
        font-size: 12px;
    }

    .#{$content} {
        height: calc(100% - 57px);
    }

    .#{$tab} {
        line-height: 35px;
    }
}

:export {
    group: $group;
    active: $active;
    header: $header;
    tab: $tab;
    breadcrumb: $breadcrumb;
    content: $content;
}
