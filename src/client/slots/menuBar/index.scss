@import '../../classNames/style.scss';

$menuBar: prefix('menuBar');
$container: BEMElement($menuBar, 'container');
$item: BEMElement($menuBar, 'item');
$name: BEMElement($menuBar, 'name');

$active: BEMModifier($item, 'active');

.#{$container} {
    background-color: var(--titleBar-activeBackground);
    color: var(--titleBar-activeForeground);
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--titleBar-border);
    padding: 0 8px;

    .#{$item} {
        font-size: 12px;
        display: inline-block;
        height: 35px;
        line-height: 35px;
        user-select: none;
    }

    .#{$name} {
        padding: 0.3em 0.7em;
        border-radius: 5px;
    }
}

.#{$active} .#{$name},
.#{$item}:hover .#{$name} {
    background-color: var(--menubar-selectionBackground);
    outline: 1px dashed var(--menubar-selectionBorder);
    outline-offset: -1px;
}

:export {
    container: $container;
    item: $item;
    name: $name;
    active: $active;
}
