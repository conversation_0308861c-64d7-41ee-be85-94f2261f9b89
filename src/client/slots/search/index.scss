@import '../../classNames/style.scss';

$container: prefix('search');
$widget: BEMElement($container, 'widget');
$result: BEMElement($container, 'result');
$tip: BEMElement($container, 'tip');

.#{$container} {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-size: 13px;
    padding: 6px 0;

    .#{$widget} {
        margin: 0 8px 5px;
    }

    .#{$result} {
        flex: 1;
        overflow: hidden;
    }

    .#{$tip} {
        margin: 0 8px 5px;
        overflow-wrap: break-word;
        cursor: default;
        color: var(--search-resultsInfoForeground);
    }
}

:export {
    container: $container;
    widget: $widget;
    result: $result;
    tip: $tip;
}
