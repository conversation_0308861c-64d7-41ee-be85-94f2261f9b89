@import '../../classNames/style.scss';

$activityBar: prefix('activityBar');
$container: BEMElement($activityBar, 'container');
$normal: BEMElement($activityBar, 'normal');


.#{$container} {
    background-color: var(--activityBar-background);
    border-right: 1px solid var(--activityBar-border);
    color: var(--activityBar-foreground);
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    position: relative;
    width: 48px;

    >ul {
        margin: 0;
        padding: 0;
    }

    .#{$normal} {
        margin-bottom: auto;

        ul {
            margin: 0;
            padding: 0;
        }
    }
}

:export {
    container: $container;
    normal: $normal;
}