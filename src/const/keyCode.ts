import { KeyCode } from 'mo/monaco';

export const KeyCodeString: Partial<{ [key in KeyCode]: string }> = {
    [KeyCode.Unknown]: '',
    [KeyCode.Backspace]: '⌫',
    [KeyCode.Tab]: '⇥',
    [KeyCode.Enter]: '↩',
    [KeyCode.PageUp]: '↑',
    [KeyCode.PageDown]: '↓',
    [KeyCode.Digit0]: '0',
    [KeyCode.Digit1]: '1',
    [KeyCode.Digit2]: '2',
    [KeyCode.Digit3]: '3',
    [KeyCode.Digit4]: '4',
    [KeyCode.Digit5]: '5',
    [KeyCode.Digit6]: '6',
    [KeyCode.Digit7]: '7',
    [KeyCode.Digit8]: '8',
    [KeyCode.Digit9]: '9',
    [KeyCode.KeyA]: 'A',
    [KeyCode.KeyB]: 'B',
    [KeyCode.KeyC]: 'C',
    [KeyCode.KeyD]: 'D',
    [KeyCode.KeyE]: 'E',
    [KeyCode.KeyF]: 'F',
    [KeyCode.KeyG]: 'G',
    [KeyCode.KeyH]: 'H',
    [KeyCode.KeyI]: 'I',
    [KeyCode.KeyJ]: 'J',
    [KeyCode.KeyK]: 'K',
    [KeyCode.KeyL]: 'L',
    [KeyCode.KeyM]: 'M',
    [KeyCode.KeyN]: 'N',
    [KeyCode.KeyO]: 'O',
    [KeyCode.KeyP]: 'P',
    [KeyCode.KeyQ]: 'Q',
    [KeyCode.KeyR]: 'R',
    [KeyCode.KeyS]: 'S',
    [KeyCode.KeyT]: 'T',
    [KeyCode.KeyU]: 'U',
    [KeyCode.KeyV]: 'V',
    [KeyCode.KeyW]: 'W',
    [KeyCode.KeyX]: 'X',
    [KeyCode.KeyY]: 'Y',
    [KeyCode.KeyZ]: 'Z',
    [KeyCode.Semicolon]: ';',
    [KeyCode.Equal]: '+',
    [KeyCode.Comma]: ',',
    [KeyCode.Minus]: '-',
    [KeyCode.Period]: '.',
    [KeyCode.Slash]: '/',
    [KeyCode.Backquote]: '~',
    [KeyCode.BracketLeft]: '[',
    [KeyCode.Backslash]: '\\',
    [KeyCode.BracketRight]: ']',
    [KeyCode.Quote]: '"',
    [KeyCode.RightArrow]: '→',
    [KeyCode.LeftArrow]: '←',
    [KeyCode.UpArrow]: '↑',
    [KeyCode.DownArrow]: '↓',
};
