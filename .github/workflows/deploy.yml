name: Deploy to GitHub Pages

on:
    push:
        branches: [main]
        paths: [website/**]

jobs:
    deploy:
        name: Deploy to GitHub Pages
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - uses: actions/setup-node@v2
              with:
                  node-version: 16.x
                  cache: yarn
                  cache-dependency-path: website/yarn.lock
            - name: Install dependencies
              run: yarn
            - name: Build website
              working-directory: website
              run: |
                  yarn install --frozen-lockfile
                  yarn build

            # Popular action to deploy to GitHub Pages:
            # Docs: https://github.com/peaceiris/actions-gh-pages#%EF%B8%8F-docusaurus
            - name: Deploy to GitHub Pages
              uses: peaceiris/actions-gh-pages@v3
              with:
                  github_token: ${{ secrets.GITHUB_TOKEN }}
                  # Build output to publish to the `gh-pages` branch:
                  publish_dir: website/build
                  # Assign commit authorship to the official GH-Actions bot for deploys to `gh-pages` branch:
                  # https://github.com/actions/checkout/issues/13#issuecomment-724415212
                  # The GH actions bot is used by default if you didn't specify the two fields.
                  # You can swap them out with your own user credentials.
                  user_name: github-actions[bot]
                  user_email: 41898282+github-actions[bot]@users.noreply.github.com
