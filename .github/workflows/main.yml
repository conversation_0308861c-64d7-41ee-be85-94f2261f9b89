# This is a basic workflow to help you get started with Actions

name: C<PERSON>

# Triggers the workflow on push or pull request events but only for the master branch
on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
    setup:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                node-version: [16.x]

        steps:
            - name: checkout
              uses: actions/checkout@v2

            - name: cache yarn.lock
              uses: actions/cache@v2
              with:
                  path: package-temp-dir
                  key: lock-${{ github.sha }}

            - name: create yarn.lock
              run: yarn generate-lock-entry

            - name: hack for single file
              run: |
                  if [ ! -d "package-temp-dir" ]; then
                    mkdir package-temp-dir
                  fi
                  cp yarn.lock package-temp-dir
            - name: cache node_modules
              id: node_modules_cache_id
              uses: actions/cache@v2
              with:
                  path: node_modules
                  key: node_modules-${{ hashFiles('**/package-temp-dir/yarn.lock') }}

            - name: install
              if: steps.node_modules_cache_id.outputs.cache-hit != 'true'
              run: yarn

    static-checking:
        needs: [setup]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2

            - name: Restore cache from yarn.lock
              uses: actions/cache@v2
              with:
                  path: package-temp-dir
                  key: lock-${{ github.sha }}

            - name: Restore cache from node_modules
              uses: actions/cache@v2
              with:
                  path: node_modules
                  key: node_modules-${{ hashFiles('**/package-temp-dir/yarn.lock') }}

            - name: Run Check Types
              run: yarn check-types
            - name: Run ESlint
              run: yarn eslint
            - name: Run Prettier
              run: yarn prettier --check
            - name: Run StyleLint
              run: yarn stylelint

    test:
        runs-on: ubuntu-latest
        needs: [setup]
        steps:
            - uses: actions/checkout@v2

            - name: Restore cache from yarn.lock
              uses: actions/cache@v2
              with:
                  path: package-temp-dir
                  key: lock-${{ github.sha }}

            - name: Restore cache from node_modules
              uses: actions/cache@v2
              with:
                  path: node_modules
                  key: node_modules-${{ hashFiles('**/package-temp-dir/yarn.lock') }}

            - name: Run Unit Test
              run: yarn test

            - name: Upload coverage to Codecov
              uses: codecov/codecov-action@v2
              with:
                  token: ${{ secrets.CODECOV_TOKEN }}
                  fail_ci_if_error: true
                  directory: ./coverage/
                  name: codecov-umbrella
                  flag: unittests, integration
                  verbose: true
    build:
        runs-on: ubuntu-latest
        needs: [setup, static-checking, test]
        steps:
            - uses: actions/checkout@v2

            - name: Restore cache from yarn.lock
              uses: actions/cache@v2
              with:
                  path: package-temp-dir
                  key: lock-${{ github.sha }}

            - name: Restore cache from node_modules
              uses: actions/cache@v2
              with:
                  path: node_modules
                  key: node_modules-${{ hashFiles('**/package-temp-dir/yarn.lock') }}

            - name: Run Build
              run: yarn build

            - name: Run Build Web
              run: yarn build-web
