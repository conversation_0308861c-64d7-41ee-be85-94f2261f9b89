name: Test GitHub Pages Deployment

on:
    pull_request:
        branches: [main]
        paths: [website/**]

jobs:
    test-deploy:
        name: Test deployment
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - uses: actions/setup-node@v2
              with:
                  node-version: 16.x
                  cache: yarn
                  cache-dependency-path: website/yarn.lock
            - name: Install dependencies
              run: yarn
            - name: Test build
              working-directory: website
              run: |
                  yarn install --frozen-lockfile
                  yarn build
